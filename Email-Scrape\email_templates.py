#!/usr/bin/env python3
"""
Email Template System
Handles email template creation, management, and personalization for mass campaigns
"""

import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import re
from jinja2 import Template, Environment, BaseLoader
from loguru import logger
import uuid

@dataclass
class EmailTemplate:
    """Email template data structure"""
    id: str
    name: str
    subject: str
    text_body: str
    html_body: str = ""
    variables: List[str] = None
    category: str = "general"
    description: str = ""
    created_at: str = ""
    updated_at: str = ""
    
    def __post_init__(self):
        if self.variables is None:
            self.variables = []
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        if not self.updated_at:
            self.updated_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EmailTemplate':
        return cls(**data)

class TemplateManager:
    """Manages email templates and personalization"""
    
    def __init__(self, templates_file: str = 'email_templates.json'):
        self.templates_file = templates_file
        self.templates: Dict[str, EmailTemplate] = {}
        self.jinja_env = Environment(loader=BaseLoader())
        self.load_templates()
        self._create_default_templates()
    
    def load_templates(self) -> None:
        """Load templates from file"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for template_data in data.get('templates', []):
                    template = EmailTemplate.from_dict(template_data)
                    self.templates[template.id] = template
                    
                logger.info(f"Loaded {len(self.templates)} email templates")
            else:
                logger.info("No templates file found, starting with empty templates")
        except Exception as e:
            logger.error(f"Error loading templates: {e}")
    
    def save_templates(self) -> None:
        """Save templates to file"""
        try:
            data = {
                'templates': [template.to_dict() for template in self.templates.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(self.templates)} email templates")
        except Exception as e:
            logger.error(f"Error saving templates: {e}")
    
    def _create_default_templates(self) -> None:
        """Create default templates if none exist"""
        if not self.templates:
            default_templates = [
                {
                    'name': 'Business Introduction',
                    'subject': 'Introduction from {{sender_name}} - {{company_name}}',
                    'text_body': '''Hello {{recipient_name}},

I hope this email finds you well. My name is {{sender_name}} and I'm reaching out from {{company_name}}.

{{message_body}}

I would love to connect and discuss how we might be able to help each other.

Best regards,
{{sender_name}}
{{sender_title}}
{{company_name}}
{{contact_info}}''',
                    'html_body': '''<html><body>
<p>Hello <strong>{{recipient_name}}</strong>,</p>

<p>I hope this email finds you well. My name is {{sender_name}} and I'm reaching out from {{company_name}}.</p>

<p>{{message_body}}</p>

<p>I would love to connect and discuss how we might be able to help each other.</p>

<p>Best regards,<br>
{{sender_name}}<br>
{{sender_title}}<br>
{{company_name}}<br>
{{contact_info}}</p>
</body></html>''',
                    'category': 'business',
                    'description': 'Professional business introduction template'
                },
                {
                    'name': 'Product Announcement',
                    'subject': 'Exciting News: {{product_name}} is Here!',
                    'text_body': '''Hi {{recipient_name}},

We're thrilled to announce the launch of {{product_name}}!

{{product_description}}

Key benefits:
{{benefits_list}}

Special launch offer: {{special_offer}}

Learn more: {{website_url}}

Best regards,
The {{company_name}} Team''',
                    'html_body': '''<html><body>
<h2>Hi {{recipient_name}},</h2>

<p>We're thrilled to announce the launch of <strong>{{product_name}}</strong>!</p>

<p>{{product_description}}</p>

<h3>Key benefits:</h3>
<div>{{benefits_list}}</div>

<p><strong>Special launch offer:</strong> {{special_offer}}</p>

<p><a href="{{website_url}}">Learn more</a></p>

<p>Best regards,<br>
The {{company_name}} Team</p>
</body></html>''',
                    'category': 'marketing',
                    'description': 'Product announcement and promotion template'
                },
                {
                    'name': 'Follow-up Email',
                    'subject': 'Following up on our {{interaction_type}}',
                    'text_body': '''Hello {{recipient_name}},

I wanted to follow up on our {{interaction_type}} {{interaction_date}}.

{{follow_up_message}}

{{next_steps}}

Please let me know if you have any questions or if there's anything I can help you with.

Best regards,
{{sender_name}}''',
                    'html_body': '''<html><body>
<p>Hello {{recipient_name}},</p>

<p>I wanted to follow up on our {{interaction_type}} {{interaction_date}}.</p>

<p>{{follow_up_message}}</p>

<p>{{next_steps}}</p>

<p>Please let me know if you have any questions or if there's anything I can help you with.</p>

<p>Best regards,<br>
{{sender_name}}</p>
</body></html>''',
                    'category': 'follow-up',
                    'description': 'General follow-up email template'
                },
                {
                    'name': 'Newsletter',
                    'subject': '{{newsletter_title}} - {{month}} {{year}}',
                    'text_body': '''{{newsletter_title}}
{{month}} {{year}} Edition

{{intro_message}}

In this issue:
{{content_summary}}

{{main_content}}

Upcoming Events:
{{upcoming_events}}

Thank you for reading!

The {{company_name}} Team
Unsubscribe: {{unsubscribe_link}}''',
                    'html_body': '''<html><body>
<h1>{{newsletter_title}}</h1>
<h2>{{month}} {{year}} Edition</h2>

<p>{{intro_message}}</p>

<h3>In this issue:</h3>
<div>{{content_summary}}</div>

<div>{{main_content}}</div>

<h3>Upcoming Events:</h3>
<div>{{upcoming_events}}</div>

<p>Thank you for reading!</p>

<p>The {{company_name}} Team<br>
<a href="{{unsubscribe_link}}">Unsubscribe</a></p>
</body></html>''',
                    'category': 'newsletter',
                    'description': 'Monthly newsletter template'
                }
            ]
            
            for template_data in default_templates:
                self.create_template(**template_data)
            
            logger.info(f"Created {len(default_templates)} default templates")
    
    def create_template(self, name: str, subject: str, text_body: str, 
                       html_body: str = "", category: str = "general", 
                       description: str = "") -> str:
        """Create a new email template"""
        try:
            template_id = str(uuid.uuid4())
            
            # Extract variables from template
            variables = self._extract_variables(subject + " " + text_body + " " + html_body)
            
            template = EmailTemplate(
                id=template_id,
                name=name,
                subject=subject,
                text_body=text_body,
                html_body=html_body,
                variables=variables,
                category=category,
                description=description
            )
            
            self.templates[template_id] = template
            self.save_templates()
            
            logger.info(f"Created template '{name}' with ID {template_id}")
            return template_id
            
        except Exception as e:
            logger.error(f"Error creating template: {e}")
            return ""
    
    def update_template(self, template_id: str, **kwargs) -> bool:
        """Update an existing template"""
        try:
            if template_id not in self.templates:
                logger.error(f"Template {template_id} not found")
                return False
            
            template = self.templates[template_id]
            
            # Update fields
            for key, value in kwargs.items():
                if hasattr(template, key):
                    setattr(template, key, value)
            
            # Update variables if content changed
            if 'subject' in kwargs or 'text_body' in kwargs or 'html_body' in kwargs:
                template.variables = self._extract_variables(
                    template.subject + " " + template.text_body + " " + template.html_body
                )
            
            template.updated_at = datetime.now().isoformat()
            self.save_templates()
            
            logger.info(f"Updated template {template_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating template: {e}")
            return False
    
    def delete_template(self, template_id: str) -> bool:
        """Delete a template"""
        try:
            if template_id in self.templates:
                del self.templates[template_id]
                self.save_templates()
                logger.info(f"Deleted template {template_id}")
                return True
            else:
                logger.error(f"Template {template_id} not found")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting template: {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[EmailTemplate]:
        """Get a template by ID"""
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[EmailTemplate]:
        """Get templates by category"""
        return [template for template in self.templates.values() 
                if template.category == category]
    
    def get_all_templates(self) -> List[EmailTemplate]:
        """Get all templates"""
        return list(self.templates.values())
    
    def get_template_list(self) -> List[Dict]:
        """Get simplified template list for UI"""
        return [{
            'id': template.id,
            'name': template.name,
            'category': template.category,
            'description': template.description,
            'variables': template.variables,
            'created_at': template.created_at
        } for template in self.templates.values()]
    
    def _extract_variables(self, text: str) -> List[str]:
        """Extract template variables from text"""
        # Find all {{variable}} patterns
        pattern = r'\{\{\s*([^}]+)\s*\}\}'
        variables = re.findall(pattern, text)
        
        # Clean and deduplicate
        variables = [var.strip() for var in variables]
        return list(set(variables))
    
    def render_template(self, template_id: str, variables: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """Render template with provided variables"""
        try:
            template = self.get_template(template_id)
            if not template:
                logger.error(f"Template {template_id} not found")
                return None
            
            # Render subject
            subject_template = self.jinja_env.from_string(template.subject)
            rendered_subject = subject_template.render(**variables)
            
            # Render text body
            text_template = self.jinja_env.from_string(template.text_body)
            rendered_text = text_template.render(**variables)
            
            # Render HTML body if available
            rendered_html = ""
            if template.html_body:
                html_template = self.jinja_env.from_string(template.html_body)
                rendered_html = html_template.render(**variables)
            
            return {
                'subject': rendered_subject,
                'text_body': rendered_text,
                'html_body': rendered_html
            }
            
        except Exception as e:
            logger.error(f"Error rendering template: {e}")
            return None
    
    def validate_template(self, template_id: str, variables: Dict[str, Any]) -> Dict[str, Any]:
        """Validate template rendering with provided variables"""
        try:
            template = self.get_template(template_id)
            if not template:
                return {'valid': False, 'error': 'Template not found'}
            
            # Check if all required variables are provided
            missing_vars = [var for var in template.variables if var not in variables]
            if missing_vars:
                return {
                    'valid': False, 
                    'error': f'Missing variables: {", ".join(missing_vars)}',
                    'missing_variables': missing_vars
                }
            
            # Try to render
            rendered = self.render_template(template_id, variables)
            if rendered:
                return {'valid': True, 'rendered': rendered}
            else:
                return {'valid': False, 'error': 'Rendering failed'}
                
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def get_categories(self) -> List[str]:
        """Get all template categories"""
        categories = set(template.category for template in self.templates.values())
        return sorted(list(categories))
    
    def duplicate_template(self, template_id: str, new_name: str) -> Optional[str]:
        """Duplicate an existing template"""
        try:
            original = self.get_template(template_id)
            if not original:
                logger.error(f"Template {template_id} not found")
                return None
            
            new_id = self.create_template(
                name=new_name,
                subject=original.subject,
                text_body=original.text_body,
                html_body=original.html_body,
                category=original.category,
                description=f"Copy of {original.description}"
            )
            
            logger.info(f"Duplicated template {template_id} as {new_id}")
            return new_id
            
        except Exception as e:
            logger.error(f"Error duplicating template: {e}")
            return None

# Example usage and testing
if __name__ == "__main__":
    # Initialize template manager
    template_manager = TemplateManager()
    
    # Example: Create a custom template
    # template_id = template_manager.create_template(
    #     name="Custom Welcome",
    #     subject="Welcome to {{company_name}}, {{recipient_name}}!",
    #     text_body="Hello {{recipient_name}}, welcome to our service!",
    #     category="welcome"
    # )
    
    # Example: Render template
    # variables = {
    #     'company_name': 'My Company',
    #     'recipient_name': 'John Doe'
    # }
    # rendered = template_manager.render_template(template_id, variables)
    # print(rendered)
    
    print("Email Template System loaded successfully")
    print(f"Available templates: {len(template_manager.get_all_templates())}")
    print(f"Categories: {template_manager.get_categories()}")