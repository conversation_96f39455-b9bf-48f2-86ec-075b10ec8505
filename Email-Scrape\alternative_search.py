#!/usr/bin/env python3
"""
Alternative search engines for when Google blocks requests
"""

import asyncio
from typing import List
from playwright.async_api import async_playwright
from loguru import logger
from email_scraper import Contact, EmailExtractor
from datetime import datetime

class AlternativeSearchEngines:
    """Alternative search engines when Google is blocked"""
    
    def __init__(self):
        self.browser = None
        self.extractor = EmailExtractor()
    
    async def init_browser(self):
        """Initialize browser"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(headless=True)
    
    async def create_page(self):
        """Create a stealth page"""
        if not self.browser:
            await self.init_browser()
        
        context = await self.browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        return page
    
    async def bing_search(self, query: str, max_pages: int = 2) -> List[Contact]:
        """Search using Bing"""
        contacts = []
        page = await self.create_page()
        
        try:
            for page_num in range(max_pages):
                search_url = f"https://www.bing.com/search?q={query}&first={page_num * 10}"
                logger.info(f"Bing search: {search_url}")
                
                await page.goto(search_url, timeout=30000)
                await page.wait_for_selector('#b_results', timeout=10000)
                
                # Extract links
                links = await page.evaluate("""
                    () => {
                        const results = [];
                        const elements = document.querySelectorAll('#b_results h2 a');
                        elements.forEach(el => {
                            if (el.href && !el.href.includes('bing.com')) {
                                results.push(el.href);
                            }
                        });
                        return results;
                    }
                """)
                
                logger.info(f"Found {len(links)} Bing results")
                
                # Scrape each link
                for link in links[:5]:
                    try:
                        await page.goto(link, timeout=15000)
                        content = await page.content()
                        emails = self.extractor.extract_emails_from_text(content)
                        
                        for email in emails:
                            contact = Contact(
                                email=email,
                                website=link,
                                source="Bing Search",
                                extracted_date=datetime.now().isoformat()
                            )
                            contacts.append(contact)
                    except Exception as e:
                        logger.warning(f"Failed to scrape {link}: {e}")
                        continue
                
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"Bing search failed: {e}")
        finally:
            await page.close()
        
        return contacts
    
    async def duckduckgo_search(self, query: str, max_pages: int = 2) -> List[Contact]:
        """Search using DuckDuckGo"""
        contacts = []
        page = await self.create_page()
        
        try:
            search_url = f"https://duckduckgo.com/?q={query}"
            logger.info(f"DuckDuckGo search: {search_url}")
            
            await page.goto(search_url, timeout=30000)
            await page.wait_for_selector('[data-testid="result"]', timeout=10000)
            
            # Extract links
            links = await page.evaluate("""
                () => {
                    const results = [];
                    const elements = document.querySelectorAll('[data-testid="result"] h2 a');
                    elements.forEach(el => {
                        if (el.href && !el.href.includes('duckduckgo.com')) {
                            results.push(el.href);
                        }
                    });
                    return results;
                }
            """)
            
            logger.info(f"Found {len(links)} DuckDuckGo results")
            
            # Scrape each link
            for link in links[:8]:
                try:
                    await page.goto(link, timeout=15000)
                    content = await page.content()
                    emails = self.extractor.extract_emails_from_text(content)
                    
                    for email in emails:
                        contact = Contact(
                            email=email,
                            website=link,
                            source="DuckDuckGo Search",
                            extracted_date=datetime.now().isoformat()
                        )
                        contacts.append(contact)
                except Exception as e:
                    logger.warning(f"Failed to scrape {link}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {e}")
        finally:
            await page.close()
        
        return contacts
    
    async def close(self):
        """Close browser"""
        if self.browser:
            await self.browser.close()

# Test function
async def test_alternative_search():
    """Test alternative search engines"""
    searcher = AlternativeSearchEngines()
    
    try:
        print("Testing Bing search...")
        bing_contacts = await searcher.bing_search("contact email software company", max_pages=1)
        print(f"Bing found {len(bing_contacts)} contacts")
        
        print("\nTesting DuckDuckGo search...")
        ddg_contacts = await searcher.duckduckgo_search("contact email software company", max_pages=1)
        print(f"DuckDuckGo found {len(ddg_contacts)} contacts")
        
        all_contacts = bing_contacts + ddg_contacts
        if all_contacts:
            print(f"\nTotal contacts found: {len(all_contacts)}")
            for i, contact in enumerate(all_contacts[:3]):
                print(f"{i+1}. {contact.email} from {contact.source}")
        
    finally:
        await searcher.close()

if __name__ == "__main__":
    asyncio.run(test_alternative_search())