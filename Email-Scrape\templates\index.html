{% extends "base.html" %}

{% block title %}Dashboard - Email Scraper Pro{% endblock %}

{% block content %}
<div class="row">
    <!-- Welcome Section -->
    <div class="col-12 mb-4">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="card-title mb-2">
                            <i class="fas fa-rocket me-2"></i>
                            Welcome to Email Scraper Pro
                        </h2>
                        <p class="card-text mb-0">
                            Your comprehensive solution for email discovery, validation, and mass campaigns.
                            Get started by scraping emails or managing your campaigns.
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <i class="fas fa-envelope-open-text fa-5x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Stats -->
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-primary mb-3">
                    <i class="fas fa-envelope fa-3x"></i>
                </div>
                <h4 class="card-title" id="total-emails">0</h4>
                <p class="card-text text-muted">Total Emails Scraped</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-success mb-3">
                    <i class="fas fa-paper-plane fa-3x"></i>
                </div>
                <h4 class="card-title" id="active-campaigns">0</h4>
                <p class="card-text text-muted">Active Campaigns</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-info mb-3">
                    <i class="fas fa-file-alt fa-3x"></i>
                </div>
                <h4 class="card-title" id="total-templates">0</h4>
                <p class="card-text text-muted">Email Templates</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card text-center h-100">
            <div class="card-body">
                <div class="text-warning mb-3">
                    <i class="fas fa-server fa-3x"></i>
                </div>
                <h4 class="card-title" id="smtp-configs">0</h4>
                <p class="card-text text-muted">SMTP Configurations</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('scraper') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-search me-2"></i>
                        Start Email Scraping
                    </a>
                    <a href="{{ url_for('campaigns') }}" class="btn btn-success btn-lg">
                        <i class="fas fa-plus me-2"></i>
                        Create New Campaign
                    </a>
                    <a href="{{ url_for('templates') }}" class="btn btn-info btn-lg">
                        <i class="fas fa-edit me-2"></i>
                        Manage Templates
                    </a>
                    <a href="/settings" class="btn btn-warning btn-lg">
                        <i class="fas fa-cog me-2"></i>
                        Configure SMTP
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-history fa-2x mb-3"></i>
                        <p>No recent activity</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- System Status -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heartbeat me-2"></i>
                    System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator bg-success me-3"></div>
                            <div>
                                <h6 class="mb-0">Web Server</h6>
                                <small class="text-muted">Running</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator bg-success me-3"></div>
                            <div>
                                <h6 class="mb-0">Email Scraper</h6>
                                <small class="text-muted">Ready</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator bg-success me-3"></div>
                            <div>
                                <h6 class="mb-0">Tracking Server</h6>
                                <small class="text-muted">Active</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Connection Status -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-wifi me-2"></i>
                    Connection
                </h5>
            </div>
            <div class="card-body text-center">
                <div id="connection-status">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Connecting...</span>
                    </div>
                    <p class="text-muted">Establishing connection...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Dashboard specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard statistics
    loadDashboardStats();
    
    // Setup real-time updates
    if (typeof socket !== 'undefined') {
        socket.on('connect', function() {
            updateConnectionStatus(true);
        });
        
        socket.on('disconnect', function() {
            updateConnectionStatus(false);
        });
    }
});

function loadDashboardStats() {
    // Load SMTP configurations count
    fetch('/api/smtp/configs')
        .then(response => response.json())
        .then(data => {
            document.getElementById('smtp-configs').textContent = data.length || 0;
        })
        .catch(error => console.error('Error loading SMTP configs:', error));
    
    // Load templates count
    fetch('/api/templates')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-templates').textContent = data.length || 0;
        })
        .catch(error => console.error('Error loading templates:', error));
    
    // Load campaigns count
    fetch('/api/campaigns')
        .then(response => response.json())
        .then(data => {
            const activeCampaigns = data.filter(campaign => campaign.status === 'running').length;
            document.getElementById('active-campaigns').textContent = activeCampaigns || 0;
        })
        .catch(error => console.error('Error loading campaigns:', error));
}

function updateConnectionStatus(connected) {
    const statusDiv = document.getElementById('connection-status');
    if (connected) {
        statusDiv.innerHTML = `
            <div class="text-success mb-3">
                <i class="fas fa-check-circle fa-2x"></i>
            </div>
            <p class="text-success mb-0">Connected</p>
        `;
    } else {
        statusDiv.innerHTML = `
            <div class="text-danger mb-3">
                <i class="fas fa-times-circle fa-2x"></i>
            </div>
            <p class="text-danger mb-0">Disconnected</p>
        `;
    }
}
</script>
{% endblock %}