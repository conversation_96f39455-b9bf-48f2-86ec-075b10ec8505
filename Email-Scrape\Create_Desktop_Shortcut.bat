@echo off
echo Creating desktop shortcut for BMD EMAIL SCRAPPER...

set "SCRIPT_DIR=%~dp0"
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT_NAME=BMD EMAIL SCRAPPER.lnk"
set "TARGET=%SCRIPT_DIR%BMD_EMAIL_SCRAPPER.bat"

:: Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\%SHORTCUT_NAME%" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%TARGET%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%SCRIPT_DIR%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "BMD EMAIL SCRAPPER - Bulk Email Generation Tool" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"

:: Execute VBS script
cscript "%TEMP%\CreateShortcut.vbs" //nologo

:: Clean up
del "%TEMP%\CreateShortcut.vbs"

echo.
echo Desktop shortcut created successfully!
echo You can now double-click "BMD EMAIL SCRAPPER" on your desktop to launch the application.
echo.
pause