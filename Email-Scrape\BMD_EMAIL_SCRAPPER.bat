@echo off
echo Starting BMD EMAIL SCRAPPER...
echo Please wait while the application loads...
cd /d "%~dp0"
python -c "import tkinter as tk; import bulk_gui; root = tk.Tk(); root.state('zoomed'); root.lift(); root.focus_force(); root.attributes('-topmost', True); root.after(1000, lambda: root.attributes('-topmost', False)); app = bulk_gui.BulkScraperGUI(root); root.mainloop()"
if %ERRORLEVEL% EQU 0 (
    echo Application closed normally.
) else (
    echo Application encountered an error. Error code: %ERRORLEVEL%
    pause
)
pause >nul