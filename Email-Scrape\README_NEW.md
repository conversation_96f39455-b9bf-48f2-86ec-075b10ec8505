# Email Scraper Pro - Advanced Lead Generation Tool

🚀 **A powerful Python-based email scraping system for generating leads from Google search, LinkedIn profiles, and websites using Playwright and advanced web scraping techniques.**

## 🌟 Features

### Core Functionality
- **Google Search Scraping**: Extract emails from Google search results based on keywords
- **LinkedIn Profile Scraping**: Gather contact information from LinkedIn profiles
- **Website Scraping**: Extract emails and contact details from any website
- **Multi-threaded Processing**: Efficient concurrent scraping for faster results
- **Proxy Rotation**: Automatic proxy rotation to avoid IP blocking and rate limiting
- **Smart Email Validation**: Advanced email validation and cleaning
- **Phone Number Extraction**: Extract and format phone numbers
- **Company Information**: Automatically detect company names and titles
- **Anti-Detection**: User agent rotation, stealth browser settings, and CAPTCHA detection

### User Interfaces
- **Modern GUI**: Beautiful CustomTkinter-based graphical interface
- **Command Line Interface**: Powerful CLI for batch processing and automation
- **Real-time Progress**: Live progress tracking and status updates
- **Export Options**: CSV, Excel, and JSON export formats

### Advanced Features
- **Proxy Support**: Rotate through multiple proxies for anonymity
- **User Agent Rotation**: Randomized user agents to avoid detection
- **Rate Limiting**: Built-in delays and rate limiting for responsible scraping
- **Data Deduplication**: Automatic removal of duplicate contacts
- **Database Storage**: SQLite database for persistent storage
- **Configuration Management**: Flexible configuration system
- **Comprehensive Logging**: Detailed logging with multiple levels

## 📦 Installation

### Prerequisites
- Python 3.8 or higher
- Windows, macOS, or Linux

### Quick Setup

1. **Clone or download the project**
   ```bash
   cd Email-Scrape
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Playwright browsers**
   ```bash
   python -m playwright install
   ```

4. **Test the installation**
   ```bash
   python test_scraper.py
   ```

## 🚀 Usage

### GUI Application

Launch the graphical interface:
```bash
python gui_app.py
```

**Features:**
- **Search Types**: Choose between Google Search, LinkedIn Profiles, or Website URLs
- **Query Input**: Enter search terms or URLs
- **Advanced Options**: Configure max pages, headless mode, and more
- **Real-time Results**: View extracted contacts in a table
- **Export Functions**: Save results to CSV or Excel
- **Activity Log**: Monitor scraping progress and status

### Command Line Interface

#### Google Search Scraping
```bash
# Single query
python cli_app.py google -q "marketing manager email" -p 5

# Multiple queries
python cli_app.py google -q "sales director contact" -q "CEO email" -p 3

# Export to file
python cli_app.py google -q "startup founder email" -o results.csv
```

#### LinkedIn Profile Scraping
```bash
# Single profile
python cli_app.py linkedin -u "https://linkedin.com/in/username"

# Multiple profiles
python cli_app.py linkedin -u "https://linkedin.com/in/user1" -u "https://linkedin.com/in/user2"
```

#### Website Scraping
```bash
# Single website
python cli_app.py websites -u "https://company.com"

# Multiple websites
python cli_app.py websites -u "https://company1.com" -u "https://company2.com"
```

#### Batch Processing
```bash
# From file (one query/URL per line)
python cli_app.py batch -f queries.txt -t google -p 5 -o results.xlsx
```

#### Export and Statistics
```bash
# Export all contacts
python cli_app.py export -f csv -o all_contacts.csv
python cli_app.py export -f excel -o all_contacts.xlsx

# View statistics
python cli_app.py stats

# Clear database
python cli_app.py clear
```

## ⚙️ Configuration

### Configuration File
The system uses `config.json` for settings. Key configurations:

```json
{
  "proxies": [
    {
      "host": "proxy1.example.com",
      "port": 8080,
      "username": "user",
      "password": "pass",
      "protocol": "http"
    }
  ],
  "scraping": {
    "headless": true,
    "page_load_timeout": 30000,
    "max_pages_per_query": 10,
    "max_results_per_page": 50,
    "min_delay": 1.0,
    "max_delay": 3.0,
    "max_retries": 3
  },
  "database": {
    "db_path": "email_scraper.db",
    "backup_enabled": true
  },
  "export": {
    "default_format": "csv",
    "include_duplicates": false
  }
}
```

### Proxy Configuration

The system supports multiple proxy types and automatic rotation:

#### Adding Proxies

**Method 1: Using Proxy Manager (Recommended)**
```bash
# Interactive proxy management
python proxy_manager.py

# Add proxy via command line
python proxy_manager.py --add

# Load proxies from file
python proxy_manager.py --load sample_proxies.txt

# Test all proxies
python proxy_manager.py --test
```

**Method 2: Manual Configuration**
Edit `sample_proxies.txt` with your proxy servers:
```
# HTTP Proxies
http://proxy1.example.com:8080
http://username:<EMAIL>:3128

# HTTPS Proxies
https://proxy3.example.com:8080

# SOCKS Proxies
socks5://proxy4.example.com:1080
socks5://user:<EMAIL>:1080
```

#### Supported Proxy Types
- **HTTP/HTTPS**: Standard web proxies
- **SOCKS4/SOCKS5**: Socket proxies for enhanced anonymity
- **Authenticated Proxies**: Username/password authentication
- **Residential Proxies**: For better success rates

#### Proxy Rotation Features
- **Automatic Rotation**: Switches proxies when blocked or rate-limited
- **Health Monitoring**: Automatically marks failed proxies
- **Round-Robin Selection**: Distributes requests across available proxies
- **Retry Logic**: Attempts multiple proxies before failing
- **User Agent Rotation**: Combines with proxy rotation for better anonymity

## 📊 Data Output

### Contact Information
Extracted contacts include:
- **Email Address**: Primary contact email
- **Name**: Person's full name (when available)
- **Company**: Company or organization name
- **Title**: Job title or position
- **Phone**: Phone number (formatted)
- **LinkedIn URL**: LinkedIn profile link
- **Website**: Source website URL
- **Source**: Scraping method used
- **Date**: Extraction timestamp

### Export Formats

#### CSV Export
```csv
email,name,company,title,phone,linkedin_url,website,source,extracted_date
<EMAIL>,John Doe,Company Inc,CEO,(*************,https://linkedin.com/in/johndoe,https://company.com,Google Search,2024-01-15 10:30:00
```

#### Excel Export
- Formatted spreadsheet with headers
- Auto-filtering enabled
- Multiple sheets for different sources

#### JSON Export
```json
[
  {
    "email": "<EMAIL>",
    "name": "John Doe",
    "company": "Company Inc",
    "title": "CEO",
    "phone": "(*************",
    "linkedin_url": "https://linkedin.com/in/johndoe",
    "website": "https://company.com",
    "source": "Google Search",
    "extracted_date": "2024-01-15 10:30:00"
  }
]
```

## 🛡️ Best Practices

### Responsible Scraping
- **Respect robots.txt**: Check website scraping policies
- **Rate Limiting**: Use built-in delays to avoid overwhelming servers
- **User Agent Rotation**: Randomize user agents to appear more natural
- **Proxy Rotation**: Use proxies to distribute requests
- **Legal Compliance**: Ensure compliance with local laws and regulations

### Performance Optimization
- **Headless Mode**: Use headless browsing for faster scraping
- **Concurrent Processing**: Leverage multi-threading for efficiency
- **Smart Filtering**: Use domain filters to focus on relevant results
- **Caching**: Database storage prevents re-scraping the same data

### Data Quality
- **Email Validation**: Built-in email format validation
- **Deduplication**: Automatic removal of duplicate contacts
- **Data Cleaning**: Text normalization and formatting
- **Source Tracking**: Track where each contact was found

## 🔧 Advanced Usage

### Custom Scripts
Create custom scraping scripts using the core modules:

```python
import asyncio
from email_scraper import WebScraper, DataManager

async def custom_scrape():
    scraper = WebScraper()
    data_manager = DataManager()
    
    await scraper.init_browser(headless=True)
    
    # Custom scraping logic
    urls = ['https://example.com']
    contacts = await scraper.website_scrape(urls)
    
    # Save to database
    saved_count = data_manager.save_contacts(contacts)
    print(f"Saved {saved_count} contacts")
    
    await scraper.close_browser()

# Run the custom scraper
asyncio.run(custom_scrape())
```

### Integration with Other Tools
- **CRM Integration**: Export data to popular CRM systems
- **Email Marketing**: Import contacts into email marketing platforms
- **Data Analysis**: Use pandas for advanced data analysis
- **API Integration**: Build REST APIs around the scraping functionality

## 📁 Project Structure

```
Email-Scrape/
├── email_scraper.py         # Core scraping engine with proxy rotation
├── gui_app.py              # GUI application
├── cli_app.py              # CLI application
├── config.py               # Configuration and proxy management
├── utils.py                # Utility functions
├── proxy_manager.py        # Proxy management utility
├── demo_proxy_rotation.py  # Proxy rotation demonstration
├── test_proxy_rotation.py  # Proxy rotation testing
├── sample_proxies.txt      # Sample proxy configuration
├── requirements.txt        # Dependencies
├── test_scraper.py         # Test suite
├── README_NEW.md           # Documentation
├── config.json             # Configuration file
├── email_scraper.db        # SQLite database
└── email_scraper.log       # Log file
```

## 🐛 Troubleshooting

### Common Issues

1. **Playwright Installation**
   ```bash
   # If browsers fail to install
   python -m playwright install --force
   ```

2. **Permission Errors**
   ```bash
   # Run with elevated permissions if needed
   sudo python gui_app.py  # Linux/macOS
   ```

3. **Network Issues**
   - Check firewall settings
   - Verify proxy configuration
   - Test internet connectivity

4. **Proxy Connection Issues**
   - Test proxies: `python proxy_manager.py --test`
   - Verify proxy credentials and format
   - Check proxy server availability
   - Try different proxy types (HTTP vs SOCKS)

5. **Rate Limiting/Blocking**
   - Add more proxies for rotation
   - Increase delays between requests
   - Use residential proxies instead of datacenter
   - Enable user agent rotation

6. **Memory Issues**
   - Reduce concurrent pages in config
   - Use headless mode
   - Clear database periodically

### Proxy Best Practices

1. **Proxy Selection**
   - Use residential proxies for better success rates
   - Avoid free proxies for production use
   - Test proxies before adding to rotation
   - Mix different proxy providers

2. **Configuration Tips**
   - Start with 5-10 working proxies
   - Set appropriate delays (1-3 seconds)
   - Monitor proxy health regularly
   - Rotate user agents with proxies

3. **Recommended Proxy Providers**
   - **Bright Data** (formerly Luminati) - Premium residential
   - **Oxylabs** - High-quality datacenter and residential
   - **Smartproxy** - Affordable residential proxies
   - **ProxyMesh** - Rotating proxy service
   - **Storm Proxies** - Budget-friendly option

4. **Monitoring and Maintenance**
   ```bash
   # Regular proxy health checks
   python proxy_manager.py --test
   
   # Monitor failed proxies
   python demo_proxy_rotation.py
   
   # Export proxy configuration
   python proxy_manager.py --export my_proxies.txt
   ```

### Debug Mode
Enable debug logging:
```python
from loguru import logger
logger.add("debug.log", level="DEBUG")
```

## 📈 Performance Metrics

### Typical Performance
- **Google Search**: 50-100 contacts per hour
- **LinkedIn Profiles**: 20-50 profiles per hour
- **Website Scraping**: 100-200 pages per hour
- **Database Operations**: 1000+ contacts per second

### Optimization Tips
- Use SSD storage for database
- Increase RAM for larger datasets
- Use multiple proxy servers
- Optimize network bandwidth

## 🔒 Security Considerations

### Data Protection
- **Local Storage**: All data stored locally by default
- **Encryption**: Consider encrypting sensitive data
- **Access Control**: Implement user authentication for production use
- **Audit Logging**: Track all scraping activities

### Privacy Compliance
- **GDPR Compliance**: Implement data subject rights
- **Data Minimization**: Only collect necessary information
- **Consent Management**: Ensure proper consent for data collection
- **Data Retention**: Implement data retention policies

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please ensure compliance with:
- Website terms of service
- Local data protection laws
- Platform-specific scraping policies
- Ethical scraping guidelines

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Test with the provided test script
4. Create an issue with detailed information

## 🎯 Use Cases

### Sales and Marketing
- **Lead Generation**: Find potential customers and their contact information
- **Market Research**: Analyze competitor contact strategies
- **Outreach Campaigns**: Build targeted email lists for marketing
- **Sales Prospecting**: Identify decision makers in target companies

### Recruitment
- **Candidate Sourcing**: Find potential candidates on LinkedIn
- **Contact Discovery**: Get contact information for recruitment outreach
- **Industry Analysis**: Research professionals in specific industries
- **Talent Mapping**: Build comprehensive talent databases

### Business Development
- **Partnership Opportunities**: Find potential business partners
- **Vendor Research**: Identify suppliers and service providers
- **Industry Networking**: Build professional contact networks
- **Market Intelligence**: Gather competitive intelligence

---

**⚠️ Disclaimer**: This tool is designed for legitimate business purposes. Users are responsible for ensuring compliance with applicable laws, regulations, and website terms of service. Always respect privacy and data protection rights.