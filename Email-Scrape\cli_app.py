#!/usr/bin/env python3
"""
Email Scraper Pro - Command Line Interface
Advanced Lead Generation Tool - CLI Version
"""

import asyncio
import click
import sys
import os
from typing import List
from pathlib import Path

# Import our custom modules
from email_scraper import Web<PERSON>craper, DataManager, Contact
from loguru import logger

# Configure logger for CLI
logger.remove()  # Remove default handler
logger.add(sys.stderr, level="INFO", format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | {message}")
logger.add("email_scraper_cli.log", rotation="10 MB", level="DEBUG")

class EmailScraperCLI:
    """Command Line Interface for Email Scraper Pro"""
    
    def __init__(self):
        self.scraper = WebScraper()
        self.data_manager = DataManager()
    
    async def run_google_search(self, queries: List[str], max_pages: int = 5, headless: bool = True):
        """Run Google search scraping"""
        logger.info(f"Starting Google search scraping for {len(queries)} queries")
        
        try:
            await self.scraper.init_browser(headless=headless)
            all_contacts = []
            
            for i, query in enumerate(queries, 1):
                logger.info(f"Processing query {i}/{len(queries)}: {query}")
                
                contacts = await self.scraper.google_search_scrape(query, max_pages)
                
                if contacts:
                    saved_count = self.data_manager.save_contacts(contacts)
                    logger.success(f"Found {len(contacts)} contacts, saved {saved_count} new ones")
                    all_contacts.extend(contacts)
                else:
                    logger.warning(f"No contacts found for query: {query}")
            
            logger.success(f"Google search completed. Total contacts found: {len(all_contacts)}")
            return all_contacts
            
        except Exception as e:
            logger.error(f"Google search scraping failed: {e}")
            raise
        finally:
            await self.scraper.close_browser()
    
    async def run_linkedin_scraping(self, profile_urls: List[str], headless: bool = True):
        """Run LinkedIn profile scraping"""
        logger.info(f"Starting LinkedIn scraping for {len(profile_urls)} profiles")
        
        try:
            await self.scraper.init_browser(headless=headless)
            
            contacts = await self.scraper.linkedin_profile_scrape(profile_urls)
            
            if contacts:
                saved_count = self.data_manager.save_contacts(contacts)
                logger.success(f"Found {len(contacts)} contacts, saved {saved_count} new ones")
            else:
                logger.warning("No contacts found from LinkedIn profiles")
            
            return contacts
            
        except Exception as e:
            logger.error(f"LinkedIn scraping failed: {e}")
            raise
        finally:
            await self.scraper.close_browser()
    
    async def run_website_scraping(self, urls: List[str], headless: bool = True):
        """Run website scraping"""
        logger.info(f"Starting website scraping for {len(urls)} URLs")
        
        try:
            await self.scraper.init_browser(headless=headless)
            
            contacts = await self.scraper.website_scrape(urls)
            
            if contacts:
                saved_count = self.data_manager.save_contacts(contacts)
                logger.success(f"Found {len(contacts)} contacts, saved {saved_count} new ones")
            else:
                logger.warning("No contacts found from websites")
            
            return contacts
            
        except Exception as e:
            logger.error(f"Website scraping failed: {e}")
            raise
        finally:
            await self.scraper.close_browser()

# CLI Commands using Click
@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Email Scraper Pro - Advanced Lead Generation Tool
    
    A powerful tool for extracting email contacts from Google search,
    LinkedIn profiles, and websites using advanced web scraping techniques.
    """
    pass

@cli.command()
@click.option('--query', '-q', multiple=True, required=True, help='Search queries (can be used multiple times)')
@click.option('--max-pages', '-p', default=5, help='Maximum pages to scrape per query')
@click.option('--headless/--no-headless', default=True, help='Run browser in headless mode')
@click.option('--output', '-o', help='Output file path (CSV or Excel)')
def google(query, max_pages, headless, output):
    """Scrape emails from Google search results
    
    Example:
        python cli_app.py google -q "marketing manager email" -q "sales director contact" -p 3
    """
    cli_app = EmailScraperCLI()
    
    try:
        contacts = asyncio.run(cli_app.run_google_search(list(query), max_pages, headless))
        
        if output:
            export_contacts(cli_app.data_manager, output)
        
        display_summary(contacts)
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--url', '-u', multiple=True, required=True, help='LinkedIn profile URLs (can be used multiple times)')
@click.option('--headless/--no-headless', default=True, help='Run browser in headless mode')
@click.option('--output', '-o', help='Output file path (CSV or Excel)')
def linkedin(url, headless, output):
    """Scrape emails from LinkedIn profiles
    
    Example:
        python cli_app.py linkedin -u "https://linkedin.com/in/username1" -u "https://linkedin.com/in/username2"
    """
    cli_app = EmailScraperCLI()
    
    try:
        contacts = asyncio.run(cli_app.run_linkedin_scraping(list(url), headless))
        
        if output:
            export_contacts(cli_app.data_manager, output)
        
        display_summary(contacts)
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--url', '-u', multiple=True, required=True, help='Website URLs (can be used multiple times)')
@click.option('--headless/--no-headless', default=True, help='Run browser in headless mode')
@click.option('--output', '-o', help='Output file path (CSV or Excel)')
def websites(url, headless, output):
    """Scrape emails from websites
    
    Example:
        python cli_app.py websites -u "https://company1.com" -u "https://company2.com"
    """
    cli_app = EmailScraperCLI()
    
    try:
        contacts = asyncio.run(cli_app.run_website_scraping(list(url), headless))
        
        if output:
            export_contacts(cli_app.data_manager, output)
        
        display_summary(contacts)
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--file', '-f', required=True, type=click.Path(exists=True), help='Input file with queries/URLs (one per line)')
@click.option('--type', '-t', type=click.Choice(['google', 'linkedin', 'websites']), required=True, help='Scraping type')
@click.option('--max-pages', '-p', default=5, help='Maximum pages to scrape per query (for Google search)')
@click.option('--headless/--no-headless', default=True, help='Run browser in headless mode')
@click.option('--output', '-o', help='Output file path (CSV or Excel)')
def batch(file, type, max_pages, headless, output):
    """Batch scraping from input file
    
    Example:
        python cli_app.py batch -f queries.txt -t google -p 5 -o results.csv
    """
    cli_app = EmailScraperCLI()
    
    try:
        # Read input file
        with open(file, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        
        if not lines:
            logger.error("Input file is empty")
            sys.exit(1)
        
        logger.info(f"Loaded {len(lines)} items from {file}")
        
        # Run appropriate scraping
        contacts = []
        if type == 'google':
            contacts = asyncio.run(cli_app.run_google_search(lines, max_pages, headless))
        elif type == 'linkedin':
            contacts = asyncio.run(cli_app.run_linkedin_scraping(lines, headless))
        elif type == 'websites':
            contacts = asyncio.run(cli_app.run_website_scraping(lines, headless))
        
        if output:
            export_contacts(cli_app.data_manager, output)
        
        display_summary(contacts)
        
    except KeyboardInterrupt:
        logger.info("Scraping interrupted by user")
    except Exception as e:
        logger.error(f"Batch scraping failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--format', '-f', type=click.Choice(['csv', 'excel', 'json']), default='csv', help='Export format')
@click.option('--output', '-o', required=True, help='Output file path')
def export(format, output):
    """Export all contacts to file
    
    Example:
        python cli_app.py export -f csv -o all_contacts.csv
        python cli_app.py export -f excel -o all_contacts.xlsx
    """
    data_manager = DataManager()
    
    try:
        if format == 'csv':
            if not output.endswith('.csv'):
                output += '.csv'
            success = data_manager.export_to_csv(output)
        elif format == 'excel':
            if not output.endswith('.xlsx'):
                output += '.xlsx'
            success = data_manager.export_to_excel(output)
        elif format == 'json':
            if not output.endswith('.json'):
                output += '.json'
            success = export_to_json(data_manager, output)
        
        if success:
            logger.success(f"Contacts exported to {output}")
        else:
            logger.error("Export failed")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Export failed: {e}")
        sys.exit(1)

@cli.command()
def stats():
    """Show database statistics"""
    data_manager = DataManager()
    
    try:
        contacts = data_manager.get_all_contacts()
        
        if not contacts:
            logger.info("No contacts found in database")
            return
        
        # Calculate statistics
        total_contacts = len(contacts)
        sources = {}
        companies = {}
        
        for contact in contacts:
            # Count by source
            source = contact.source or 'Unknown'
            sources[source] = sources.get(source, 0) + 1
            
            # Count by company
            company = contact.company or 'Unknown'
            companies[company] = companies.get(company, 0) + 1
        
        # Display statistics
        click.echo("\n" + "="*50)
        click.echo("📊 EMAIL SCRAPER PRO - DATABASE STATISTICS")
        click.echo("="*50)
        
        click.echo(f"\n📧 Total Contacts: {total_contacts}")
        
        click.echo("\n📈 Contacts by Source:")
        for source, count in sorted(sources.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_contacts) * 100
            click.echo(f"  • {source}: {count} ({percentage:.1f}%)")
        
        click.echo("\n🏢 Top Companies:")
        top_companies = sorted(companies.items(), key=lambda x: x[1], reverse=True)[:10]
        for company, count in top_companies:
            if company != 'Unknown':
                click.echo(f"  • {company}: {count} contacts")
        
        click.echo("\n" + "="*50)
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        sys.exit(1)

@cli.command()
@click.confirmation_option(prompt='Are you sure you want to delete all contacts?')
def clear():
    """Clear all contacts from database"""
    data_manager = DataManager()
    
    try:
        if os.path.exists(data_manager.db_path):
            os.remove(data_manager.db_path)
        
        data_manager.init_database()
        logger.success("All contacts cleared from database")
        
    except Exception as e:
        logger.error(f"Failed to clear database: {e}")
        sys.exit(1)

def export_contacts(data_manager: DataManager, output_path: str):
    """Export contacts based on file extension"""
    if output_path.endswith('.csv'):
        success = data_manager.export_to_csv(output_path)
    elif output_path.endswith('.xlsx'):
        success = data_manager.export_to_excel(output_path)
    elif output_path.endswith('.json'):
        success = export_to_json(data_manager, output_path)
    else:
        logger.error("Unsupported output format. Use .csv, .xlsx, or .json")
        return
    
    if success:
        logger.success(f"Contacts exported to {output_path}")
    else:
        logger.error("Export failed")

def export_to_json(data_manager: DataManager, filename: str) -> bool:
    """Export contacts to JSON file"""
    try:
        import json
        contacts = data_manager.get_all_contacts()
        
        data = []
        for contact in contacts:
            data.append({
                'email': contact.email,
                'name': contact.name,
                'company': contact.company,
                'title': contact.title,
                'linkedin_url': contact.linkedin_url,
                'phone': contact.phone,
                'website': contact.website,
                'source': contact.source,
                'extracted_date': contact.extracted_date
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        return True
    except Exception as e:
        logger.error(f"JSON export failed: {e}")
        return False

def display_summary(contacts: List[Contact]):
    """Display scraping summary"""
    if not contacts:
        logger.info("No contacts found")
        return
    
    click.echo("\n" + "="*40)
    click.echo("📊 SCRAPING SUMMARY")
    click.echo("="*40)
    
    click.echo(f"\n📧 Total Contacts Found: {len(contacts)}")
    
    # Group by source
    sources = {}
    for contact in contacts:
        source = contact.source or 'Unknown'
        sources[source] = sources.get(source, 0) + 1
    
    click.echo("\n📈 Breakdown by Source:")
    for source, count in sources.items():
        click.echo(f"  • {source}: {count}")
    
    # Show sample contacts
    click.echo("\n📋 Sample Contacts:")
    for i, contact in enumerate(contacts[:5], 1):
        click.echo(f"  {i}. {contact.email}")
        if contact.name:
            click.echo(f"     Name: {contact.name}")
        if contact.company:
            click.echo(f"     Company: {contact.company}")
        click.echo()
    
    if len(contacts) > 5:
        click.echo(f"  ... and {len(contacts) - 5} more contacts")
    
    click.echo("="*40)

if __name__ == '__main__':
    cli()