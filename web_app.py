from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import os
import sys
import json
import asyncio
import threading
from datetime import datetime

# Add the Email-Scrape directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Email-Scrape'))

# Import existing modules
from bulk_scraper import BulkEmailScraper
from email_generator import EmailGenerator
from smtp_config import SMTPManager
from mass_email_sender import MassEmailSender
from email_templates import TemplateManager
from config import Config

app = Flask(__name__)
app.config['SECRET_KEY'] = 'email_scraper_secret_key_2024'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize components
config = Config()
bulk_scraper = BulkEmailScraper(config)
email_generator = EmailGenerator()
smtp_manager = SMTPManager()
mass_sender = MassEmailSender(smtp_manager)
template_manager = TemplateManager()

# Global variables for tracking
scraping_progress = {
    'status': 'idle',
    'progress': 0,
    'total': 0,
    'current_site': '',
    'results': [],
    'errors': []
}

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/scraper')
def scraper_page():
    """Email scraper interface"""
    return render_template('scraper.html')

@app.route('/campaigns')
def campaigns_page():
    """Email campaigns interface"""
    return render_template('campaigns.html')

@app.route('/templates')
def templates_page():
    """Email templates interface"""
    return render_template('templates.html')

@app.route('/settings')
def settings_page():
    """Settings and configuration"""
    return render_template('settings.html')

# API Endpoints
@app.route('/api/scrape', methods=['POST'])
def start_scraping():
    """Start email scraping process"""
    try:
        data = request.get_json()
        urls = data.get('urls', [])
        search_terms = data.get('search_terms', [])
        
        if not urls:
            return jsonify({'error': 'No URLs provided'}), 400
        
        # Start scraping in background thread
        thread = threading.Thread(target=run_scraping, args=(urls, search_terms))
        thread.daemon = True
        thread.start()
        
        return jsonify({'message': 'Scraping started', 'status': 'started'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/scrape/status')
def get_scraping_status():
    """Get current scraping status"""
    return jsonify(scraping_progress)

@app.route('/api/scrape/stop', methods=['POST'])
def stop_scraping():
    """Stop current scraping process"""
    global scraping_progress
    scraping_progress['status'] = 'stopped'
    return jsonify({'message': 'Scraping stopped'})

@app.route('/api/generate-emails', methods=['POST'])
def generate_emails():
    """Generate emails from domains"""
    try:
        data = request.get_json()
        domains = data.get('domains', [])
        patterns = data.get('patterns', [])
        
        if not domains:
            return jsonify({'error': 'No domains provided'}), 400
        
        generated_emails = []
        for domain in domains:
            emails = email_generator.generate_emails_for_domain(domain, patterns)
            generated_emails.extend(emails)
        
        return jsonify({
            'emails': generated_emails,
            'count': len(generated_emails)
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/smtp/configs')
def get_smtp_configs():
    """Get SMTP configurations"""
    try:
        configs = smtp_manager.get_config_list()
        return jsonify(configs)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/smtp/configs', methods=['POST'])
def add_smtp_config():
    """Add new SMTP configuration"""
    try:
        data = request.get_json()
        config_name = data.get('name')
        smtp_config = {
            'provider': data.get('provider'),
            'smtp_server': data.get('smtp_server'),
            'smtp_port': data.get('smtp_port'),
            'email': data.get('email'),
            'password': data.get('password'),
            'use_tls': data.get('use_tls', True)
        }
        
        smtp_manager.add_config(config_name, smtp_config)
        return jsonify({'message': 'SMTP configuration added successfully'})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/templates')
def get_templates():
    """Get email templates"""
    try:
        templates = template_manager.get_all_templates()
        return jsonify([{
            'id': template.id,
            'name': template.name,
            'subject': template.subject,
            'category': template.category,
            'created_at': template.created_at.isoformat() if template.created_at else None
        } for template in templates])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns')
def get_campaigns():
    """Get email campaigns"""
    try:
        campaigns = mass_sender.get_all_campaigns()
        return jsonify([{
            'id': campaign.id,
            'name': campaign.config.name,
            'status': campaign.status.value,
            'sent_count': campaign.sent_count,
            'failed_count': campaign.failed_count,
            'created_at': campaign.created_at.isoformat() if campaign.created_at else None
        } for campaign in campaigns])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_scraping(urls, search_terms):
    """Run scraping process in background"""
    global scraping_progress
    
    scraping_progress.update({
        'status': 'running',
        'progress': 0,
        'total': len(urls),
        'current_site': '',
        'results': [],
        'errors': []
    })
    
    try:
        for i, url in enumerate(urls):
            if scraping_progress['status'] == 'stopped':
                break
                
            scraping_progress['current_site'] = url
            scraping_progress['progress'] = i
            
            # Emit progress update
            socketio.emit('scraping_progress', scraping_progress)
            
            try:
                # Perform scraping
                results = bulk_scraper.scrape_single_url(url, search_terms)
                scraping_progress['results'].extend(results)
            except Exception as e:
                error_msg = f"Error scraping {url}: {str(e)}"
                scraping_progress['errors'].append(error_msg)
        
        scraping_progress['status'] = 'completed'
        scraping_progress['progress'] = len(urls)
        
    except Exception as e:
        scraping_progress['status'] = 'error'
        scraping_progress['errors'].append(str(e))
    
    # Emit final status
    socketio.emit('scraping_complete', scraping_progress)

# WebSocket events
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('status', {'message': 'Connected to Email Scraper'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    
    os.makedirs(templates_dir, exist_ok=True)
    os.makedirs(static_dir, exist_ok=True)
    
    print("Starting Email Scraper Web Application...")
    print("Access the application at: http://localhost:5000")
    
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)