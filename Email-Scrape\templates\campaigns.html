{% extends "base.html" %}

{% block title %}Campaign Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-bullhorn"></i> Campaign Management</h1>
                <p class="text-muted">Create, manage, and monitor your email campaigns</p>
            </div>
        </div>
    </div>

    <!-- Campaign Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stats-content">
                    <h3 id="total-campaigns">0</h3>
                    <p>Total Campaigns</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-play"></i>
                </div>
                <div class="stats-content">
                    <h3 id="active-campaigns">0</h3>
                    <p>Active Campaigns</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stats-content">
                    <h3 id="total-sent">0</h3>
                    <p>Emails Sent</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-content">
                    <h3 id="success-rate">0%</h3>
                    <p>Success Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus"></i> Create New Campaign</h5>
                </div>
                <div class="card-body">
                    <form id="campaign-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="campaign-name">Campaign Name</label>
                                    <input type="text" class="form-control" id="campaign-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="campaign-template">Email Template</label>
                                    <select class="form-control" id="campaign-template" required>
                                        <option value="">Select Template</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="campaign-smtp">SMTP Configuration</label>
                                    <select class="form-control" id="campaign-smtp" required>
                                        <option value="">Select SMTP Config</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="campaign-recipients">Recipients File</label>
                                    <input type="file" class="form-control-file" id="campaign-recipients" accept=".csv,.txt">
                                    <small class="form-text text-muted">Upload CSV or TXT file with email addresses</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="campaign-delay">Delay Between Emails (seconds)</label>
                                    <input type="number" class="form-control" id="campaign-delay" value="1" min="0.1" step="0.1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="campaign-batch-size">Batch Size</label>
                                    <input type="number" class="form-control" id="campaign-batch-size" value="10" min="1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="campaign-priority">Priority</label>
                                    <select class="form-control" id="campaign-priority">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Campaign
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Active Campaigns</h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshCampaigns()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <div id="campaigns-loading" class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading campaigns...</span>
                        </div>
                        <p class="mt-2">Loading campaigns...</p>
                    </div>
                    <div id="campaigns-list" class="d-none">
                        <!-- Campaigns will be loaded here -->
                    </div>
                    <div id="no-campaigns" class="text-center py-4 d-none">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No campaigns found</h5>
                        <p class="text-muted">Create your first campaign to get started</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Campaign Details Modal -->
<div class="modal fade" id="campaignModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Campaign Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="campaign-details">
                    <!-- Campaign details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="start-campaign-btn">
                    <i class="fas fa-play"></i> Start Campaign
                </button>
                <button type="button" class="btn btn-warning" id="pause-campaign-btn">
                    <i class="fas fa-pause"></i> Pause Campaign
                </button>
                <button type="button" class="btn btn-danger" id="stop-campaign-btn">
                    <i class="fas fa-stop"></i> Stop Campaign
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let campaigns = [];
let templates = [];
let smtpConfigs = [];

// Initialize page
$(document).ready(function() {
    loadTemplates();
    loadSMTPConfigs();
    loadCampaigns();
    
    // Form submission
    $('#campaign-form').on('submit', function(e) {
        e.preventDefault();
        createCampaign();
    });
});

// Load templates for dropdown
function loadTemplates() {
    $.get('/api/templates')
        .done(function(response) {
            templates = response.templates;
            const select = $('#campaign-template');
            select.empty().append('<option value="">Select Template</option>');
            
            templates.forEach(template => {
                select.append(`<option value="${template.id}">${template.name}</option>`);
            });
        })
        .fail(function() {
            showAlert('Failed to load templates', 'danger');
        });
}

// Load SMTP configurations for dropdown
function loadSMTPConfigs() {
    $.get('/api/smtp-configs')
        .done(function(response) {
            smtpConfigs = response.smtp_configs;
            const select = $('#campaign-smtp');
            select.empty().append('<option value="">Select SMTP Config</option>');
            
            smtpConfigs.forEach(config => {
                select.append(`<option value="${config.name}">${config.name} (${config.host}:${config.port})</option>`);
            });
        })
        .fail(function() {
            showAlert('Failed to load SMTP configurations', 'danger');
        });
}

// Load campaigns
function loadCampaigns() {
    $('#campaigns-loading').removeClass('d-none');
    $('#campaigns-list').addClass('d-none');
    $('#no-campaigns').addClass('d-none');
    
    $.get('/api/campaigns')
        .done(function(response) {
            campaigns = response.campaigns;
            displayCampaigns();
            updateStats();
        })
        .fail(function() {
            showAlert('Failed to load campaigns', 'danger');
        })
        .always(function() {
            $('#campaigns-loading').addClass('d-none');
        });
}

// Display campaigns
function displayCampaigns() {
    if (campaigns.length === 0) {
        $('#no-campaigns').removeClass('d-none');
        return;
    }
    
    const container = $('#campaigns-list');
    container.empty();
    
    campaigns.forEach(campaign => {
        const statusBadge = getStatusBadge(campaign.status);
        const successRate = campaign.sent_count > 0 ? 
            ((campaign.sent_count / (campaign.sent_count + campaign.failed_count)) * 100).toFixed(1) : 0;
        
        const campaignCard = `
            <div class="campaign-card mb-3" data-campaign-id="${campaign.id}">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="mb-1">${campaign.name}</h6>
                                <small class="text-muted">ID: ${campaign.id}</small>
                            </div>
                            <div class="col-md-2">
                                ${statusBadge}
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">Sent</small>
                                <div class="font-weight-bold">${campaign.sent_count}</div>
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">Failed</small>
                                <div class="font-weight-bold text-danger">${campaign.failed_count}</div>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewCampaign('${campaign.id}')">
                                    <i class="fas fa-eye"></i> View
                                </button>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-success" style="width: ${successRate}%"></div>
                                </div>
                                <small class="text-muted">Success Rate: ${successRate}%</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.append(campaignCard);
    });
    
    container.removeClass('d-none');
}

// Get status badge
function getStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge badge-secondary">Pending</span>',
        'running': '<span class="badge badge-success">Running</span>',
        'paused': '<span class="badge badge-warning">Paused</span>',
        'completed': '<span class="badge badge-primary">Completed</span>',
        'failed': '<span class="badge badge-danger">Failed</span>',
        'stopped': '<span class="badge badge-dark">Stopped</span>'
    };
    return badges[status] || '<span class="badge badge-light">Unknown</span>';
}

// Update statistics
function updateStats() {
    const totalCampaigns = campaigns.length;
    const activeCampaigns = campaigns.filter(c => c.status === 'running').length;
    const totalSent = campaigns.reduce((sum, c) => sum + c.sent_count, 0);
    const totalFailed = campaigns.reduce((sum, c) => sum + c.failed_count, 0);
    const successRate = totalSent > 0 ? ((totalSent / (totalSent + totalFailed)) * 100).toFixed(1) : 0;
    
    $('#total-campaigns').text(totalCampaigns);
    $('#active-campaigns').text(activeCampaigns);
    $('#total-sent').text(totalSent.toLocaleString());
    $('#success-rate').text(successRate + '%');
}

// Create new campaign
function createCampaign() {
    const formData = new FormData();
    formData.append('name', $('#campaign-name').val());
    formData.append('template_id', $('#campaign-template').val());
    formData.append('smtp_config', $('#campaign-smtp').val());
    formData.append('delay', $('#campaign-delay').val());
    formData.append('batch_size', $('#campaign-batch-size').val());
    formData.append('priority', $('#campaign-priority').val());
    
    const recipientsFile = $('#campaign-recipients')[0].files[0];
    if (recipientsFile) {
        formData.append('recipients_file', recipientsFile);
    }
    
    $.ajax({
        url: '/api/campaigns',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            showAlert('Campaign created successfully!', 'success');
            $('#campaign-form')[0].reset();
            loadCampaigns();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to create campaign';
            showAlert(error, 'danger');
        }
    });
}

// View campaign details
function viewCampaign(campaignId) {
    const campaign = campaigns.find(c => c.id === campaignId);
    if (!campaign) return;
    
    const details = `
        <div class="row">
            <div class="col-md-6">
                <h6>Campaign Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${campaign.name}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>${getStatusBadge(campaign.status)}</td></tr>
                    <tr><td><strong>Created:</strong></td><td>${new Date(campaign.created_at).toLocaleString()}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Statistics</h6>
                <table class="table table-sm">
                    <tr><td><strong>Emails Sent:</strong></td><td>${campaign.sent_count}</td></tr>
                    <tr><td><strong>Failed:</strong></td><td>${campaign.failed_count}</td></tr>
                    <tr><td><strong>Success Rate:</strong></td><td>${campaign.sent_count > 0 ? ((campaign.sent_count / (campaign.sent_count + campaign.failed_count)) * 100).toFixed(1) : 0}%</td></tr>
                </table>
            </div>
        </div>
    `;
    
    $('#campaign-details').html(details);
    $('#campaignModal').modal('show');
}

// Refresh campaigns
function refreshCampaigns() {
    loadCampaigns();
}

// Show alert
function showAlert(message, type) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    $('.container-fluid').prepend(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}