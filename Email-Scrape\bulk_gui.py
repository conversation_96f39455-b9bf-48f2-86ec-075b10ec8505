#!/usr/bin/env python3
"""
Bulk Email Scraper GUI
Modern GUI application for bulk scraping and email generation
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import asyncio
import threading
import json
import csv
from pathlib import Path
from typing import Dict, List
from loguru import logger
from bulk_scraper import BulkScraper
from email_generator import EmailGenerator
from config import get_config

# Pure black theme with colored buttons
COLORS = {
    'bg_primary': '#000000',       # Pure black background
    'bg_secondary': '#111111',     # Slightly lighter black
    'bg_card': '#000000',          # Black cards
    'accent_primary': '#667eea',   # Modern blue (kept colored)
    'accent_secondary': '#764ba2', # Purple accent (kept colored)
    'accent_success': '#48bb78',   # Green (kept colored)
    'accent_warning': '#ed8936',   # Orange (kept colored)
    'accent_danger': '#f56565',    # Red (kept colored)
    'text_primary': '#ffffff',     # White text
    'text_secondary': '#cccccc',   # Light gray text
    'text_light': '#999999',       # Medium gray text
    'shadow_light': '#333333',     # Dark shadow (highlight)
    'shadow_dark': '#000000',      # Pure black shadow
    'border': '#333333',           # Dark border
    'hover': '#222222'             # Dark hover state
}

# Enhanced Neumorphic button style with larger fonts
NEUMORPHIC_STYLE = {
    'relief': 'flat',
    'borderwidth': 0,
    'highlightthickness': 0,
    'font': ('Segoe UI', 14, 'bold'),
    'cursor': 'hand2'
}

# Enhanced font sizes for better visibility
FONT_SIZES = {
    'title': ('Segoe UI', 28, 'bold'),
    'subtitle': ('Segoe UI', 16, 'normal'),
    'header': ('Segoe UI', 18, 'bold'),
    'subheader': ('Segoe UI', 14, 'bold'),
    'body': ('Segoe UI', 12, 'normal'),
    'button': ('Segoe UI', 14, 'bold'),
    'input': ('Segoe UI', 13, 'normal'),
    'small': ('Segoe UI', 11, 'normal')
}

class BulkScraperGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("BMD EMAIL SCRAPPER - Enhanced Bulk Scraping & Generation")
        self.root.geometry("1600x1000")  # Larger window for better visibility
        self.root.configure(bg=COLORS['bg_primary'])
        self.root.option_add('*TCombobox*Listbox.selectBackground', COLORS['accent_primary'])
        
        # Configure window to be more visible
        self.root.state('zoomed')  # Maximize window on Windows
        self.root.lift()
        self.root.focus_force()
        
        # Initialize components
        self.scraper = BulkScraper()
        self.generator = EmailGenerator()
        self.is_running = False
        self.consent_given = False
        
        # Show consent screen first
        self.show_consent_screen()
        
    def show_consent_screen(self):
        """Show consent and disclaimer screen"""
        # Hide main window
        self.root.withdraw()
        
        # Create enhanced consent window
        self.consent_window = tk.Toplevel(self.root)
        self.consent_window.title("BMD EMAIL SCRAPPER - Enhanced Legal Notice")
        self.consent_window.geometry("1000x800")  # Larger for better visibility
        self.consent_window.configure(bg=COLORS['bg_primary'])
        self.consent_window.resizable(True, True)  # Allow resizing for accessibility
        
        # Center the window and ensure it's visible
        self.consent_window.transient(self.root)
        self.consent_window.grab_set()
        self.consent_window.lift()
        self.consent_window.focus_force()
        self.consent_window.attributes('-topmost', True)
        self.consent_window.after(1000, lambda: self.consent_window.attributes('-topmost', False))
        
        # Handle window close event
        self.consent_window.protocol("WM_DELETE_WINDOW", self.decline_terms)
        
        # Main container
        main_frame = tk.Frame(self.consent_window, bg=COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # Enhanced Title with larger font
        title_label = tk.Label(main_frame, text="⚠️ IMPORTANT LEGAL NOTICE", 
                              bg=COLORS['bg_primary'], fg=COLORS['accent_danger'],
                              font=FONT_SIZES['title'])
        title_label.pack(pady=(0, 30))
        
        # Disclaimer text
        disclaimer_frame = tk.Frame(main_frame, bg=COLORS['bg_card'], relief='solid', bd=1)
        disclaimer_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        disclaimer_text = scrolledtext.ScrolledText(disclaimer_frame, height=18, wrap='word',
                                                   bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                   font=FONT_SIZES['body'], relief='flat',
                                                   selectbackground=COLORS['accent_primary'],
                                                   selectforeground='white')
        disclaimer_text.pack(fill='both', expand=True, padx=20, pady=20)
        
        disclaimer_content = """DISCLAIMER AND TERMS OF USE

By using BMD EMAIL SCRAPPER, you acknowledge and agree to the following terms:

1. LEGAL COMPLIANCE
   • This software is intended for legitimate business purposes only
   • Users must comply with all applicable laws including GDPR, CAN-SPAM Act, and local privacy regulations
   • Email scraping must only be performed on publicly available information
   • Users are responsible for obtaining proper consent before contacting individuals

2. PROHIBITED USES
   • Spam or unsolicited bulk email campaigns
   • Harvesting emails for malicious purposes
   • Violating website terms of service
   • Any illegal or unethical activities

3. DATA PROTECTION
   • All scraped data must be handled according to applicable privacy laws
   • Users must implement appropriate security measures
   • Data retention policies must comply with legal requirements

4. LIABILITY DISCLAIMER
   • BMD EMAIL SCRAPPER is provided "AS IS" without warranties
   • The developers are not liable for any misuse of this software
   • Users assume full responsibility for their actions

5. ETHICAL GUIDELINES
   • Respect website robots.txt files and rate limits
   • Use reasonable delays between requests
   • Do not overload target servers
   • Respect opt-out requests immediately

6. BUSINESS USE ONLY
   • This tool is designed for legitimate business research
   • Lead generation must follow ethical marketing practices
   • Always provide clear unsubscribe options

By clicking "I AGREE", you confirm that you:
• Have read and understood these terms
• Will use this software legally and ethically
• Accept full responsibility for your actions
• Agree to comply with all applicable laws and regulations

If you do not agree to these terms, please click "DECLINE" and do not use this software."""
        
        disclaimer_text.insert('1.0', disclaimer_content)
        disclaimer_text.config(state='disabled')
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg=COLORS['bg_primary'])
        buttons_frame.pack(fill='x')
        
        # Enhanced Terms and Policy buttons
        terms_btn = tk.Button(buttons_frame, text="📋 View Terms", 
                             command=self.show_terms_window,
                             bg=COLORS['accent_warning'], fg='white',
                             font=FONT_SIZES['button'], relief='flat',
                             padx=25, pady=15, cursor='hand2')
        terms_btn.pack(side='left', padx=(0, 15))
        
        policy_btn = tk.Button(buttons_frame, text="🔒 Privacy Policy", 
                              command=self.show_policy_window,
                              bg=COLORS['accent_secondary'], fg='white',
                              font=FONT_SIZES['button'], relief='flat',
                              padx=25, pady=15, cursor='hand2')
        policy_btn.pack(side='left', padx=(0, 30))
        
        # Enhanced Agreement buttons
        decline_btn = tk.Button(buttons_frame, text="❌ DECLINE", 
                               command=self.decline_terms,
                               bg=COLORS['accent_danger'], fg='white',
                               font=FONT_SIZES['header'], relief='flat',
                               padx=40, pady=18, cursor='hand2')
        decline_btn.pack(side='right', padx=(15, 0))
        
        agree_btn = tk.Button(buttons_frame, text="✅ I AGREE", 
                             command=self.accept_terms,
                             bg=COLORS['accent_success'], fg='white',
                             font=FONT_SIZES['header'], relief='flat',
                             padx=40, pady=18, cursor='hand2')
        agree_btn.pack(side='right')
        
        # Handle window close
        self.consent_window.protocol("WM_DELETE_WINDOW", self.decline_terms)
        
    def show_terms_window(self):
        """Show detailed terms and conditions"""
        terms_window = tk.Toplevel(self.consent_window)
        terms_window.title("BMD EMAIL SCRAPPER - Terms & Conditions")
        terms_window.geometry("700x500")
        terms_window.configure(bg=COLORS['bg_primary'])
        
        main_frame = tk.Frame(terms_window, bg=COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        title_label = tk.Label(main_frame, text="📋 TERMS & CONDITIONS", 
                              bg=COLORS['bg_primary'], fg=COLORS['text_primary'],
                              font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))
        
        terms_text = scrolledtext.ScrolledText(main_frame, height=20, wrap='word',
                                              bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                              font=('Segoe UI', 9), relief='solid', bd=1)
        terms_text.pack(fill='both', expand=True, pady=(0, 15))
        
        terms_content = """TERMS AND CONDITIONS OF USE

Last Updated: January 2025

1. ACCEPTANCE OF TERMS
By accessing and using BMD EMAIL SCRAPPER, you accept and agree to be bound by the terms and provision of this agreement.

2. SOFTWARE LICENSE
• This software is licensed, not sold
• You may use this software for legitimate business purposes only
• Reverse engineering or redistribution is prohibited

3. USER RESPONSIBILITIES
• Ensure compliance with all applicable laws
• Respect website terms of service
• Implement appropriate data protection measures
• Use reasonable request rates to avoid server overload

4. PROHIBITED ACTIVITIES
• Sending unsolicited bulk emails (spam)
• Harvesting emails for malicious purposes
• Violating privacy laws or regulations
• Using the software for illegal activities

5. DATA HANDLING
• Users are responsible for all data collected
• Must comply with GDPR, CCPA, and other privacy laws
• Implement proper data security measures
• Respect data subject rights and requests

6. LIMITATION OF LIABILITY
• Software provided "AS IS" without warranties
• No liability for damages arising from use
• Users assume full responsibility for their actions

7. TERMINATION
• License may be terminated for violations
• All data must be properly disposed of upon termination

8. GOVERNING LAW
• These terms are governed by applicable local laws
• Disputes subject to local jurisdiction

9. UPDATES
• Terms may be updated without notice
• Continued use constitutes acceptance of changes

10. CONTACT
For questions about these terms, contact the software provider."""
        
        terms_text.insert('1.0', terms_content)
        terms_text.config(state='disabled')
        
        close_btn = tk.Button(main_frame, text="Close", command=terms_window.destroy,
                             bg=COLORS['accent_primary'], fg='white',
                             font=('Segoe UI', 10, 'bold'), relief='flat',
                             padx=20, pady=8, cursor='hand2')
        close_btn.pack()
        
    def show_policy_window(self):
        """Show privacy policy"""
        policy_window = tk.Toplevel(self.consent_window)
        policy_window.title("BMD EMAIL SCRAPPER - Privacy Policy")
        policy_window.geometry("700x500")
        policy_window.configure(bg=COLORS['bg_primary'])
        
        main_frame = tk.Frame(policy_window, bg=COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        title_label = tk.Label(main_frame, text="🔒 PRIVACY POLICY", 
                              bg=COLORS['bg_primary'], fg=COLORS['text_primary'],
                              font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))
        
        policy_text = scrolledtext.ScrolledText(main_frame, height=20, wrap='word',
                                               bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                               font=('Segoe UI', 9), relief='solid', bd=1)
        policy_text.pack(fill='both', expand=True, pady=(0, 15))
        
        policy_content = """PRIVACY POLICY

Last Updated: January 2025

1. INFORMATION COLLECTION
• BMD EMAIL SCRAPPER collects publicly available email addresses
• No personal data is stored by the software itself
• Users control all collected data

2. DATA USAGE
• Collected data is for legitimate business purposes only
• Must comply with applicable privacy laws
• Users responsible for lawful use of collected information

3. DATA STORAGE
• All data is stored locally on user's device
• No data is transmitted to external servers
• Users control data retention and deletion

4. DATA SECURITY
• Users must implement appropriate security measures
• Protect collected data from unauthorized access
• Use encryption for sensitive information

5. THIRD-PARTY WEBSITES
• Software may access third-party websites
• Each website has its own privacy policy
• Users must respect website terms and policies

6. USER OBLIGATIONS
• Comply with GDPR, CCPA, and other privacy laws
• Obtain necessary consents before data processing
• Respect data subject rights and requests
• Implement proper data protection measures

7. DATA SUBJECT RIGHTS
• Individuals have rights regarding their personal data
• Users must honor requests for access, correction, deletion
• Provide clear opt-out mechanisms

8. INTERNATIONAL TRANSFERS
• Users responsible for cross-border data transfer compliance
• Must ensure adequate protection levels
• Comply with applicable transfer mechanisms

9. CHILDREN'S PRIVACY
• Software not intended for use by children under 13
• Do not knowingly collect children's personal information
• Comply with COPPA and similar regulations

10. POLICY UPDATES
• This policy may be updated periodically
• Users will be notified of significant changes
• Continued use constitutes acceptance

11. CONTACT INFORMATION
For privacy-related questions or concerns, contact the software provider.

12. COMPLIANCE
• Users must ensure compliance with all applicable laws
• Seek legal advice when uncertain about obligations
• Implement privacy by design principles"""
        
        policy_text.insert('1.0', policy_content)
        policy_text.config(state='disabled')
        
        close_btn = tk.Button(main_frame, text="Close", command=policy_window.destroy,
                             bg=COLORS['accent_primary'], fg='white',
                             font=('Segoe UI', 10, 'bold'), relief='flat',
                             padx=20, pady=8, cursor='hand2')
        close_btn.pack()
        
    def accept_terms(self):
        """User accepted terms and conditions"""
        self.consent_given = True
        self.consent_window.destroy()
        self.root.deiconify()  # Show main window
        
        # Ensure main window is visible and focused
        self.root.lift()
        self.root.focus_force()
        self.root.attributes('-topmost', True)
        self.root.after(1000, lambda: self.root.attributes('-topmost', False))
        
        # Setup GUI after consent
        self.setup_styles()
        self.create_widgets()
        self.setup_logging()
        
    def decline_terms(self):
        """User declined terms - exit application"""
        self.root.quit()
        self.root.destroy()
        
    def setup_styles(self):
        """Setup custom neumorphic styles for the GUI"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure neumorphic styles
        style.configure('Title.TLabel', 
                       font=('Segoe UI', 20, 'bold'), 
                       background=COLORS['bg_primary'],
                       foreground=COLORS['text_primary'])
        
        style.configure('Subtitle.TLabel', 
                       font=('Segoe UI', 14, 'bold'), 
                       background=COLORS['bg_primary'],
                       foreground=COLORS['text_secondary'])
        
        style.configure('Action.TButton', 
                       font=('Segoe UI', 11, 'bold'),
                       background=COLORS['accent_primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none')
        
        style.map('Action.TButton',
                 background=[('active', COLORS['accent_secondary']),
                           ('pressed', COLORS['accent_secondary'])])
        
        style.configure('Success.TLabel', 
                       foreground=COLORS['accent_success'], 
                       background=COLORS['bg_primary'],
                       font=('Segoe UI', 10))
        
        style.configure('Error.TLabel', 
                       foreground=COLORS['accent_danger'], 
                       background=COLORS['bg_primary'],
                       font=('Segoe UI', 10))
        
        # Modern notebook style
        style.configure('TNotebook', 
                       background=COLORS['bg_primary'],
                       borderwidth=0)
        
        style.configure('TNotebook.Tab', 
                       background=COLORS['bg_secondary'],
                       foreground=COLORS['text_primary'],
                       padding=[20, 10],
                       font=('Segoe UI', 10, 'bold'))
        
        style.map('TNotebook.Tab',
                 background=[('selected', COLORS['bg_card']),
                           ('active', COLORS['hover'])],
                 foreground=[('selected', COLORS['accent_primary'])])
        
        # Modern frame style
        style.configure('Card.TLabelFrame',
                       background=COLORS['bg_card'],
                       borderwidth=1,
                       relief='solid',
                       bordercolor=COLORS['border'])
        
        style.configure('Card.TLabelFrame.Label',
                       background=COLORS['bg_card'],
                       foreground=COLORS['text_primary'],
                       font=('Segoe UI', 11, 'bold'))
        
        # Modern entry style
        style.configure('Modern.TEntry',
                       fieldbackground=COLORS['bg_card'],
                       borderwidth=1,
                       bordercolor=COLORS['border'],
                       focuscolor=COLORS['accent_primary'])
        
        # Modern progressbar
        style.configure('Modern.Horizontal.TProgressbar',
                       background=COLORS['accent_primary'],
                       troughcolor=COLORS['bg_secondary'],
                       borderwidth=0,
                       lightcolor=COLORS['accent_primary'],
                       darkcolor=COLORS['accent_primary'])
        
    def create_widgets(self):
        """Create and layout all GUI widgets with modern neumorphic design"""
        # Main container with padding
        main_container = tk.Frame(self.root, bg=COLORS['bg_primary'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header section with gradient-like effect
        header_frame = tk.Frame(main_container, bg=COLORS['bg_primary'], height=80)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # Enhanced main title with larger styling
        title_label = tk.Label(header_frame, text="🔥 BMD EMAIL SCRAPPER", 
                              bg=COLORS['bg_primary'], fg=COLORS['accent_primary'],
                              font=FONT_SIZES['title'])
        title_label.pack(pady=(15, 8))
        
        subtitle_label = tk.Label(header_frame, text="Professional Email Intelligence & Generation Platform", 
                                 bg=COLORS['bg_primary'], fg=COLORS['text_secondary'],
                                 font=FONT_SIZES['subtitle'])
        subtitle_label.pack()
        
        # Create modern notebook with neumorphic tabs
        self.notebook = ttk.Notebook(main_container)
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs with modern design
        self.create_bulk_scrape_tab()
        self.create_email_generation_tab()
        self.create_combined_tab()
        self.create_results_tab()
        self.create_settings_tab()
        
    def create_neumorphic_card(self, parent, title):
        """Create an enhanced neumorphic-style card container"""
        card = tk.Frame(parent, bg=COLORS['bg_card'], relief='solid', bd=2)
        
        # Enhanced title bar with larger height
        title_frame = tk.Frame(card, bg=COLORS['bg_secondary'], height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text=title, 
                              bg=COLORS['bg_secondary'], fg=COLORS['text_primary'],
                              font=FONT_SIZES['header'])
        title_label.pack(expand=True)
        
        return card
        
    def create_neumorphic_button(self, parent, text, command, color):
        """Create an enhanced neumorphic-style button"""
        btn = tk.Button(parent, text=text, command=command,
                       bg=color, fg='white', font=FONT_SIZES['button'],
                       relief='flat', bd=0, padx=30, pady=15,
                       cursor='hand2', activebackground=COLORS['accent_secondary'])
        
        # Enhanced hover effects
        def on_enter(e):
            btn.config(bg=COLORS['accent_secondary'], relief='raised', bd=2)
            
        def on_leave(e):
            btn.config(bg=color, relief='flat', bd=0)
            
        btn.bind('<Enter>', on_enter)
        btn.bind('<Leave>', on_leave)
        
        return btn
        
    def create_bulk_scrape_tab(self):
        """Create modern bulk scraping tab with neumorphic design"""
        # Main frame with modern background
        frame = tk.Frame(self.notebook, bg=COLORS['bg_primary'])
        self.notebook.add(frame, text='🔍 Bulk Scraping')
        
        # Scrollable container
        canvas = tk.Canvas(frame, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")
        
        # Search Configuration Card
        search_card = self.create_neumorphic_card(scrollable_frame, "🔍 Search Configuration")
        search_card.pack(fill='x', pady=(0, 20), padx=10)
        
        # Query input with modern styling
        query_frame = tk.Frame(search_card, bg=COLORS['bg_card'])
        query_frame.pack(fill='x', padx=20, pady=15)
        
        tk.Label(query_frame, text="Search Query:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=FONT_SIZES['subheader']).pack(anchor='w', pady=(0, 8))
        
        self.bulk_query_var = tk.StringVar(value="marketing agency")
        query_entry = tk.Entry(query_frame, textvariable=self.bulk_query_var,
                              bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                              font=FONT_SIZES['input'], relief='solid', bd=2,
                              highlightthickness=2, highlightcolor=COLORS['accent_primary'])
        query_entry.pack(fill='x', ipady=12)
        
        # Location input
        location_frame = tk.Frame(search_card, bg=COLORS['bg_card'])
        location_frame.pack(fill='x', padx=20, pady=(0, 15))
        
        tk.Label(location_frame, text="Location (optional):", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=FONT_SIZES['subheader']).pack(anchor='w', pady=(0, 8))
        
        self.bulk_location_var = tk.StringVar()
        location_entry = tk.Entry(location_frame, textvariable=self.bulk_location_var,
                                 bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                 font=FONT_SIZES['input'], relief='solid', bd=2,
                                 highlightthickness=2, highlightcolor=COLORS['accent_primary'])
        location_entry.pack(fill='x', ipady=12)
        
        # Enhanced Max results
        max_results_frame = tk.Frame(search_card, bg=COLORS['bg_card'])
        max_results_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        tk.Label(max_results_frame, text="Max Results per Platform:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=FONT_SIZES['subheader']).pack(anchor='w', pady=(0, 8))
        
        self.bulk_max_results_var = tk.IntVar(value=100)
        max_results_entry = tk.Entry(max_results_frame, textvariable=self.bulk_max_results_var,
                                    bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                    font=FONT_SIZES['input'], relief='solid', bd=2, width=15,
                                    highlightthickness=2, highlightcolor=COLORS['accent_primary'])
        max_results_entry.pack(anchor='w', ipady=10)
        
        # Platform Selection Card
        platform_card = self.create_neumorphic_card(scrollable_frame, "🌐 Platform Selection")
        platform_card.pack(fill='x', pady=(0, 20), padx=10)
        
        platform_inner = tk.Frame(platform_card, bg=COLORS['bg_card'])
        platform_inner.pack(fill='x', padx=20, pady=15)
        
        self.platform_vars = {}
        platforms = [
            ('Social Media', ['LinkedIn', 'Facebook', 'Twitter', 'Instagram']),
            ('Business Directories', ['Yellow Pages', 'Yelp', 'Google Business', 'White Pages']),
            ('B2B Platforms', ['Alibaba', 'AliExpress', 'ThomasNet', 'Global Sources']),
            ('Professional Networks', ['Crunchbase', 'AngelList', 'ZoomInfo'])
        ]
        
        for category, platform_list in platforms:
            category_frame = tk.Frame(platform_inner, bg=COLORS['bg_card'])
            category_frame.pack(fill='x', pady=(0, 15))
            
            tk.Label(category_frame, text=f"{category}:", 
                    bg=COLORS['bg_card'], fg=COLORS['accent_primary'],
                    font=FONT_SIZES['subheader']).pack(anchor='w', pady=(0, 12))
            
            platforms_grid = tk.Frame(category_frame, bg=COLORS['bg_card'])
            platforms_grid.pack(fill='x')
            
            for i, platform in enumerate(platform_list):
                var = tk.BooleanVar(value=True)
                self.platform_vars[platform.lower().replace(' ', '_')] = var
                
                cb = tk.Checkbutton(platforms_grid, text=platform, variable=var,
                                   bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                   font=FONT_SIZES['body'], activebackground=COLORS['hover'],
                                   selectcolor=COLORS['accent_primary'])
                cb.grid(row=i//2, column=i%2, sticky='w', padx=(0, 30), pady=5)
        
        # Control buttons with neumorphic style
        action_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        action_frame.pack(fill='x', pady=20, padx=10)
        
        self.bulk_scrape_btn = self.create_neumorphic_button(action_frame, "🚀 Start Bulk Scraping", 
                                                             self.start_bulk_scraping, COLORS['accent_primary'])
        self.bulk_scrape_btn.pack(side='left', padx=(0, 10))
        
        self.bulk_stop_btn = self.create_neumorphic_button(action_frame, "⏹️ Stop", 
                                                           self.stop_operation, COLORS['accent_danger'])
        self.bulk_stop_btn.pack(side='left', padx=(0, 10))
        self.bulk_stop_btn.config(state='disabled')
        
        export_btn = self.create_neumorphic_button(action_frame, "💾 Export Results", 
                                                   self.export_bulk_results, COLORS['accent_success'])
        export_btn.pack(side='right')
        
        # Progress and status with modern styling
        progress_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        progress_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.bulk_progress = ttk.Progressbar(progress_frame, mode='indeterminate', style='Modern.Horizontal.TProgressbar')
        self.bulk_progress.pack(fill='x', pady=(0, 10))
        
        self.bulk_status_var = tk.StringVar(value="Ready to scrape")
        status_label = tk.Label(progress_frame, textvariable=self.bulk_status_var,
                               bg=COLORS['bg_primary'], fg=COLORS['text_secondary'],
                               font=('Segoe UI', 10))
        status_label.pack()
        
    def create_email_generation_tab(self):
        """Create modern email generation tab with neumorphic design"""
        frame = tk.Frame(self.notebook, bg=COLORS['bg_primary'])
        self.notebook.add(frame, text='📧 Email Generation')
        
        # Scrollable container
        canvas = tk.Canvas(frame, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")
        
        # Generation Parameters Card
        params_card = self.create_neumorphic_card(scrollable_frame, "⚙️ Generation Parameters")
        params_card.pack(fill='x', pady=(0, 20), padx=10)
        
        params_inner = tk.Frame(params_card, bg=COLORS['bg_card'])
        params_inner.pack(fill='x', padx=20, pady=15)
        
        # Names input with modern styling
        names_frame = tk.Frame(params_inner, bg=COLORS['bg_card'])
        names_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(names_frame, text="Names (one per line):", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.names_text = scrolledtext.ScrolledText(names_frame, height=5,
                                                    bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                    font=('Segoe UI', 10), relief='solid', bd=1,
                                                    selectbackground=COLORS['accent_primary'],
                                                    selectforeground='white')
        self.names_text.pack(fill='x', pady=(0, 5))
        self.names_text.insert('1.0', "anna\njohn\nsarah\nmichael\ndavid\nemma\nliam\nolivia")
        
        # Region selection with modern combobox
        region_frame = tk.Frame(params_inner, bg=COLORS['bg_card'])
        region_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(region_frame, text="Target Region:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.email_region_var = tk.StringVar(value="global")
        region_combo = ttk.Combobox(region_frame, textvariable=self.email_region_var, 
                                   values=list(self.generator.regional_domains.keys()),
                                   font=('Segoe UI', 10), state='readonly')
        region_combo.pack(anchor='w', pady=(0, 5))
        
        # Email count with modern input
        count_frame = tk.Frame(params_inner, bg=COLORS['bg_card'])
        count_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(count_frame, text="Emails per Name:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.email_count_var = tk.IntVar(value=2000)
        count_entry = tk.Entry(count_frame, textvariable=self.email_count_var,
                              bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                              font=('Segoe UI', 11), relief='solid', bd=1, width=10,
                              highlightthickness=1, highlightcolor=COLORS['accent_primary'])
        count_entry.pack(anchor='w', ipady=5)
        
        # Options with modern checkboxes
        options_frame = tk.Frame(params_inner, bg=COLORS['bg_card'])
        options_frame.pack(fill='x')
        
        self.include_business_var = tk.BooleanVar(value=True)
        business_cb = tk.Checkbutton(options_frame, text="🏢 Include Business Domains", 
                                    variable=self.include_business_var,
                                    bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                    font=('Segoe UI', 10), activebackground=COLORS['hover'],
                                    selectcolor=COLORS['accent_primary'])
        business_cb.pack(anchor='w', pady=(0, 5))
        
        self.bulk_regions_var = tk.BooleanVar()
        regions_cb = tk.Checkbutton(options_frame, text="🌍 Generate for All Regions", 
                                   variable=self.bulk_regions_var,
                                   bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                   font=('Segoe UI', 10), activebackground=COLORS['hover'],
                                   selectcolor=COLORS['accent_primary'])
        regions_cb.pack(anchor='w', pady=(0, 5))
        
        self.include_numbers_var = tk.BooleanVar(value=True)
        numbers_cb = tk.Checkbutton(options_frame, text="🔢 Include Number Variations", 
                                   variable=self.include_numbers_var,
                                   bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                   font=('Segoe UI', 10), activebackground=COLORS['hover'],
                                   selectcolor=COLORS['accent_primary'])
        numbers_cb.pack(anchor='w')
        
        # Control buttons with neumorphic style
        action_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        action_frame.pack(fill='x', pady=20, padx=10)
        
        generate_btn = self.create_neumorphic_button(action_frame, "🎯 Generate Emails", 
                                                     self.generate_emails, COLORS['accent_primary'])
        generate_btn.pack(side='left', padx=(0, 10))
        
        preview_btn = self.create_neumorphic_button(action_frame, "📋 Preview Sample", 
                                                    self.preview_emails, COLORS['accent_secondary'])
        preview_btn.pack(side='left', padx=(0, 10))
        
        export_btn = self.create_neumorphic_button(action_frame, "💾 Export Emails", 
                                                   self.export_generated_emails, COLORS['accent_success'])
        export_btn.pack(side='right')
        
        # Preview section with modern styling
        preview_card = self.create_neumorphic_card(scrollable_frame, "👀 Email Preview")
        preview_card.pack(fill='both', expand=True, padx=10)
        
        preview_inner = tk.Frame(preview_card, bg=COLORS['bg_card'])
        preview_inner.pack(fill='both', expand=True, padx=20, pady=15)
        
        self.email_preview = scrolledtext.ScrolledText(preview_inner, height=15, 
                                                       bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                       font=('Consolas', 10), relief='solid', bd=1,
                                                       selectbackground=COLORS['accent_primary'],
                                                       selectforeground='white')
        self.email_preview.pack(fill='both', expand=True)
        
    def create_combined_tab(self):
        """Create modern combined operations tab with neumorphic design"""
        frame = tk.Frame(self.notebook, bg=COLORS['bg_primary'])
        self.notebook.add(frame, text='🔄 Combined Mode')
        
        # Scrollable container
        canvas = tk.Canvas(frame, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")
        
        # Operation Configuration Card
        config_card = self.create_neumorphic_card(scrollable_frame, "⚙️ Operation Configuration")
        config_card.pack(fill='x', pady=(0, 20), padx=10)
        
        config_inner = tk.Frame(config_card, bg=COLORS['bg_card'])
        config_inner.pack(fill='x', padx=20, pady=15)
        
        # Scraping query with modern styling
        query_frame = tk.Frame(config_inner, bg=COLORS['bg_card'])
        query_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(query_frame, text="Scraping Query:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.combined_query_var = tk.StringVar(value="tech startup")
        query_entry = tk.Entry(query_frame, textvariable=self.combined_query_var,
                              bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                              font=('Segoe UI', 11), relief='solid', bd=1,
                              highlightthickness=1, highlightcolor=COLORS['accent_primary'])
        query_entry.pack(fill='x', ipady=8)
        
        # Names for generation
        names_frame = tk.Frame(config_inner, bg=COLORS['bg_card'])
        names_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(names_frame, text="Names for Generation:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.combined_names_text = scrolledtext.ScrolledText(names_frame, height=4,
                                                             bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                             font=('Segoe UI', 10), relief='solid', bd=1,
                                                             selectbackground=COLORS['accent_primary'],
                                                             selectforeground='white')
        self.combined_names_text.pack(fill='x', pady=(0, 5))
        self.combined_names_text.insert('1.0', "anna\njohn\nsarah")
        
        # Region selection
        region_frame = tk.Frame(config_inner, bg=COLORS['bg_card'])
        region_frame.pack(fill='x')
        
        tk.Label(region_frame, text="Target Region:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.combined_region_var = tk.StringVar(value="global")
        region_combo = ttk.Combobox(region_frame, textvariable=self.combined_region_var, 
                                   values=list(self.generator.regional_domains.keys()),
                                   font=('Segoe UI', 10), state='readonly')
        region_combo.pack(anchor='w')
        
        # Control buttons with neumorphic style
        action_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        action_frame.pack(fill='x', pady=20, padx=10)
        
        self.combined_btn = self.create_neumorphic_button(action_frame, "🚀 Start Combined Operation", 
                                                          self.start_combined_operation, COLORS['accent_primary'])
        self.combined_btn.pack(side='left', padx=(0, 10))
        
        self.combined_stop_btn = self.create_neumorphic_button(action_frame, "⏹️ Stop Operation", 
                                                               self.stop_operation, COLORS['accent_danger'])
        self.combined_stop_btn.pack(side='left', padx=(0, 10))
        self.combined_stop_btn.config(state='disabled')
        
        # Progress and status with modern styling
        progress_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        progress_frame.pack(fill='x', padx=10, pady=(0, 20))
        
        self.combined_progress = ttk.Progressbar(progress_frame, mode='indeterminate', style='Modern.Horizontal.TProgressbar')
        self.combined_progress.pack(fill='x', pady=(0, 10))
        
        self.combined_status_var = tk.StringVar(value="Ready for combined operation")
        status_label = tk.Label(progress_frame, textvariable=self.combined_status_var,
                               bg=COLORS['bg_primary'], fg=COLORS['text_secondary'],
                               font=('Segoe UI', 10))
        status_label.pack()
        
        # Results summary with modern styling
        summary_card = self.create_neumorphic_card(scrollable_frame, "📈 Operation Summary")
        summary_card.pack(fill='both', expand=True, padx=10)
        
        summary_inner = tk.Frame(summary_card, bg=COLORS['bg_card'])
        summary_inner.pack(fill='both', expand=True, padx=20, pady=15)
        
        self.combined_summary = scrolledtext.ScrolledText(summary_inner, height=12, 
                                                          bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                          font=('Consolas', 10), relief='solid', bd=1,
                                                          selectbackground=COLORS['accent_primary'],
                                                          selectforeground='white')
        self.combined_summary.pack(fill='both', expand=True)
        
    def create_results_tab(self):
        """Create modern results viewing tab with neumorphic design"""
        frame = tk.Frame(self.notebook, bg=COLORS['bg_primary'])
        self.notebook.add(frame, text='📊 Results')
        
        # Scrollable container
        canvas = tk.Canvas(frame, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")
        
        # Control buttons with neumorphic style
        action_frame = tk.Frame(scrollable_frame, bg=COLORS['bg_primary'])
        action_frame.pack(fill='x', pady=(0, 20), padx=10)
        
        load_btn = self.create_neumorphic_button(action_frame, "📂 Load Results", 
                                                 self.load_results, COLORS['accent_secondary'])
        load_btn.pack(side='left', padx=(0, 10))
        
        refresh_btn = self.create_neumorphic_button(action_frame, "🔄 Refresh", 
                                                    self.refresh_results, COLORS['accent_primary'])
        refresh_btn.pack(side='left', padx=(0, 10))
        
        export_btn = self.create_neumorphic_button(action_frame, "💾 Export Selected", 
                                                   self.export_selected_results, COLORS['accent_success'])
        export_btn.pack(side='right')
        
        # Results display with modern styling
        results_card = self.create_neumorphic_card(scrollable_frame, "📋 Results Data")
        results_card.pack(fill='both', expand=True, padx=10, pady=(0, 20))
        
        results_inner = tk.Frame(results_card, bg=COLORS['bg_card'])
        results_inner.pack(fill='both', expand=True, padx=20, pady=15)
        
        # Create a frame for the treeview and scrollbars using grid
        tree_container = tk.Frame(results_inner, bg=COLORS['bg_card'])
        tree_container.pack(fill='both', expand=True)
        
        # Create modern treeview for results
        columns = ('Type', 'Platform', 'Name', 'Email', 'Company', 'Source')
        self.results_tree = ttk.Treeview(tree_container, columns=columns, show='headings', height=18)
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=150)
        
        # Scrollbars for treeview
        v_scrollbar = ttk.Scrollbar(tree_container, orient='vertical', command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient='horizontal', command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Place treeview and scrollbars in the container
        self.results_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        tree_container.grid_rowconfigure(0, weight=1)
        tree_container.grid_columnconfigure(0, weight=1)
        
        # Statistics panel with modern styling
        stats_card = self.create_neumorphic_card(scrollable_frame, "📊 Statistics")
        stats_card.pack(fill='x', padx=10)
        
        stats_inner = tk.Frame(stats_card, bg=COLORS['bg_card'])
        stats_inner.pack(fill='x', padx=20, pady=15)
        
        self.stats_text = tk.Text(stats_inner, height=4, wrap='word',
                                 bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                 font=('Consolas', 10), relief='solid', bd=1,
                                 selectbackground=COLORS['accent_primary'],
                                 selectforeground='white')
        self.stats_text.pack(fill='x')
        
    def create_settings_tab(self):
        """Create modern settings tab with neumorphic design"""
        frame = tk.Frame(self.notebook, bg=COLORS['bg_primary'])
        self.notebook.add(frame, text='⚙️ Settings')
        
        # Scrollable container
        canvas = tk.Canvas(frame, bg=COLORS['bg_primary'], highlightthickness=0)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=COLORS['bg_primary'])
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True, padx=20, pady=20)
        scrollbar.pack(side="right", fill="y")
        
        # Proxy Configuration Card
        proxy_card = self.create_neumorphic_card(scrollable_frame, "🔧 Proxy Configuration")
        proxy_card.pack(fill='x', pady=(0, 20), padx=10)
        
        proxy_inner = tk.Frame(proxy_card, bg=COLORS['bg_card'])
        proxy_inner.pack(fill='x', padx=20, pady=15)
        
        proxy_buttons_frame = tk.Frame(proxy_inner, bg=COLORS['bg_card'])
        proxy_buttons_frame.pack(fill='x')
        
        manage_proxy_btn = self.create_neumorphic_button(proxy_buttons_frame, "🔧 Manage Proxies", 
                                                         self.open_proxy_manager, COLORS['accent_primary'])
        manage_proxy_btn.pack(side='left', padx=(0, 10))
        
        test_proxy_btn = self.create_neumorphic_button(proxy_buttons_frame, "🧪 Test Proxies", 
                                                       self.test_proxies, COLORS['accent_secondary'])
        test_proxy_btn.pack(side='left')
        
        # Export Settings Card
        export_card = self.create_neumorphic_card(scrollable_frame, "💾 Export Settings")
        export_card.pack(fill='x', pady=(0, 20), padx=10)
        
        export_inner = tk.Frame(export_card, bg=COLORS['bg_card'])
        export_inner.pack(fill='x', padx=20, pady=15)
        
        format_frame = tk.Frame(export_inner, bg=COLORS['bg_card'])
        format_frame.pack(fill='x')
        
        tk.Label(format_frame, text="Default Export Format:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.export_format_var = tk.StringVar(value="JSON")
        format_combo = ttk.Combobox(format_frame, textvariable=self.export_format_var, 
                                   values=["JSON", "CSV", "Excel"], state='readonly',
                                   font=('Segoe UI', 10))
        format_combo.pack(anchor='w')
        
        # Application Settings Card
        app_card = self.create_neumorphic_card(scrollable_frame, "🎛️ Application Settings")
        app_card.pack(fill='x', pady=(0, 20), padx=10)
        
        app_inner = tk.Frame(app_card, bg=COLORS['bg_card'])
        app_inner.pack(fill='x', padx=20, pady=15)
        
        # Theme settings
        theme_frame = tk.Frame(app_inner, bg=COLORS['bg_card'])
        theme_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(theme_frame, text="Theme:", 
                bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                font=('Segoe UI', 10, 'bold')).pack(anchor='w', pady=(0, 5))
        
        self.theme_var = tk.StringVar(value="Dark")
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var, 
                                  values=["Dark", "Light"], state='readonly',
                                  font=('Segoe UI', 10))
        theme_combo.pack(anchor='w')
        
        # Auto-save settings
        autosave_frame = tk.Frame(app_inner, bg=COLORS['bg_card'])
        autosave_frame.pack(fill='x')
        
        self.autosave_var = tk.BooleanVar(value=True)
        autosave_cb = tk.Checkbutton(autosave_frame, text="🔄 Auto-save results", 
                                    variable=self.autosave_var,
                                    bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                    font=('Segoe UI', 10), activebackground=COLORS['hover'],
                                    selectcolor=COLORS['accent_primary'])
        autosave_cb.pack(anchor='w', pady=(0, 5))
        
        self.notifications_var = tk.BooleanVar(value=True)
        notif_cb = tk.Checkbutton(autosave_frame, text="🔔 Enable notifications", 
                                 variable=self.notifications_var,
                                 bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                 font=('Segoe UI', 10), activebackground=COLORS['hover'],
                                 selectcolor=COLORS['accent_primary'])
        notif_cb.pack(anchor='w')
        
        # Logging Card
        log_card = self.create_neumorphic_card(scrollable_frame, "📋 Application Logs")
        log_card.pack(fill='both', expand=True, padx=10)
        
        log_inner = tk.Frame(log_card, bg=COLORS['bg_card'])
        log_inner.pack(fill='both', expand=True, padx=20, pady=15)
        
        # Log controls
        log_controls = tk.Frame(log_inner, bg=COLORS['bg_card'])
        log_controls.pack(fill='x', pady=(0, 10))
        
        clear_log_btn = self.create_neumorphic_button(log_controls, "🗑️ Clear Logs", 
                                                      self.clear_logs, COLORS['accent_danger'])
        clear_log_btn.pack(side='left', padx=(0, 10))
        
        export_log_btn = self.create_neumorphic_button(log_controls, "💾 Export Logs", 
                                                       self.export_logs, COLORS['accent_success'])
        export_log_btn.pack(side='left')
        
        # Log display
        self.log_text = scrolledtext.ScrolledText(log_inner, height=12,
                                                  bg=COLORS['bg_card'], fg=COLORS['text_primary'],
                                                  font=('Consolas', 9), relief='solid', bd=1,
                                                  selectbackground=COLORS['accent_primary'],
                                                  selectforeground='white')
        self.log_text.pack(fill='both', expand=True)
        
    def setup_logging(self):
        """Setup logging to display in GUI"""
        # Custom handler to display logs in GUI
        class GUILogHandler:
            def __init__(self, text_widget):
                self.text_widget = text_widget
                
            def write(self, message):
                if message.strip():
                    self.text_widget.insert('end', message + '\n')
                    self.text_widget.see('end')
                    
        # Setup logger
        logger.remove()
        logger.add(GUILogHandler(self.log_text), level="INFO", 
                  format="{time:HH:mm:ss} | {level} | {message}")
        logger.add("bulk_scraper_gui.log", level="DEBUG", rotation="10 MB")
        
    def start_bulk_scraping(self):
        """Start bulk scraping operation"""
        if self.is_running:
            return
            
        query = self.bulk_query_var.get().strip()
        if not query:
            messagebox.showerror("Error", "Please enter a search query")
            return
            
        self.is_running = True
        self.bulk_scrape_btn.config(state='disabled')
        self.bulk_stop_btn.config(state='normal')
        self.bulk_progress.start()
        
        # Run in separate thread
        thread = threading.Thread(target=self._run_bulk_scraping)
        thread.daemon = True
        thread.start()
        
    def _run_bulk_scraping(self):
        """Run bulk scraping in background thread"""
        try:
            query = self.bulk_query_var.get().strip()
            location = self.bulk_location_var.get().strip()
            max_results = self.bulk_max_results_var.get()
            
            self.bulk_status_var.set("Starting bulk scraping...")
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run scraping
            results = loop.run_until_complete(
                self.scraper.bulk_scrape_all_platforms(query, location, max_results)
            )
            
            # Store results
            self.bulk_results = results
            
            # Update UI
            total_results = sum(len(contacts) for contacts in results.values())
            self.bulk_status_var.set(f"Completed! Found {total_results} contacts")
            
            # Update results tab
            self.root.after(0, self._update_results_display, results, 'scraped')
            
        except Exception as e:
            logger.error(f"Bulk scraping error: {str(e)}")
            self.bulk_status_var.set(f"Error: {str(e)}")
        finally:
            self.root.after(0, self._reset_bulk_ui)
            
    def generate_emails(self):
        """Generate email addresses"""
        names_text = self.names_text.get('1.0', 'end').strip()
        if not names_text:
            messagebox.showerror("Error", "Please enter at least one name")
            return
            
        names = [name.strip() for name in names_text.split('\n') if name.strip()]
        region = self.email_region_var.get()
        count = self.email_count_var.get()
        
        try:
            if self.bulk_regions_var.get():
                results = self.generator.generate_bulk_emails(names, emails_per_name=count)
            else:
                results = {}
                for name in names:
                    emails = self.generator.generate_email_variations(
                        name, region, count, self.include_business_var.get()
                    )
                    results[name] = emails
            
            self.generated_emails = results
            
            # Display in preview
            self.email_preview.delete('1.0', 'end')
            total_emails = 0
            
            for name, emails in results.items():
                total_emails += len(emails)
                self.email_preview.insert('end', f"\n=== {name.upper()} ({len(emails)} emails) ===\n")
                for i, email in enumerate(emails[:20], 1):
                    self.email_preview.insert('end', f"{i:3d}. {email}\n")
                if len(emails) > 20:
                    self.email_preview.insert('end', f"    ... and {len(emails) - 20} more\n")
            
            self.email_preview.insert('1.0', f"Generated {total_emails} total emails\n{'='*50}\n")
            
            messagebox.showinfo("Success", f"Generated {total_emails} email addresses!")
            
        except Exception as e:
            logger.error(f"Email generation error: {str(e)}")
            messagebox.showerror("Error", f"Email generation failed: {str(e)}")
            
    def preview_emails(self):
        """Preview a small sample of generated emails"""
        names_text = self.names_text.get('1.0', 'end').strip()
        if not names_text:
            messagebox.showerror("Error", "Please enter at least one name")
            return
            
        names = [name.strip() for name in names_text.split('\n') if name.strip()]
        region = self.email_region_var.get()
        
        self.email_preview.delete('1.0', 'end')
        self.email_preview.insert('1.0', f"Email Preview for Region: {region}\n{'='*50}\n\n")
        
        for name in names[:3]:  # Preview first 3 names
            emails = self.generator.generate_email_variations(name, region, 20, True)
            self.email_preview.insert('end', f"=== {name.upper()} (sample) ===\n")
            for i, email in enumerate(emails[:10], 1):
                self.email_preview.insert('end', f"{i:2d}. {email}\n")
            self.email_preview.insert('end', "\n")
            
    def start_combined_operation(self):
        """Start combined scraping and email generation"""
        if self.is_running:
            return
            
        query = self.combined_query_var.get().strip()
        names_text = self.combined_names_text.get('1.0', 'end').strip()
        
        if not query or not names_text:
            messagebox.showerror("Error", "Please enter both query and names")
            return
            
        self.is_running = True
        self.combined_btn.config(state='disabled')
        self.combined_stop_btn.config(state='normal')
        self.combined_progress.start()
        
        # Run in separate thread
        thread = threading.Thread(target=self._run_combined_operation)
        thread.daemon = True
        thread.start()
        
    def _run_combined_operation(self):
        """Run combined operation in background thread"""
        try:
            query = self.combined_query_var.get().strip()
            names_text = self.combined_names_text.get('1.0', 'end').strip()
            names = [name.strip() for name in names_text.split('\n') if name.strip()]
            region = self.combined_region_var.get()
            
            self.combined_status_var.set("Running combined operation...")
            
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run combined operation
            results = loop.run_until_complete(
                self.scraper.scrape_with_generated_emails(query, names, region)
            )
            
            # Store results
            self.combined_results = results
            
            # Update UI
            summary = f"""Combined Operation Completed!
            
Query: {results['query']}
Region: {results['region']}
Timestamp: {results['timestamp']}

Results Summary:
- Scraped Contacts: {results['total_scraped']}
- Generated Emails: {results['total_generated']}

Platform Breakdown:
"""
            
            for platform, contacts in results['scraped_contacts'].items():
                summary += f"- {platform}: {len(contacts)} contacts\n"
                
            summary += "\nGenerated Email Breakdown:\n"
            for name, emails in results['generated_emails'].items():
                summary += f"- {name}: {len(emails)} emails\n"
                
            self.root.after(0, self._update_combined_summary, summary)
            self.combined_status_var.set("Combined operation completed!")
            
        except Exception as e:
            logger.error(f"Combined operation error: {str(e)}")
            self.combined_status_var.set(f"Error: {str(e)}")
        finally:
            self.root.after(0, self._reset_combined_ui)
            
    def _update_combined_summary(self, summary):
        """Update combined operation summary"""
        self.combined_summary.delete('1.0', 'end')
        self.combined_summary.insert('1.0', summary)
        
    def _reset_bulk_ui(self):
        """Reset bulk scraping UI"""
        self.is_running = False
        self.bulk_scrape_btn.config(state='normal')
        self.bulk_stop_btn.config(state='disabled')
        self.bulk_progress.stop()
        
    def _reset_combined_ui(self):
        """Reset combined operation UI"""
        self.is_running = False
        self.combined_btn.config(state='normal')
        self.combined_stop_btn.config(state='disabled')
        self.combined_progress.stop()
        
    def _update_results_display(self, results, result_type):
        """Update results display in results tab"""
        # Clear existing results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
            
        # Add new results
        total_count = 0
        for category, contacts in results.items():
            for contact in contacts:
                self.results_tree.insert('', 'end', values=(
                    result_type,
                    contact.get('platform', category),
                    contact.get('name', ''),
                    contact.get('email', ''),
                    contact.get('company', ''),
                    contact.get('source_url', '')
                ))
                total_count += 1
                
        # Update statistics
        stats = f"Total Results: {total_count}\n"
        stats += f"Categories: {len(results)}\n"
        stats += f"Type: {result_type}\n"
        stats += f"Last Updated: {tk.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        self.stats_text.delete('1.0', 'end')
        self.stats_text.insert('1.0', stats)
        
    def stop_operation(self):
        """Stop current operation"""
        self.is_running = False
        logger.info("Operation stopped by user")
        
    def export_bulk_results(self):
        """Export bulk scraping results"""
        if not hasattr(self, 'bulk_results'):
            messagebox.showwarning("Warning", "No bulk results to export")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                if filename.endswith('.csv'):
                    self._export_to_csv(self.bulk_results, filename)
                else:
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(self.bulk_results, f, indent=2, ensure_ascii=False)
                        
                messagebox.showinfo("Success", f"Results exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {str(e)}")
                
    def export_generated_emails(self):
        """Export generated emails"""
        if not hasattr(self, 'generated_emails'):
            messagebox.showwarning("Warning", "No generated emails to export")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                if filename.endswith('.csv'):
                    self.generator.export_to_csv(self.generated_emails, filename)
                else:
                    self.generator.save_generated_emails(self.generated_emails, filename)
                    
                messagebox.showinfo("Success", f"Emails exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {str(e)}")
                
    def _export_to_csv(self, results, filename):
        """Export results to CSV format"""
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Platform', 'Category', 'Name', 'Email', 'Company', 'Phone', 'Address', 'Source_URL'])
            
            for category, contacts in results.items():
                for contact in contacts:
                    writer.writerow([
                        contact.get('platform', ''),
                        category,
                        contact.get('name', ''),
                        contact.get('email', ''),
                        contact.get('company', ''),
                        contact.get('phone', ''),
                        contact.get('address', ''),
                        contact.get('source_url', '')
                    ])
                    
    def load_results(self):
        """Load results from file"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                    
                self._update_results_display(results, 'loaded')
                messagebox.showinfo("Success", f"Results loaded from {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load results: {str(e)}")
                
    def refresh_results(self):
        """Refresh results display"""
        if hasattr(self, 'bulk_results'):
            self._update_results_display(self.bulk_results, 'scraped')
        elif hasattr(self, 'combined_results'):
            self._update_results_display(self.combined_results['scraped_contacts'], 'combined')
            
    def export_selected_results(self):
        """Export selected results from treeview"""
        selected_items = self.results_tree.selection()
        if not selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("JSON files", "*.json")]
        )
        
        if filename:
            try:
                selected_data = []
                for item in selected_items:
                    values = self.results_tree.item(item)['values']
                    selected_data.append(values)
                    
                with open(filename, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Type', 'Platform', 'Name', 'Email', 'Company', 'Source'])
                    writer.writerows(selected_data)
                    
                messagebox.showinfo("Success", f"Selected results exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {str(e)}")
                
    def open_proxy_manager(self):
        """Open proxy manager"""
        import subprocess
        try:
            subprocess.Popen(["python", "proxy_manager.py"])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open proxy manager: {str(e)}")
            
    def test_proxies(self):
        """Test configured proxies"""
        try:
            config = get_config()
            proxies = config.get('proxies', [])
            
            if not proxies:
                messagebox.showinfo("Info", "No proxies configured")
                return
                
            # Test proxies in background
            thread = threading.Thread(target=self._test_proxies_background, args=(proxies,))
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Proxy test failed: {str(e)}")
            
    def _test_proxies_background(self, proxies):
        """Test proxies in background thread"""
        working_count = 0
        total_count = len(proxies)
        
        for i, proxy in enumerate(proxies, 1):
            try:
                # Simple proxy test (you can implement actual testing logic)
                logger.info(f"Testing proxy {i}/{total_count}: {proxy.get('host', 'unknown')}")
                # Add actual proxy testing logic here
                working_count += 1
            except:
                pass
                
        result_msg = f"Proxy Test Results:\nTotal: {total_count}\nWorking: {working_count}\nFailed: {total_count - working_count}"
        self.root.after(0, lambda: messagebox.showinfo("Proxy Test Results", result_msg))
    
    def clear_logs(self):
        """Clear the log display"""
        if hasattr(self, 'log_text'):
            self.log_text.delete('1.0', 'end')
        
    def export_logs(self):
        """Export logs to file"""
        if not hasattr(self, 'log_text'):
            messagebox.showwarning("Warning", "No logs available")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".log",
            filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get('1.0', 'end'))
                messagebox.showinfo("Success", f"Logs exported to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {str(e)}")
    
    def toggle_theme(self):
        """Toggle between light and dark theme"""
        current_theme = self.theme_var.get()
        if current_theme == "dark":
            # Switch to light theme
            self.COLORS = {
                'bg': '#f0f0f0',
                'card_bg': '#ffffff',
                'primary': '#2196F3',
                'secondary': '#FFC107',
                'success': '#4CAF50',
                'danger': '#F44336',
                'text': '#333333',
                'text_secondary': '#666666',
                'border': '#e0e0e0',
                'shadow_light': '#ffffff',
                'shadow_dark': '#d0d0d0'
            }
            self.theme_var.set("light")
        else:
            # Switch to dark theme
            self.COLORS = {
                'bg': '#2b2b2b',
                'card_bg': '#3c3c3c',
                'primary': '#1976D2',
                'secondary': '#FF9800',
                'success': '#388E3C',
                'danger': '#D32F2F',
                'text': '#ffffff',
                'text_secondary': '#cccccc',
                'border': '#555555',
                'shadow_light': '#4a4a4a',
                'shadow_dark': '#1a1a1a'
            }
            self.theme_var.set("dark")
        
        # Update GUI colors
        self.root.configure(bg=self.COLORS['bg'])
        messagebox.showinfo("Theme Changed", f"Theme switched to {self.theme_var.get()} mode")
    
    def toggle_auto_save(self):
        """Toggle auto-save functionality"""
        if self.auto_save_var.get():
            messagebox.showinfo("Auto-Save", "Auto-save enabled")
        else:
            messagebox.showinfo("Auto-Save", "Auto-save disabled")
    
    def toggle_notifications(self):
        """Toggle notifications"""
        if self.notifications_var.get():
            messagebox.showinfo("Notifications", "Notifications enabled")
        else:
            messagebox.showinfo("Notifications", "Notifications disabled")

def main():
    root = tk.Tk()
    app = BulkScraperGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()