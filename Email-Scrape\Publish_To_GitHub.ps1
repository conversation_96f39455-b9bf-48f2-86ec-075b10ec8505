# BMD EMAIL SCRAPPER - GitHub Publishing Script
# This script helps automate the GitHub publishing process

Write-Host "=================================================" -ForegroundColor Cyan
Write-Host "    BMD EMAIL SCRAPPER - GitHub Publisher    " -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Host "❌ Error: Not in a git repository!" -ForegroundColor Red
    Write-Host "Please run this script from the project root directory." -ForegroundColor Yellow
    exit 1
}

# Check git status
Write-Host "📋 Checking git status..." -ForegroundColor Yellow
$gitStatus = git status --porcelain
if ($gitStatus) {
    Write-Host "⚠️  Warning: You have uncommitted changes:" -ForegroundColor Yellow
    git status --short
    Write-Host ""
    $commit = Read-Host "Do you want to commit these changes first? (y/n)"
    if ($commit -eq 'y' -or $commit -eq 'Y') {
        $message = Read-Host "Enter commit message"
        git add .
        git commit -m "$message"
        Write-Host "✅ Changes committed!" -ForegroundColor Green
    }
}

# Get GitHub username
Write-Host "👤 GitHub Setup" -ForegroundColor Cyan
$username = Read-Host "Enter your GitHub username"
if (-not $username) {
    Write-Host "❌ GitHub username is required!" -ForegroundColor Red
    exit 1
}

# Repository name
$repoName = "bmd-email-scrapper"
Write-Host "📁 Repository name: $repoName" -ForegroundColor Green

# Check if remote already exists
$remoteExists = git remote get-url origin 2>$null
if ($remoteExists) {
    Write-Host "🔗 Remote 'origin' already exists: $remoteExists" -ForegroundColor Yellow
    $overwrite = Read-Host "Do you want to update it? (y/n)"
    if ($overwrite -eq 'y' -or $overwrite -eq 'Y') {
        git remote remove origin
        Write-Host "🗑️  Removed existing remote" -ForegroundColor Yellow
    } else {
        Write-Host "⏭️  Keeping existing remote" -ForegroundColor Blue
    }
}

# Add remote if not exists
if (-not (git remote get-url origin 2>$null)) {
    $repoUrl = "https://github.com/$username/$repoName.git"
    Write-Host "🔗 Adding remote: $repoUrl" -ForegroundColor Blue
    git remote add origin $repoUrl
    Write-Host "✅ Remote added successfully!" -ForegroundColor Green
}

# Set main branch
Write-Host "🌿 Setting up main branch..." -ForegroundColor Yellow
git branch -M main

# Display next steps
Write-Host ""
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host "           NEXT STEPS - MANUAL ACTION           " -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🌐 Go to GitHub.com and create a new repository:" -ForegroundColor Yellow
Write-Host "   Repository name: $repoName" -ForegroundColor White
Write-Host "   Description: Enhanced Bulk Email Scraping & Generation Tool with Advanced GUI" -ForegroundColor White
Write-Host "   ⚠️  DO NOT initialize with README, .gitignore, or license" -ForegroundColor Red
Write-Host ""
Write-Host "2. 🚀 After creating the repository, run this command:" -ForegroundColor Yellow
Write-Host "   git push -u origin main" -ForegroundColor Green
Write-Host ""
Write-Host "3. 📝 Configure your repository:" -ForegroundColor Yellow
Write-Host "   - Add topics: email-scraping, gui-application, python, tkinter" -ForegroundColor White
Write-Host "   - Enable Issues and Discussions" -ForegroundColor White
Write-Host "   - Create your first release (v2.0.0)" -ForegroundColor White
Write-Host ""

# Ask if user wants to push now
$pushNow = Read-Host "Have you created the GitHub repository? Push now? (y/n)"
if ($pushNow -eq 'y' -or $pushNow -eq 'Y') {
    Write-Host "🚀 Pushing to GitHub..." -ForegroundColor Blue
    try {
        git push -u origin main
        Write-Host ""
        Write-Host "🎉 SUCCESS! Your BMD EMAIL SCRAPPER is now on GitHub!" -ForegroundColor Green
        Write-Host "🔗 Repository URL: https://github.com/$username/$repoName" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "📋 Don't forget to:" -ForegroundColor Yellow
        Write-Host "   ✅ Add repository description and topics" -ForegroundColor White
        Write-Host "   ✅ Create your first release" -ForegroundColor White
        Write-Host "   ✅ Enable Issues and Discussions" -ForegroundColor White
        Write-Host "   ✅ Share your project with the community!" -ForegroundColor White
    }
    catch {
        Write-Host "❌ Error pushing to GitHub:" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 Make sure you:" -ForegroundColor Yellow
        Write-Host "   1. Created the repository on GitHub" -ForegroundColor White
        Write-Host "   2. Have the correct permissions" -ForegroundColor White
        Write-Host "   3. Are logged into Git (git config --global user.name)" -ForegroundColor White
    }
} else {
    Write-Host "⏸️  Push postponed. Run 'git push -u origin main' when ready." -ForegroundColor Blue
}

Write-Host ""
Write-Host "📖 For detailed instructions, see GITHUB_SETUP.md" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

Pause