@echo off
color 0A
echo ================================================
echo     BMD EMAIL SCRAPPER - GitHub Publisher
echo ================================================
echo.

REM Check if we're in a git repository
if not exist ".git" (
    echo [ERROR] Not in a git repository!
    echo Please run this script from the project root directory.
    pause
    exit /b 1
)

echo [INFO] Checking git status...
git status --short
echo.

echo [SETUP] GitHub Repository Setup
set /p username="Enter your GitHub username: "
if "%username%"=="" (
    echo [ERROR] GitHub username is required!
    pause
    exit /b 1
)

set repoName=bmd-email-scrapper
echo [INFO] Repository name: %repoName%
echo.

REM Check if remote exists
git remote get-url origin >nul 2>&1
if %errorlevel% equ 0 (
    echo [WARNING] Remote 'origin' already exists
    set /p overwrite="Do you want to update it? (y/n): "
    if /i "%overwrite%"=="y" (
        git remote remove origin
        echo [INFO] Removed existing remote
    )
)

REM Add remote
git remote get-url origin >nul 2>&1
if %errorlevel% neq 0 (
    set repoUrl=https://github.com/%username%/%repoName%.git
    echo [INFO] Adding remote: !repoUrl!
    git remote add origin https://github.com/%username%/%repoName%.git
    echo [SUCCESS] Remote added successfully!
)

echo [INFO] Setting up main branch...
git branch -M main
echo.

echo ================================================
echo            NEXT STEPS - MANUAL ACTION
echo ================================================
echo.
echo 1. Go to GitHub.com and create a new repository:
echo    Repository name: %repoName%
echo    Description: Enhanced Bulk Email Scraping ^& Generation Tool with Advanced GUI
echo    WARNING: DO NOT initialize with README, .gitignore, or license
echo.
echo 2. After creating the repository, this script will push your code
echo.
echo 3. Configure your repository:
echo    - Add topics: email-scraping, gui-application, python, tkinter
echo    - Enable Issues and Discussions
echo    - Create your first release (v2.0.0)
echo.

set /p pushNow="Have you created the GitHub repository? Push now? (y/n): "
if /i "%pushNow%"=="y" (
    echo [INFO] Pushing to GitHub...
    git push -u origin main
    if %errorlevel% equ 0 (
        echo.
        echo [SUCCESS] Your BMD EMAIL SCRAPPER is now on GitHub!
        echo Repository URL: https://github.com/%username%/%repoName%
        echo.
        echo Don't forget to:
        echo   - Add repository description and topics
        echo   - Create your first release
        echo   - Enable Issues and Discussions
        echo   - Share your project with the community!
    ) else (
        echo.
        echo [ERROR] Failed to push to GitHub
        echo Make sure you:
        echo   1. Created the repository on GitHub
        echo   2. Have the correct permissions
        echo   3. Are logged into Git
    )
) else (
    echo [INFO] Push postponed. Run 'git push -u origin main' when ready.
)

echo.
echo For detailed instructions, see GITHUB_SETUP.md
echo ================================================
pause