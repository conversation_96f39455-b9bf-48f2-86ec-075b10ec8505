#!/usr/bin/env python3
"""
Direct GUI Test - Bypass consent screen for testing
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_basic_gui():
    """Test basic GUI functionality"""
    print("Testing basic GUI...")
    
    # Create test window
    root = tk.Tk()
    root.title("GUI Test - BMD EMAIL SCRAPPER")
    root.geometry("400x300")
    root.configure(bg='black')
    
    # Force window to be visible
    root.lift()
    root.attributes('-topmost', True)
    root.focus_force()
    root.after(2000, lambda: root.attributes('-topmost', False))
    
    # Add test content
    label = tk.Label(root, text="GUI Test Successful!", 
                    bg='black', fg='green', 
                    font=('Arial', 16, 'bold'))
    label.pack(pady=50)
    
    def launch_main_app():
        root.destroy()
        try:
            # Import and modify bulk_gui to skip consent
            import bulk_gui
            
            # Create main app
            main_root = tk.Tk()
            app = bulk_gui.BulkScraperGUI(main_root)
            
            # Force consent to be accepted
            app.consent_given = True
            
            # Close consent window if it exists
            if hasattr(app, 'consent_window'):
                try:
                    app.consent_window.destroy()
                except:
                    pass
            
            # Show main window
            app.root.deiconify()
            app.setup_main_interface()
            
            # Force visibility
            main_root.lift()
            main_root.attributes('-topmost', True)
            main_root.focus_force()
            main_root.after(3000, lambda: main_root.attributes('-topmost', False))
            
            main_root.mainloop()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch main app: {e}")
            print(f"Error: {e}")
    
    btn = tk.Button(root, text="Launch Main App", 
                   command=launch_main_app,
                   bg='green', fg='white', 
                   font=('Arial', 12, 'bold'),
                   padx=20, pady=10)
    btn.pack(pady=20)
    
    quit_btn = tk.Button(root, text="Quit", 
                        command=root.quit,
                        bg='red', fg='white', 
                        font=('Arial', 12, 'bold'),
                        padx=20, pady=10)
    quit_btn.pack(pady=10)
    
    print("Test window should be visible now!")
    root.mainloop()

if __name__ == "__main__":
    print("Starting GUI test...")
    test_basic_gui()