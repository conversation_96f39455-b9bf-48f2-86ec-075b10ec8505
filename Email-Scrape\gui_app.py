#!/usr/bin/env python3
"""
Email Scraper Pro - GUI Application
Advanced Lead Generation Tool with Modern UI
"""

import asyncio
import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from typing import List, Dict
import os
import sys
from datetime import datetime

# Import our custom modules
from email_scraper import WebScraper, DataManager, Contact
from loguru import logger

# Configure CustomTkinter
ctk.set_appearance_mode("dark")  # Modes: system (default), light, dark
ctk.set_default_color_theme("blue")  # Themes: blue (default), green, dark-blue

class EmailScraperGUI:
    """Main GUI application for Email Scraper Pro"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Email Scraper Pro - Advanced Lead Generation Tool")
        self.root.geometry("1200x800")
        
        # Initialize components
        self.scraper = WebScraper()
        self.data_manager = DataManager()
        self.is_scraping = False
        
        # Create GUI elements
        self.create_widgets()
        
        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
    
    def create_widgets(self):
        """Create and arrange GUI widgets"""
        
        # Left sidebar for controls
        self.sidebar_frame = ctk.CTkFrame(self.root, width=300, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)
        
        # Logo and title
        self.logo_label = ctk.CTkLabel(self.sidebar_frame, text="Email Scraper Pro", 
                                      font=ctk.CTkFont(size=20, weight="bold"))
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # Search type selection
        self.search_type_label = ctk.CTkLabel(self.sidebar_frame, text="Search Type:", 
                                             font=ctk.CTkFont(size=14, weight="bold"))
        self.search_type_label.grid(row=1, column=0, padx=20, pady=(20, 5), sticky="w")
        
        self.search_type_var = ctk.StringVar(value="Google Search")
        self.search_type_menu = ctk.CTkOptionMenu(self.sidebar_frame, 
                                                 values=["Google Search", "LinkedIn Profiles", "Website URLs"],
                                                 variable=self.search_type_var,
                                                 command=self.on_search_type_change)
        self.search_type_menu.grid(row=2, column=0, padx=20, pady=5, sticky="ew")
        
        # Input frame
        self.input_frame = ctk.CTkFrame(self.sidebar_frame)
        self.input_frame.grid(row=3, column=0, padx=20, pady=10, sticky="ew")
        
        # Query input
        self.query_label = ctk.CTkLabel(self.input_frame, text="Search Query/URLs:", 
                                       font=ctk.CTkFont(size=12, weight="bold"))
        self.query_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        self.query_text = ctk.CTkTextbox(self.input_frame, height=100, width=250)
        self.query_text.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        self.query_text.insert("0.0", "Enter search keywords or URLs (one per line)")
        
        # Advanced options
        self.options_label = ctk.CTkLabel(self.input_frame, text="Options:", 
                                         font=ctk.CTkFont(size=12, weight="bold"))
        self.options_label.grid(row=2, column=0, padx=10, pady=(15, 5), sticky="w")
        
        # Max pages option
        self.max_pages_label = ctk.CTkLabel(self.input_frame, text="Max Pages:")
        self.max_pages_label.grid(row=3, column=0, padx=10, pady=2, sticky="w")
        
        self.max_pages_var = ctk.StringVar(value="5")
        self.max_pages_entry = ctk.CTkEntry(self.input_frame, textvariable=self.max_pages_var, width=60)
        self.max_pages_entry.grid(row=4, column=0, padx=10, pady=2, sticky="w")
        
        # Headless browser option
        self.headless_var = ctk.BooleanVar(value=True)
        self.headless_checkbox = ctk.CTkCheckBox(self.input_frame, text="Headless Browser", 
                                                variable=self.headless_var)
        self.headless_checkbox.grid(row=5, column=0, padx=10, pady=5, sticky="w")
        
        # Control buttons
        self.start_button = ctk.CTkButton(self.sidebar_frame, text="Start Scraping", 
                                         command=self.start_scraping,
                                         font=ctk.CTkFont(size=14, weight="bold"),
                                         height=40)
        self.start_button.grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        
        self.stop_button = ctk.CTkButton(self.sidebar_frame, text="Stop Scraping", 
                                        command=self.stop_scraping,
                                        font=ctk.CTkFont(size=14, weight="bold"),
                                        height=40, state="disabled")
        self.stop_button.grid(row=6, column=0, padx=20, pady=5, sticky="ew")
        
        # Export buttons
        self.export_csv_button = ctk.CTkButton(self.sidebar_frame, text="Export to CSV", 
                                              command=self.export_csv)
        self.export_csv_button.grid(row=7, column=0, padx=20, pady=5, sticky="ew")
        
        self.export_excel_button = ctk.CTkButton(self.sidebar_frame, text="Export to Excel", 
                                                 command=self.export_excel)
        self.export_excel_button.grid(row=8, column=0, padx=20, pady=5, sticky="ew")
        
        # Clear data button
        self.clear_button = ctk.CTkButton(self.sidebar_frame, text="Clear All Data", 
                                         command=self.clear_data,
                                         fg_color="red", hover_color="darkred")
        self.clear_button.grid(row=9, column=0, padx=20, pady=(20, 10), sticky="ew")
        
        # Main content area
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.grid(row=0, column=1, padx=(20, 20), pady=(20, 0), sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # Status and progress
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.grid(row=0, column=0, padx=20, pady=20, sticky="ew")
        self.status_frame.grid_columnconfigure(1, weight=1)
        
        self.status_label = ctk.CTkLabel(self.status_frame, text="Status: Ready", 
                                        font=ctk.CTkFont(size=14, weight="bold"))
        self.status_label.grid(row=0, column=0, padx=10, pady=10, sticky="w")
        
        self.progress_bar = ctk.CTkProgressBar(self.status_frame)
        self.progress_bar.grid(row=0, column=1, padx=10, pady=10, sticky="ew")
        self.progress_bar.set(0)
        
        self.contacts_count_label = ctk.CTkLabel(self.status_frame, text="Contacts Found: 0")
        self.contacts_count_label.grid(row=0, column=2, padx=10, pady=10, sticky="e")
        
        # Results table
        self.create_results_table()
        
        # Log area
        self.log_frame = ctk.CTkFrame(self.root)
        self.log_frame.grid(row=1, column=1, padx=(20, 20), pady=20, sticky="ew")
        self.log_frame.grid_columnconfigure(0, weight=1)
        
        self.log_label = ctk.CTkLabel(self.log_frame, text="Activity Log:", 
                                     font=ctk.CTkFont(size=12, weight="bold"))
        self.log_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        self.log_text = ctk.CTkTextbox(self.log_frame, height=150)
        self.log_text.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="ew")
        
        # Load existing contacts
        self.refresh_results_table()
    
    def create_results_table(self):
        """Create the results table using Treeview"""
        self.table_frame = ctk.CTkFrame(self.main_frame)
        self.table_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        self.table_frame.grid_columnconfigure(0, weight=1)
        self.table_frame.grid_rowconfigure(0, weight=1)
        
        # Create Treeview
        columns = ('Email', 'Name', 'Company', 'Title', 'Source', 'Date')
        self.tree = ttk.Treeview(self.table_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        self.tree.heading('Email', text='Email')
        self.tree.heading('Name', text='Name')
        self.tree.heading('Company', text='Company')
        self.tree.heading('Title', text='Title')
        self.tree.heading('Source', text='Source')
        self.tree.heading('Date', text='Date')
        
        # Configure column widths
        self.tree.column('Email', width=200)
        self.tree.column('Name', width=150)
        self.tree.column('Company', width=150)
        self.tree.column('Title', width=150)
        self.tree.column('Source', width=100)
        self.tree.column('Date', width=100)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(self.table_frame, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self.table_frame, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid the treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
    
    def on_search_type_change(self, choice):
        """Handle search type selection change"""
        if choice == "Google Search":
            self.query_text.delete("0.0", "end")
            self.query_text.insert("0.0", "Enter search keywords (one per line)\ne.g., 'marketing manager email'")
        elif choice == "LinkedIn Profiles":
            self.query_text.delete("0.0", "end")
            self.query_text.insert("0.0", "Enter LinkedIn profile URLs (one per line)\ne.g., https://linkedin.com/in/username")
        elif choice == "Website URLs":
            self.query_text.delete("0.0", "end")
            self.query_text.insert("0.0", "Enter website URLs (one per line)\ne.g., https://company.com")
    
    def log_message(self, message: str):
        """Add message to log area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.root.update_idletasks()
    
    def update_status(self, status: str, progress: float = None):
        """Update status label and progress bar"""
        self.status_label.configure(text=f"Status: {status}")
        if progress is not None:
            self.progress_bar.set(progress)
        self.root.update_idletasks()
    
    def update_contacts_count(self):
        """Update the contacts count display"""
        contacts = self.data_manager.get_all_contacts()
        self.contacts_count_label.configure(text=f"Contacts Found: {len(contacts)}")
    
    def refresh_results_table(self):
        """Refresh the results table with current data"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Load contacts from database
        contacts = self.data_manager.get_all_contacts()
        
        # Add contacts to table
        for contact in contacts:
            self.tree.insert('', 'end', values=(
                contact.email,
                contact.name,
                contact.company,
                contact.title,
                contact.source,
                contact.extracted_date.split('T')[0] if contact.extracted_date else ''
            ))
        
        self.update_contacts_count()
    
    def start_scraping(self):
        """Start the scraping process"""
        if self.is_scraping:
            return
        
        # Get input parameters
        search_type = self.search_type_var.get()
        query_text = self.query_text.get("0.0", "end").strip()
        
        if not query_text or query_text.startswith("Enter"):
            messagebox.showerror("Error", "Please enter search queries or URLs")
            return
        
        try:
            max_pages = int(self.max_pages_var.get())
        except ValueError:
            messagebox.showerror("Error", "Max pages must be a number")
            return
        
        headless = self.headless_var.get()
        
        # Parse input
        queries = [line.strip() for line in query_text.split('\n') if line.strip()]
        
        # Update UI state
        self.is_scraping = True
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.update_status("Starting scraping...", 0)
        self.log_message(f"Starting {search_type} scraping with {len(queries)} queries")
        
        # Start scraping in separate thread
        thread = threading.Thread(target=self.run_scraping, 
                                 args=(search_type, queries, max_pages, headless))
        thread.daemon = True
        thread.start()
    
    def run_scraping(self, search_type: str, queries: List[str], max_pages: int, headless: bool):
        """Run scraping in separate thread"""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the async scraping
            loop.run_until_complete(self.async_scraping(search_type, queries, max_pages, headless))
            
        except Exception as e:
            self.log_message(f"Scraping error: {str(e)}")
            logger.error(f"Scraping error: {e}")
        finally:
            # Reset UI state
            self.is_scraping = False
            self.start_button.configure(state="normal")
            self.stop_button.configure(state="disabled")
            self.update_status("Scraping completed", 1.0)
    
    async def async_scraping(self, search_type: str, queries: List[str], max_pages: int, headless: bool):
        """Async scraping function"""
        all_contacts = []
        
        try:
            await self.scraper.init_browser(headless=headless)
            
            total_queries = len(queries)
            
            for i, query in enumerate(queries):
                if not self.is_scraping:
                    break
                
                self.log_message(f"Processing: {query}")
                progress = i / total_queries
                self.update_status(f"Processing {i+1}/{total_queries}", progress)
                
                contacts = []
                
                if search_type == "Google Search":
                    contacts = await self.scraper.google_search_scrape(query, max_pages)
                elif search_type == "LinkedIn Profiles":
                    contacts = await self.scraper.linkedin_profile_scrape([query])
                elif search_type == "Website URLs":
                    contacts = await self.scraper.website_scrape([query])
                
                if contacts:
                    saved_count = self.data_manager.save_contacts(contacts)
                    self.log_message(f"Found {len(contacts)} contacts, saved {saved_count} new ones")
                    all_contacts.extend(contacts)
                    
                    # Update table in real-time
                    self.root.after(0, self.refresh_results_table)
                else:
                    self.log_message(f"No contacts found for: {query}")
            
            self.log_message(f"Scraping completed. Total contacts found: {len(all_contacts)}")
            
        except Exception as e:
            self.log_message(f"Scraping error: {str(e)}")
            raise
        finally:
            await self.scraper.close_browser()
    
    def stop_scraping(self):
        """Stop the scraping process"""
        self.is_scraping = False
        self.log_message("Stopping scraping...")
        self.update_status("Stopping...", None)
    
    def export_csv(self):
        """Export contacts to CSV file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="Save CSV file"
        )
        
        if filename:
            if self.data_manager.export_to_csv(filename):
                messagebox.showinfo("Success", f"Contacts exported to {filename}")
                self.log_message(f"Exported contacts to CSV: {filename}")
            else:
                messagebox.showerror("Error", "Failed to export CSV file")
    
    def export_excel(self):
        """Export contacts to Excel file"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
            title="Save Excel file"
        )
        
        if filename:
            if self.data_manager.export_to_excel(filename):
                messagebox.showinfo("Success", f"Contacts exported to {filename}")
                self.log_message(f"Exported contacts to Excel: {filename}")
            else:
                messagebox.showerror("Error", "Failed to export Excel file")
    
    def clear_data(self):
        """Clear all contact data"""
        result = messagebox.askyesno("Confirm", "Are you sure you want to delete all contact data?")
        if result:
            try:
                # Delete database file
                if os.path.exists(self.data_manager.db_path):
                    os.remove(self.data_manager.db_path)
                
                # Reinitialize database
                self.data_manager.init_database()
                
                # Refresh table
                self.refresh_results_table()
                
                self.log_message("All contact data cleared")
                messagebox.showinfo("Success", "All contact data has been cleared")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to clear data: {str(e)}")
    
    def run(self):
        """Start the GUI application"""
        self.log_message("Email Scraper Pro started")
        self.root.mainloop()

def main():
    """Main function to run the application"""
    try:
        app = EmailScraperGUI()
        app.run()
    except Exception as e:
        print(f"Failed to start application: {e}")
        logger.error(f"Application startup error: {e}")

if __name__ == "__main__":
    main()