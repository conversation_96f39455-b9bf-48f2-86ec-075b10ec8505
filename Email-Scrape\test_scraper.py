#!/usr/bin/env python3
"""
Email Scraper Pro - Test Script
Quick test to verify the system is working
"""

import asyncio
import sys
from email_scraper import WebScraper, DataManager
from loguru import logger

async def test_basic_functionality():
    """Test basic scraping functionality"""
    logger.info("Starting Email Scraper Pro test...")
    
    try:
        # Initialize components
        scraper = WebScraper()
        data_manager = DataManager()
        
        logger.info("✅ Components initialized successfully")
        
        # Test browser initialization
        await scraper.init_browser(headless=True)
        logger.info("✅ Browser initialized successfully")
        
        # Test simple website scraping
        test_urls = ['https://httpbin.org/html']
        contacts = await scraper.website_scrape(test_urls)
        
        logger.info(f"✅ Website scraping test completed. Found {len(contacts)} contacts")
        
        # Test database operations
        if contacts:
            saved_count = data_manager.save_contacts(contacts)
            logger.info(f"✅ Database test completed. Saved {saved_count} contacts")
        
        # Clean up
        await scraper.close_browser()
        logger.info("✅ Browser closed successfully")
        
        logger.success("🎉 All tests passed! Email Scraper Pro is ready to use.")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    
    finally:
        try:
            await scraper.close_browser()
        except:
            pass

def main():
    """Run the test"""
    print("\n" + "="*60)
    print("🔍 EMAIL SCRAPER PRO - SYSTEM TEST")
    print("="*60)
    
    try:
        result = asyncio.run(test_basic_functionality())
        
        if result:
            print("\n✅ System is ready! You can now:")
            print("   • Run 'python gui_app.py' for the GUI interface")
            print("   • Run 'python cli_app.py --help' for CLI usage")
            print("   • Check the database file 'email_scraper.db' for stored contacts")
        else:
            print("\n❌ System test failed. Please check the error messages above.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
    
    print("\n" + "="*60)

if __name__ == '__main__':
    main()