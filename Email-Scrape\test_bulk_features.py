#!/usr/bin/env python3
"""
Test Bulk Features
Demonstration and testing of bulk scraping and email generation features
"""

import asyncio
import json
import time
from loguru import logger
from email_generator import EmailGenerator
from bulk_scraper import BulkScraper

async def test_email_generation():
    """Test email generation functionality"""
    print("\n" + "="*60)
    print("🧪 TESTING EMAIL GENERATION")
    print("="*60)
    
    generator = EmailGenerator()
    
    # Test 1: Single name, single region
    print("\n📧 Test 1: Single name generation")
    print("-" * 40)
    
    emails = generator.generate_email_variations(
        name="anna",
        region="global",
        count=50,
        include_business=True
    )
    
    print(f"Generated {len(emails)} emails for 'anna' in global region:")
    for i, email in enumerate(emails[:15], 1):
        print(f"  {i:2d}. {email}")
    if len(emails) > 15:
        print(f"  ... and {len(emails) - 15} more")
    
    # Test 2: Multiple names, multiple regions
    print("\n📧 Test 2: Bulk generation for multiple names")
    print("-" * 40)
    
    names = ["john", "sarah", "michael"]
    regions = ["north_america", "europe", "asia"]
    
    bulk_results = generator.generate_bulk_emails(
        names=names,
        regions=regions,
        emails_per_name=100
    )
    
    total_generated = sum(len(emails) for emails in bulk_results.values())
    print(f"Bulk generation results ({total_generated} total emails):")
    
    for name, emails in bulk_results.items():
        print(f"  {name}: {len(emails)} emails")
        print(f"    Sample: {', '.join(emails[:5])}")
    
    # Test 3: Regional domain analysis
    print("\n🌍 Test 3: Regional domain analysis")
    print("-" * 40)
    
    for region, domains in generator.regional_domains.items():
        print(f"  {region}: {len(domains)} domains")
        print(f"    Sample: {', '.join(domains[:3])}")
    
    # Save test results
    test_results = {
        'single_name_test': {
            'name': 'anna',
            'region': 'global',
            'count': len(emails),
            'sample_emails': emails[:10]
        },
        'bulk_test': {
            'names': names,
            'regions': regions,
            'total_generated': total_generated,
            'breakdown': {name: len(emails) for name, emails in bulk_results.items()}
        },
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    with open('email_generation_test_results.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n✅ Email generation test completed!")
    print(f"📄 Results saved to: email_generation_test_results.json")
    
    return test_results

async def test_bulk_scraping_demo():
    """Test bulk scraping functionality (demo mode)"""
    print("\n" + "="*60)
    print("🔍 TESTING BULK SCRAPING (DEMO MODE)")
    print("="*60)
    
    scraper = BulkScraper()
    
    # Demo scraping (won't actually scrape due to potential blocking)
    print("\n🚀 Demo: Platform configuration")
    print("-" * 40)
    
    platforms_info = {
        'social_media': list(scraper.platforms['social_media'].keys()),
        'business_directories': list(scraper.platforms['business_directories'].keys()),
        'b2b_platforms': list(scraper.platforms['b2b_platforms'].keys()),
        'professional_networks': list(scraper.platforms['professional_networks'].keys())
    }
    
    total_platforms = sum(len(platforms) for platforms in platforms_info.values())
    print(f"Total platforms configured: {total_platforms}")
    
    for category, platforms in platforms_info.items():
        print(f"\n📂 {category.replace('_', ' ').title()}:")
        for i, platform in enumerate(platforms, 1):
            print(f"  {i}. {platform.replace('_', ' ').title()}")
    
    # Demo: URL generation
    print("\n🔗 Demo: Search URL generation")
    print("-" * 40)
    
    query = "marketing agency"
    location = "New York"
    
    sample_urls = []
    for category, platforms in scraper.platforms.items():
        for platform_name, config in list(platforms.items())[:2]:  # First 2 from each category
            try:
                url = config['search_url'].format(
                    query=query.replace(' ', '+'),
                    location=location.replace(' ', '+')
                )
                sample_urls.append((platform_name, url))
            except:
                pass
    
    print(f"Sample search URLs for query '{query}' in '{location}':")
    for platform, url in sample_urls[:5]:
        print(f"  {platform}: {url[:80]}...")
    
    return {
        'platforms_configured': total_platforms,
        'categories': list(platforms_info.keys()),
        'sample_urls': sample_urls[:5],
        'demo_query': query,
        'demo_location': location
    }

async def test_combined_features():
    """Test combined scraping and email generation"""
    print("\n" + "="*60)
    print("🔄 TESTING COMBINED FEATURES")
    print("="*60)
    
    scraper = BulkScraper()
    
    # Test targeted email generation
    print("\n🎯 Test: Targeted email generation")
    print("-" * 40)
    
    names = ["anna", "john"]
    companies = ["TechCorp", "DataSystems"]
    
    targeted_emails = scraper.generate_targeted_emails(
        names=names,
        companies=companies,
        region="north_america"
    )
    
    total_targeted = sum(len(emails) for emails in targeted_emails.values())
    print(f"Generated {total_targeted} targeted emails:")
    
    for key, emails in targeted_emails.items():
        print(f"  {key}: {len(emails)} emails")
        print(f"    Sample: {', '.join(emails[:3])}")
    
    # Test email pattern analysis
    print("\n📊 Test: Email pattern analysis")
    print("-" * 40)
    
    generator = EmailGenerator()
    sample_emails = generator.generate_email_variations("test", "global", 100)
    
    # Analyze patterns
    patterns = {
        'with_numbers': sum(1 for email in sample_emails if any(c.isdigit() for c in email.split('@')[0])),
        'with_dots': sum(1 for email in sample_emails if '.' in email.split('@')[0]),
        'with_underscores': sum(1 for email in sample_emails if '_' in email.split('@')[0]),
        'with_dashes': sum(1 for email in sample_emails if '-' in email.split('@')[0]),
        'business_domains': sum(1 for email in sample_emails if any(domain in email for domain in generator.business_domains))
    }
    
    print("Email pattern analysis:")
    for pattern, count in patterns.items():
        percentage = (count / len(sample_emails)) * 100
        print(f"  {pattern.replace('_', ' ').title()}: {count} ({percentage:.1f}%)")
    
    return {
        'targeted_emails': {key: len(emails) for key, emails in targeted_emails.items()},
        'total_targeted': total_targeted,
        'pattern_analysis': patterns,
        'sample_size': len(sample_emails)
    }

async def test_export_functionality():
    """Test export functionality"""
    print("\n" + "="*60)
    print("💾 TESTING EXPORT FUNCTIONALITY")
    print("="*60)
    
    generator = EmailGenerator()
    
    # Generate test data
    test_data = generator.generate_bulk_emails(
        names=["test_user", "demo_user"],
        regions=["global"],
        emails_per_name=50
    )
    
    # Test JSON export
    print("\n📄 Test: JSON export")
    json_file = "test_export.json"
    generator.save_generated_emails(test_data, json_file)
    print(f"  ✅ JSON export saved to: {json_file}")
    
    # Test CSV export
    print("\n📊 Test: CSV export")
    csv_file = "test_export.csv"
    generator.export_to_csv(test_data, csv_file)
    print(f"  ✅ CSV export saved to: {csv_file}")
    
    # Verify file sizes
    import os
    json_size = os.path.getsize(json_file) if os.path.exists(json_file) else 0
    csv_size = os.path.getsize(csv_file) if os.path.exists(csv_file) else 0
    
    print(f"\n📏 File sizes:")
    print(f"  JSON: {json_size:,} bytes")
    print(f"  CSV: {csv_size:,} bytes")
    
    return {
        'json_file': json_file,
        'csv_file': csv_file,
        'json_size': json_size,
        'csv_size': csv_size,
        'records_exported': sum(len(emails) for emails in test_data.values())
    }

async def run_comprehensive_test():
    """Run comprehensive test of all features"""
    print("🚀 STARTING COMPREHENSIVE BULK FEATURES TEST")
    print("=" * 80)
    
    start_time = time.time()
    
    # Run all tests
    email_test = await test_email_generation()
    scraping_test = await test_bulk_scraping_demo()
    combined_test = await test_combined_features()
    export_test = await test_export_functionality()
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Compile comprehensive results
    comprehensive_results = {
        'test_summary': {
            'total_duration_seconds': round(duration, 2),
            'tests_completed': 4,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        },
        'email_generation_test': email_test,
        'bulk_scraping_demo': scraping_test,
        'combined_features_test': combined_test,
        'export_functionality_test': export_test
    }
    
    # Save comprehensive results
    with open('comprehensive_test_results.json', 'w') as f:
        json.dump(comprehensive_results, f, indent=2)
    
    # Print final summary
    print("\n" + "="*80)
    print("📋 COMPREHENSIVE TEST SUMMARY")
    print("="*80)
    
    print(f"\n⏱️  Total test duration: {duration:.2f} seconds")
    print(f"✅ Tests completed: 4/4")
    print(f"📄 Results saved to: comprehensive_test_results.json")
    
    print("\n📊 Key Statistics:")
    print(f"  • Email generation: {email_test['bulk_test']['total_generated']} emails")
    print(f"  • Platforms configured: {scraping_test['platforms_configured']}")
    print(f"  • Targeted emails: {combined_test['total_targeted']}")
    print(f"  • Export records: {export_test['records_exported']}")
    
    print("\n🎉 All tests completed successfully!")
    print("\n💡 Next steps:")
    print("  1. Configure real proxies for production scraping")
    print("  2. Test with actual websites (be mindful of rate limits)")
    print("  3. Customize email patterns for your specific needs")
    print("  4. Set up automated scheduling for bulk operations")
    
    return comprehensive_results

def demo_cli_usage():
    """Demonstrate CLI usage examples"""
    print("\n" + "="*60)
    print("💻 CLI USAGE EXAMPLES")
    print("="*60)
    
    examples = [
        {
            'title': 'Generate emails for multiple names',
            'command': 'python bulk_cli.py generate anna john sarah --region north_america --count 1000 --csv'
        },
        {
            'title': 'Bulk scrape multiple platforms',
            'command': 'python bulk_cli.py scrape "marketing agency" --location "New York" --max-results 50 --csv'
        },
        {
            'title': 'Combined scraping and email generation',
            'command': 'python bulk_cli.py combined "tech startup" anna john --region global'
        },
        {
            'title': 'List available regions',
            'command': 'python bulk_cli.py regions --verbose'
        },
        {
            'title': 'Run email generation demo',
            'command': 'python bulk_cli.py demo --name anna --region europe'
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}:")
        print(f"   {example['command']}")
    
    print("\n📚 For more options, run: python bulk_cli.py --help")

def demo_gui_features():
    """Demonstrate GUI features"""
    print("\n" + "="*60)
    print("🖥️  GUI FEATURES OVERVIEW")
    print("="*60)
    
    features = [
        "🔍 Bulk Scraping Tab - Scrape multiple platforms simultaneously",
        "📧 Email Generation Tab - Generate thousands of email variations",
        "🔄 Combined Mode Tab - Scrape and generate emails in one operation",
        "📊 Results Tab - View, filter, and export all results",
        "⚙️ Settings Tab - Manage proxies and configuration",
        "📈 Real-time Progress - Live updates and status monitoring",
        "💾 Multiple Export Formats - JSON, CSV, Excel support",
        "🎯 Platform Selection - Choose specific platforms to scrape",
        "🌍 Regional Targeting - Generate emails for specific regions",
        "📋 Results Preview - View samples before full generation"
    ]
    
    print("\nAvailable features:")
    for feature in features:
        print(f"  {feature}")
    
    print("\n🚀 To start the GUI, run: python bulk_gui.py")

if __name__ == "__main__":
    print("🧪 BULK FEATURES TEST SUITE")
    print("=" * 50)
    
    # Setup logging
    logger.remove()
    logger.add("test_bulk_features.log", level="DEBUG")
    logger.add(lambda msg: print(f"[LOG] {msg}"), level="INFO")
    
    try:
        # Run comprehensive test
        results = asyncio.run(run_comprehensive_test())
        
        # Show CLI and GUI demos
        demo_cli_usage()
        demo_gui_features()
        
        print("\n" + "="*80)
        print("🎊 ALL TESTS AND DEMOS COMPLETED SUCCESSFULLY!")
        print("="*80)
        
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        print(f"\n❌ Test failed: {str(e)}")