#!/usr/bin/env python3
"""
Advanced Email Delivery System
Features:
- Attachment support with multiple formats
- Advanced obfuscation techniques
- Inbox validation and deliverability optimization
- Anti-spam measures and reputation management
- Enhanced delivery tracking
"""

import asyncio
import base64
import hashlib
import mimetypes
import os
import random
import re
import string
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from email.mime.application import MIMEApplication
from email.mime.audio import MIMEAudio
from email.mime.image import MIMEImage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import json
import dns.resolver
import requests
from loguru import logger
import smtplib
import ssl
from email.utils import formataddr, make_msgid
from urllib.parse import quote, unquote

# Import existing modules
from smtp_config import SMTPManager
from email_templates import TemplateManager

@dataclass
class AttachmentConfig:
    """Configuration for email attachments"""
    file_path: str
    filename: Optional[str] = None
    content_type: Optional[str] = None
    inline: bool = False
    content_id: Optional[str] = None
    encoding: str = 'base64'
    obfuscate_name: bool = True
    max_size_mb: float = 25.0

@dataclass
class ObfuscationConfig:
    """Configuration for email obfuscation"""
    randomize_headers: bool = True
    vary_content: bool = True
    use_html_entities: bool = True
    randomize_whitespace: bool = True
    split_links: bool = True
    use_unicode_variants: bool = True
    randomize_case: bool = False
    insert_invisible_chars: bool = True
    use_base64_encoding: bool = False

@dataclass
class AntiSpamConfig:
    """Configuration for anti-spam measures"""
    check_spf: bool = True
    check_dkim: bool = True
    check_dmarc: bool = True
    validate_mx_records: bool = True
    check_blacklists: bool = True
    rate_limit_per_hour: int = 100
    delay_between_emails: float = 2.0
    randomize_delays: bool = True
    use_reputation_warming: bool = True
    track_bounces: bool = True

@dataclass
class DeliveryMetrics:
    """Email delivery metrics tracking"""
    sent_count: int = 0
    delivered_count: int = 0
    bounced_count: int = 0
    opened_count: int = 0
    clicked_count: int = 0
    spam_count: int = 0
    reputation_score: float = 100.0
    last_updated: datetime = field(default_factory=datetime.now)

class EmailObfuscator:
    """Advanced email content obfuscation"""
    
    def __init__(self, config: ObfuscationConfig):
        self.config = config
        self.unicode_variants = {
            'a': ['а', 'ɑ', 'α', 'ａ'],
            'e': ['е', 'ε', 'ｅ'],
            'o': ['о', 'ο', 'ｏ'],
            'p': ['р', 'ρ', 'ｐ'],
            'c': ['с', 'ϲ', 'ｃ'],
            'x': ['х', 'χ', 'ｘ'],
            'y': ['у', 'γ', 'ｙ'],
        }
        
    def obfuscate_text(self, text: str) -> str:
        """Apply various obfuscation techniques to text"""
        result = text
        
        if self.config.use_html_entities:
            result = self._apply_html_entities(result)
            
        if self.config.use_unicode_variants:
            result = self._apply_unicode_variants(result)
            
        if self.config.randomize_whitespace:
            result = self._randomize_whitespace(result)
            
        if self.config.insert_invisible_chars:
            result = self._insert_invisible_chars(result)
            
        if self.config.randomize_case:
            result = self._randomize_case(result)
            
        return result
    
    def _apply_html_entities(self, text: str) -> str:
        """Convert some characters to HTML entities"""
        entities = {
            '@': '&#64;',
            '.': '&#46;',
            'a': '&#97;',
            'e': '&#101;',
            'i': '&#105;',
            'o': '&#111;',
            'u': '&#117;'
        }
        
        result = text
        for char, entity in entities.items():
            if random.random() < 0.3:  # 30% chance to replace
                result = result.replace(char, entity)
        
        return result
    
    def _apply_unicode_variants(self, text: str) -> str:
        """Replace some characters with Unicode variants"""
        result = text
        for char, variants in self.unicode_variants.items():
            if char in result and random.random() < 0.2:  # 20% chance
                variant = random.choice(variants)
                result = result.replace(char, variant, 1)
        
        return result
    
    def _randomize_whitespace(self, text: str) -> str:
        """Add random whitespace variations"""
        # Add zero-width spaces randomly
        zero_width_chars = ['\u200B', '\u200C', '\u200D', '\uFEFF']
        
        result = ""
        for char in text:
            result += char
            if char == ' ' and random.random() < 0.1:  # 10% chance
                result += random.choice(zero_width_chars)
        
        return result
    
    def _insert_invisible_chars(self, text: str) -> str:
        """Insert invisible characters randomly"""
        invisible_chars = ['\u200B', '\u200C', '\u200D']
        
        result = ""
        for i, char in enumerate(text):
            result += char
            if i > 0 and i % 10 == 0 and random.random() < 0.05:  # 5% chance every 10 chars
                result += random.choice(invisible_chars)
        
        return result
    
    def _randomize_case(self, text: str) -> str:
        """Randomly change case of some characters"""
        result = ""
        for char in text:
            if char.isalpha() and random.random() < 0.1:  # 10% chance
                result += char.upper() if char.islower() else char.lower()
            else:
                result += char
        
        return result
    
    def obfuscate_links(self, html_content: str) -> str:
        """Obfuscate links in HTML content"""
        if not self.config.split_links:
            return html_content
            
        # Find all href attributes
        href_pattern = r'href=["\']([^"\']+)["\']'
        
        def obfuscate_url(match):
            url = match.group(1)
            # Split URL and use redirector or URL shortener simulation
            encoded_url = base64.b64encode(url.encode()).decode()
            return f'href="data:text/html,<script>window.location=atob(\'{encoded_url}\')</script>"'
        
        return re.sub(href_pattern, obfuscate_url, html_content)

class InboxValidator:
    """Advanced inbox validation and deliverability checker"""
    
    def __init__(self):
        self.mx_cache = {}
        self.blacklist_cache = {}
        
    async def validate_email_deliverability(self, email: str, domain: str = None) -> Dict:
        """Comprehensive email deliverability validation"""
        results = {
            'email': email,
            'is_valid': False,
            'mx_valid': False,
            'spf_valid': False,
            'dmarc_valid': False,
            'blacklisted': False,
            'deliverability_score': 0,
            'recommendations': []
        }
        
        try:
            # Basic email format validation
            if not self._is_valid_email_format(email):
                results['recommendations'].append("Invalid email format")
                return results
            
            email_domain = email.split('@')[1]
            
            # MX record validation
            mx_valid = await self._check_mx_records(email_domain)
            results['mx_valid'] = mx_valid
            
            if not mx_valid:
                results['recommendations'].append("No valid MX records found")
                return results
            
            # SPF record check
            spf_valid = await self._check_spf_record(domain or email_domain)
            results['spf_valid'] = spf_valid
            
            # DMARC record check
            dmarc_valid = await self._check_dmarc_record(email_domain)
            results['dmarc_valid'] = dmarc_valid
            
            # Blacklist check
            blacklisted = await self._check_blacklists(domain or email_domain)
            results['blacklisted'] = blacklisted
            
            # Calculate deliverability score
            score = 0
            if mx_valid:
                score += 40
            if spf_valid:
                score += 25
            if dmarc_valid:
                score += 25
            if not blacklisted:
                score += 10
            
            results['deliverability_score'] = score
            results['is_valid'] = score >= 70
            
            # Add recommendations
            if not spf_valid:
                results['recommendations'].append("Configure SPF record")
            if not dmarc_valid:
                results['recommendations'].append("Configure DMARC record")
            if blacklisted:
                results['recommendations'].append("Domain/IP is blacklisted")
            
        except Exception as e:
            logger.error(f"Error validating email deliverability: {e}")
            results['recommendations'].append(f"Validation error: {str(e)}")
        
        return results
    
    def _is_valid_email_format(self, email: str) -> bool:
        """Validate email format using regex"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    async def _check_mx_records(self, domain: str) -> bool:
        """Check if domain has valid MX records"""
        if domain in self.mx_cache:
            return self.mx_cache[domain]
        
        try:
            mx_records = dns.resolver.resolve(domain, 'MX')
            valid = len(mx_records) > 0
            self.mx_cache[domain] = valid
            return valid
        except Exception:
            self.mx_cache[domain] = False
            return False
    
    async def _check_spf_record(self, domain: str) -> bool:
        """Check if domain has valid SPF record"""
        try:
            txt_records = dns.resolver.resolve(domain, 'TXT')
            for record in txt_records:
                if 'v=spf1' in str(record):
                    return True
            return False
        except Exception:
            return False
    
    async def _check_dmarc_record(self, domain: str) -> bool:
        """Check if domain has valid DMARC record"""
        try:
            dmarc_domain = f'_dmarc.{domain}'
            txt_records = dns.resolver.resolve(dmarc_domain, 'TXT')
            for record in txt_records:
                if 'v=DMARC1' in str(record):
                    return True
            return False
        except Exception:
            return False
    
    async def _check_blacklists(self, domain: str) -> bool:
        """Check if domain is blacklisted"""
        if domain in self.blacklist_cache:
            return self.blacklist_cache[domain]
        
        # Simplified blacklist check - in production, use multiple RBL services
        blacklists = [
            'zen.spamhaus.org',
            'bl.spamcop.net',
            'dnsbl.sorbs.net'
        ]
        
        try:
            for blacklist in blacklists:
                try:
                    query = f'{domain}.{blacklist}'
                    dns.resolver.resolve(query, 'A')
                    self.blacklist_cache[domain] = True
                    return True
                except dns.resolver.NXDOMAIN:
                    continue
                except Exception:
                    continue
            
            self.blacklist_cache[domain] = False
            return False
        except Exception:
            return False

class AttachmentProcessor:
    """Advanced attachment processing with security and obfuscation"""

    def __init__(self):
        self.supported_types = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.xls': 'application/vnd.ms-excel',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.ppt': 'application/vnd.ms-powerpoint',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.txt': 'text/plain',
            '.csv': 'text/csv',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed'
        }

    def process_attachment(self, config: AttachmentConfig) -> Optional[MIMEApplication]:
        """Process and prepare attachment with obfuscation"""
        try:
            file_path = Path(config.file_path)

            if not file_path.exists():
                logger.error(f"Attachment file not found: {config.file_path}")
                return None

            # Check file size
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > config.max_size_mb:
                logger.error(f"Attachment too large: {file_size_mb:.2f}MB > {config.max_size_mb}MB")
                return None

            # Read file content
            with open(file_path, 'rb') as f:
                content = f.read()

            # Determine content type
            content_type = config.content_type
            if not content_type:
                content_type = self._detect_content_type(file_path)

            # Create attachment
            if content_type.startswith('image/'):
                attachment = MIMEImage(content)
            elif content_type.startswith('audio/'):
                attachment = MIMEAudio(content)
            else:
                attachment = MIMEApplication(content, _subtype=content_type.split('/')[-1])

            # Set filename (with obfuscation if enabled)
            filename = config.filename or file_path.name
            if config.obfuscate_name:
                filename = self._obfuscate_filename(filename)

            attachment.add_header('Content-Disposition', 'attachment', filename=filename)

            # Set content ID for inline attachments
            if config.inline and config.content_id:
                attachment.add_header('Content-ID', f'<{config.content_id}>')
                attachment.add_header('Content-Disposition', 'inline', filename=filename)

            logger.info(f"Processed attachment: {filename} ({file_size_mb:.2f}MB)")
            return attachment

        except Exception as e:
            logger.error(f"Error processing attachment {config.file_path}: {e}")
            return None

    def _detect_content_type(self, file_path: Path) -> str:
        """Detect content type from file extension"""
        extension = file_path.suffix.lower()
        return self.supported_types.get(extension, 'application/octet-stream')

    def _obfuscate_filename(self, filename: str) -> str:
        """Obfuscate filename to avoid spam filters"""
        name, ext = os.path.splitext(filename)

        # Add random characters
        random_chars = ''.join(random.choices(string.ascii_letters + string.digits, k=3))

        # Create obfuscated name
        obfuscated_name = f"{name}_{random_chars}{ext}"

        return obfuscated_name

class AdvancedEmailDelivery:
    """Main advanced email delivery system"""

    def __init__(self, smtp_manager: SMTPManager = None):
        self.smtp_manager = smtp_manager or SMTPManager()
        self.template_manager = TemplateManager()
        self.obfuscator = EmailObfuscator(ObfuscationConfig())
        self.validator = InboxValidator()
        self.attachment_processor = AttachmentProcessor()
        self.metrics = DeliveryMetrics()
        self.anti_spam_config = AntiSpamConfig()

        # Rate limiting
        self.last_send_time = 0
        self.hourly_send_count = 0
        self.hourly_reset_time = time.time()

        # Delivery tracking
        self.delivery_log = []
        self.bounce_tracking = {}

    async def send_advanced_email(
        self,
        to_emails: Union[str, List[str]],
        subject: str,
        body: str,
        html_body: str = None,
        attachments: List[AttachmentConfig] = None,
        obfuscation_config: ObfuscationConfig = None,
        anti_spam_config: AntiSpamConfig = None,
        template_variables: Dict = None,
        sender_name: str = None,
        reply_to: str = None,
        custom_headers: Dict = None
    ) -> Dict:
        """Send advanced email with all features"""

        # Initialize configurations
        obf_config = obfuscation_config or self.obfuscator.config
        spam_config = anti_spam_config or self.anti_spam_config

        # Ensure to_emails is a list
        if isinstance(to_emails, str):
            to_emails = [to_emails]

        results = {
            'total_emails': len(to_emails),
            'sent': 0,
            'failed': 0,
            'delivery_results': [],
            'overall_success': False
        }

        try:
            # Rate limiting check
            if not self._check_rate_limit(spam_config):
                results['error'] = "Rate limit exceeded"
                return results

            # Get SMTP configuration
            smtp_config = self.smtp_manager.get_active_config()
            if not smtp_config:
                results['error'] = "No active SMTP configuration"
                return results

            # Validate sender domain if anti-spam is enabled
            if spam_config.check_spf or spam_config.check_dmarc:
                sender_domain = smtp_config.username.split('@')[-1] if '@' in smtp_config.username else None
                if sender_domain:
                    validation = await self.validator.validate_email_deliverability(
                        smtp_config.username, sender_domain
                    )
                    if validation['deliverability_score'] < 50:
                        logger.warning(f"Low deliverability score: {validation['deliverability_score']}")

            # Process each recipient
            for email in to_emails:
                try:
                    # Validate recipient if enabled
                    if spam_config.validate_mx_records:
                        recipient_validation = await self.validator.validate_email_deliverability(email)
                        if not recipient_validation['is_valid']:
                            logger.warning(f"Invalid recipient: {email}")
                            results['delivery_results'].append({
                                'email': email,
                                'status': 'failed',
                                'reason': 'Invalid email'
                            })
                            results['failed'] += 1
                            continue

                    # Apply rate limiting delay
                    if spam_config.delay_between_emails > 0:
                        delay = spam_config.delay_between_emails
                        if spam_config.randomize_delays:
                            delay = random.uniform(delay * 0.5, delay * 1.5)

                        time_since_last = time.time() - self.last_send_time
                        if time_since_last < delay:
                            await asyncio.sleep(delay - time_since_last)

                    # Send individual email
                    send_result = await self._send_single_advanced_email(
                        email, subject, body, html_body, attachments,
                        obf_config, template_variables, sender_name,
                        reply_to, custom_headers
                    )

                    results['delivery_results'].append(send_result)

                    if send_result['status'] == 'sent':
                        results['sent'] += 1
                        self.metrics.sent_count += 1
                    else:
                        results['failed'] += 1

                    self.last_send_time = time.time()
                    self.hourly_send_count += 1

                except Exception as e:
                    logger.error(f"Error sending to {email}: {e}")
                    results['delivery_results'].append({
                        'email': email,
                        'status': 'failed',
                        'reason': str(e)
                    })
                    results['failed'] += 1

            results['overall_success'] = results['sent'] > 0

            # Update metrics
            self.metrics.last_updated = datetime.now()

            # Log delivery results
            self._log_delivery_results(results)

        except Exception as e:
            logger.error(f"Error in advanced email delivery: {e}")
            results['error'] = str(e)

        return results

    async def _send_single_advanced_email(
        self,
        email: str,
        subject: str,
        body: str,
        html_body: str = None,
        attachments: List[AttachmentConfig] = None,
        obf_config: ObfuscationConfig = None,
        template_variables: Dict = None,
        sender_name: str = None,
        reply_to: str = None,
        custom_headers: Dict = None
    ) -> Dict:
        """Send a single advanced email with all features"""

        result = {
            'email': email,
            'status': 'failed',
            'reason': '',
            'message_id': '',
            'timestamp': datetime.now().isoformat()
        }

        try:
            smtp_config = self.smtp_manager.get_active_config()

            # Create message
            msg = MIMEMultipart('mixed')

            # Generate unique message ID
            message_id = make_msgid()
            msg['Message-ID'] = message_id
            result['message_id'] = message_id

            # Set headers
            sender_email = smtp_config.username
            if sender_name:
                msg['From'] = formataddr((sender_name, sender_email))
            else:
                msg['From'] = sender_email

            msg['To'] = email
            msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

            # Apply obfuscation to subject
            if obf_config and obf_config.vary_content:
                obfuscated_subject = self.obfuscator.obfuscate_text(subject)
                msg['Subject'] = obfuscated_subject
            else:
                msg['Subject'] = subject

            # Set reply-to if provided
            if reply_to:
                msg['Reply-To'] = reply_to

            # Add custom headers
            if custom_headers:
                for header, value in custom_headers.items():
                    msg[header] = value

            # Add randomized headers for obfuscation
            if obf_config and obf_config.randomize_headers:
                self._add_randomized_headers(msg)

            # Create multipart alternative for text and HTML
            msg_alternative = MIMEMultipart('alternative')

            # Apply obfuscation to body
            if obf_config:
                obfuscated_body = self.obfuscator.obfuscate_text(body)
                msg_alternative.attach(MIMEText(obfuscated_body, 'plain', 'utf-8'))

                if html_body:
                    obfuscated_html = self.obfuscator.obfuscate_text(html_body)
                    obfuscated_html = self.obfuscator.obfuscate_links(obfuscated_html)
                    msg_alternative.attach(MIMEText(obfuscated_html, 'html', 'utf-8'))
            else:
                msg_alternative.attach(MIMEText(body, 'plain', 'utf-8'))
                if html_body:
                    msg_alternative.attach(MIMEText(html_body, 'html', 'utf-8'))

            msg.attach(msg_alternative)

            # Process attachments
            if attachments:
                for attachment_config in attachments:
                    attachment = self.attachment_processor.process_attachment(attachment_config)
                    if attachment:
                        msg.attach(attachment)

            # Send email
            success = self._send_smtp_message(msg, smtp_config)

            if success:
                result['status'] = 'sent'
                result['reason'] = 'Successfully sent'
                logger.info(f"Advanced email sent to {email}")
            else:
                result['reason'] = 'SMTP send failed'
                logger.error(f"Failed to send advanced email to {email}")

        except Exception as e:
            result['reason'] = str(e)
            logger.error(f"Error sending advanced email to {email}: {e}")

        return result

    def _send_smtp_message(self, msg: MIMEMultipart, smtp_config) -> bool:
        """Send message via SMTP with retry logic"""
        for attempt in range(smtp_config.max_retries):
            try:
                # Create SMTP connection
                if smtp_config.use_ssl:
                    server = smtplib.SMTP_SSL(
                        smtp_config.smtp_server,
                        smtp_config.smtp_port,
                        timeout=smtp_config.timeout
                    )
                else:
                    server = smtplib.SMTP(
                        smtp_config.smtp_server,
                        smtp_config.smtp_port,
                        timeout=smtp_config.timeout
                    )
                    if smtp_config.use_tls:
                        server.starttls(context=ssl.create_default_context())

                # Login and send
                server.login(smtp_config.username, smtp_config.password)
                server.send_message(msg)
                server.quit()

                return True

            except Exception as e:
                logger.warning(f"SMTP attempt {attempt + 1} failed: {e}")
                if attempt < smtp_config.max_retries - 1:
                    time.sleep(smtp_config.retry_delay)
                else:
                    raise e

        return False

    def _add_randomized_headers(self, msg: MIMEMultipart):
        """Add randomized headers to avoid pattern detection"""
        random_headers = [
            ('X-Mailer', random.choice([
                'Microsoft Outlook 16.0',
                'Mozilla Thunderbird 78.0',
                'Apple Mail (2.3445.104.11)',
                'Gmail API'
            ])),
            ('X-Priority', random.choice(['1', '3', '5'])),
            ('X-MSMail-Priority', random.choice(['High', 'Normal', 'Low'])),
            ('Importance', random.choice(['High', 'Normal', 'Low']))
        ]

        # Add 1-3 random headers
        num_headers = random.randint(1, 3)
        selected_headers = random.sample(random_headers, num_headers)

        for header, value in selected_headers:
            msg[header] = value

    def _check_rate_limit(self, config: AntiSpamConfig) -> bool:
        """Check if rate limit allows sending"""
        current_time = time.time()

        # Reset hourly counter if needed
        if current_time - self.hourly_reset_time >= 3600:
            self.hourly_send_count = 0
            self.hourly_reset_time = current_time

        # Check hourly limit
        if self.hourly_send_count >= config.rate_limit_per_hour:
            logger.warning(f"Hourly rate limit exceeded: {self.hourly_send_count}/{config.rate_limit_per_hour}")
            return False

        return True

    def _log_delivery_results(self, results: Dict):
        """Log delivery results for tracking"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'total_emails': results['total_emails'],
            'sent': results['sent'],
            'failed': results['failed'],
            'success_rate': results['sent'] / results['total_emails'] if results['total_emails'] > 0 else 0
        }

        self.delivery_log.append(log_entry)

        # Keep only last 1000 entries
        if len(self.delivery_log) > 1000:
            self.delivery_log = self.delivery_log[-1000:]

    def get_delivery_metrics(self) -> Dict:
        """Get comprehensive delivery metrics"""
        return {
            'total_sent': self.metrics.sent_count,
            'total_delivered': self.metrics.delivered_count,
            'total_bounced': self.metrics.bounced_count,
            'total_opened': self.metrics.opened_count,
            'total_clicked': self.metrics.clicked_count,
            'spam_reports': self.metrics.spam_count,
            'reputation_score': self.metrics.reputation_score,
            'delivery_rate': (self.metrics.delivered_count / self.metrics.sent_count * 100) if self.metrics.sent_count > 0 else 0,
            'open_rate': (self.metrics.opened_count / self.metrics.delivered_count * 100) if self.metrics.delivered_count > 0 else 0,
            'click_rate': (self.metrics.clicked_count / self.metrics.opened_count * 100) if self.metrics.opened_count > 0 else 0,
            'last_updated': self.metrics.last_updated.isoformat()
        }

    def export_delivery_log(self, filename: str = None) -> str:
        """Export delivery log to JSON file"""
        if not filename:
            filename = f"delivery_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        export_data = {
            'metrics': self.get_delivery_metrics(),
            'delivery_log': self.delivery_log,
            'export_timestamp': datetime.now().isoformat()
        }

        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2)

        logger.info(f"Delivery log exported to {filename}")
        return filename

# Example usage and testing
if __name__ == "__main__":
    async def test_advanced_delivery():
        """Test the advanced email delivery system"""

        # Initialize the delivery system
        delivery_system = AdvancedEmailDelivery()

        # Configure obfuscation
        obf_config = ObfuscationConfig(
            randomize_headers=True,
            vary_content=True,
            use_html_entities=True,
            randomize_whitespace=True,
            split_links=True,
            use_unicode_variants=True,
            insert_invisible_chars=True
        )

        # Configure anti-spam
        anti_spam_config = AntiSpamConfig(
            rate_limit_per_hour=50,
            delay_between_emails=3.0,
            randomize_delays=True,
            validate_mx_records=True
        )

        # Test email content
        subject = "Important Business Proposal"
        body = "Dear recipient, we have an exciting business opportunity for you."
        html_body = "<html><body><h1>Business Opportunity</h1><p>Dear recipient, we have an exciting business opportunity for you.</p></body></html>"

        # Test with attachments (uncomment to test)
        # attachments = [
        #     AttachmentConfig(
        #         file_path="test_document.pdf",
        #         obfuscate_name=True
        #     )
        # ]

        # Send test email
        results = await delivery_system.send_advanced_email(
            to_emails=["<EMAIL>"],
            subject=subject,
            body=body,
            html_body=html_body,
            # attachments=attachments,
            obfuscation_config=obf_config,
            anti_spam_config=anti_spam_config,
            sender_name="Business Development Team"
        )

        print("Delivery Results:")
        print(json.dumps(results, indent=2))

        # Get metrics
        metrics = delivery_system.get_delivery_metrics()
        print("\nDelivery Metrics:")
        print(json.dumps(metrics, indent=2))

    # Run the test
    # asyncio.run(test_advanced_delivery())
