@echo off
echo ========================================
echo  BMD EMAIL SCRAPPER - Security Exclusion
echo ========================================
echo.
echo This will add the current folder to Windows Security exclusions
echo to prevent antivirus interference with the application.
echo.
echo IMPORTANT: This requires Administrator privileges!
echo.
pause

echo Adding Windows Security exclusions...
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with Administrator privileges...
    echo.
) else (
    echo WARNING: Not running as Administrator!
    echo Some operations may fail.
    echo For best results, right-click this file and select "Run as administrator"
    echo.
)

:: Add folder exclusion using PowerShell
echo Adding folder exclusion...
powershell -Command "try { Add-MpPreference -ExclusionPath '%~dp0'; Write-Host 'Folder exclusion added successfully!' -ForegroundColor Green } catch { Write-Host 'Error adding folder exclusion: ' $_.Exception.Message -ForegroundColor Red }"

:: Add Python process exclusion
echo.
echo Adding Python process exclusion...
powershell -Command "try { $python = (Get-Command python -ErrorAction SilentlyContinue).Source; if ($python) { Add-MpPreference -ExclusionProcess $python; Write-Host 'Python process excluded successfully!' -ForegroundColor Green } else { Write-Host 'Python not found in PATH' -ForegroundColor Yellow } } catch { Write-Host 'Error adding Python exclusion: ' $_.Exception.Message -ForegroundColor Red }"

:: Add file extension exclusions
echo.
echo Adding file extension exclusions...
powershell -Command "try { Add-MpPreference -ExclusionExtension '.py'; Add-MpPreference -ExclusionExtension '.bat'; Add-MpPreference -ExclusionExtension '.ps1'; Write-Host 'File extensions excluded successfully!' -ForegroundColor Green } catch { Write-Host 'Error adding extension exclusions: ' $_.Exception.Message -ForegroundColor Red }"

echo.
echo ========================================
echo  Exclusions Setup Complete!
echo ========================================
echo.
echo Your BMD EMAIL SCRAPPER should now run without
echo antivirus interference.
echo.
echo If you encountered errors, you may need to:
echo 1. Run this file as Administrator
echo 2. Manually add exclusions in Windows Security
echo.
echo Press any key to exit...
pause >nul