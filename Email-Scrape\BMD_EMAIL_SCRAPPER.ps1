# BMD EMAIL SCRAPPER Launcher
# PowerShell script to launch the application

Write-Host "Starting BMD EMAIL SCRAPPER..." -ForegroundColor Green

# Change to script directory
Set-Location -Path $PSScriptRoot

try {
    # Launch the GUI application
    python bulk_gui.py
}
catch {
    Write-Host "Error: Failed to start the application." -ForegroundColor Red
    Write-Host "Please ensure Python is installed and all dependencies are available." -ForegroundColor Yellow
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}