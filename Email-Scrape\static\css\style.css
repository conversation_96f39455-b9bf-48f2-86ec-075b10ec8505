/* Custom CSS for Email Scraper Pro */

:root {
    /* Modern 2024 Color Palette - High Contrast & Accessible */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --success-color: #059669;
    --success-hover: #047857;
    --danger-color: #dc2626;
    --danger-hover: #b91c1c;
    --warning-color: #d97706;
    --warning-hover: #b45309;
    --info-color: #0891b2;
    --info-hover: #0e7490;
    
    /* Neutral Colors - Off-white approach for modern aesthetics */
    --background-primary: #fefefe;
    --background-secondary: #f8fafc;
    --background-tertiary: #f1f5f9;
    --surface-color: #ffffff;
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    
    /* Dark Mode Colors */
    --dark-bg-primary: #0f172a;
    --dark-bg-secondary: #1e293b;
    --dark-surface: #334155;
    --dark-text-primary: #f8fafc;
    --dark-text-secondary: #cbd5e1;
    
    /* Design System Variables */
    --border-radius-sm: 0.375rem;
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
}

/* Global Styles - Modern 2024 Approach */
body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--space-4);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

p {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
}

.text-muted {
    color: var(--text-muted) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* Modern Card System - 2024 Design Patterns */
.card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--box-shadow-xl);
    transform: translateY(-4px);
    border-color: var(--border-hover);
}

.card-header {
    background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--space-5) var(--space-6);
    font-size: var(--font-size-lg);
}

.card-body {
    padding: var(--space-6);
}

.card-footer {
    background: var(--background-secondary);
    border-top: 1px solid var(--border-color);
    padding: var(--space-4) var(--space-6);
}

/* Card Variants */
.card-elevated {
    box-shadow: var(--box-shadow-lg);
    border: none;
}

.card-elevated:hover {
    box-shadow: var(--box-shadow-xl);
    transform: translateY(-6px);
}

.card-interactive {
    cursor: pointer;
    transition: var(--transition);
}

.card-interactive:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.card-interactive:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
    outline: none;
}

/* Gradient Cards - Modern Approach */
.card-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
}

.card-gradient-primary .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.card-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: white;
    border: none;
}

.card-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%);
    color: white;
    border: none;
}

/* Card with Border Accent */
.card-accent {
    border-left: 4px solid var(--primary-color);
}

.card-accent-success {
    border-left: 4px solid var(--success-color);
}

.card-accent-warning {
    border-left: 4px solid var(--warning-color);
}

.card-accent-danger {
    border-left: 4px solid var(--danger-color);
}

/* Glass Card Effect */
.card-glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Compact Card */
.card-compact .card-body {
    padding: var(--space-4);
}

.card-compact .card-header {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
}

/* Modern Button System - 2024 Design Patterns */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: var(--font-size-sm);
    padding: var(--space-3) var(--space-6);
    border: 1px solid transparent;
    transition: var(--transition);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

.btn:active {
    transform: translateY(0);
    transition: var(--transition-fast);
}

.btn:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Primary Button - Modern Gradient */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover) 0%, #1e40af 100%);
    border-color: var(--primary-hover);
    color: white;
}

/* Secondary Button - Modern Outline */
.btn-secondary {
    background: var(--surface-color);
    color: var(--secondary-color);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background: var(--background-secondary);
    border-color: var(--border-hover);
    color: var(--secondary-hover);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-hover) 0%, #065f46 100%);
    border-color: var(--success-hover);
    color: white;
}

/* Danger Button */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-hover) 0%, #991b1b 100%);
    border-color: var(--danger-hover);
    color: white;
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

/* Ghost Button Variant */
.btn-ghost {
    background: transparent;
    color: var(--primary-color);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--primary-light);
    color: var(--primary-hover);
}

/* Icon Buttons */
.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-icon-sm {
    width: 2rem;
    height: 2rem;
}

.btn-icon-lg {
    width: 3rem;
    height: 3rem;
}

/* Modern Form Controls - Enhanced UX */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    background: var(--surface-color);
    color: var(--text-primary);
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    transition: var(--transition);
    box-shadow: var(--box-shadow-sm);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
    outline: none;
    background: var(--surface-color);
}

.form-control:hover, .form-select:hover {
    border-color: var(--border-hover);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 1;
}

/* Form Labels */
.form-label {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
}

/* Input Groups */
.input-group {
    box-shadow: var(--box-shadow-sm);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.input-group-text {
    background: var(--background-secondary);
    border-color: var(--border-color);
    color: var(--text-secondary);
    font-weight: 500;
}

/* Form Validation */
.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.valid-feedback {
    color: var(--success-color);
    font-size: var(--font-size-sm);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color) 0%, #0056b3 100%);
    transition: width 0.3s ease;
}

/* Enhanced Status Indicators - Modern Design */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: statusPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-indicator::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: inherit;
    animation: statusRipple 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(0.95);
    }
}

@keyframes statusRipple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

.status-online {
    background: var(--success-color);
}

.status-offline {
    background: var(--secondary-color);
}

.status-busy {
    background: var(--danger-color);
}

.status-away {
    background: var(--warning-color);
}

/* Large Status Indicators */
.status-indicator-lg {
    width: 16px;
    height: 16px;
}

.status-indicator-sm {
    width: 8px;
    height: 8px;
}

/* Email Results */
.email-item {
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    transition: var(--transition);
}

.email-item:hover {
    background-color: #f8f9fa;
}

.email-item:last-child {
    border-bottom: none;
}

/* Statistics Cards */
.stat-card {
    text-align: center;
    padding: 1.5rem;
}

.stat-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.stat-card h4 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* Loading Animations */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-card i {
        font-size: 2rem;
    }
    
    .stat-card h4 {
        font-size: 1.5rem;
    }
}

/* Enhanced Dark Mode - Modern 2024 Approach */
@media (prefers-color-scheme: dark) {
    :root {
        --background-primary: var(--dark-bg-primary);
        --background-secondary: var(--dark-bg-secondary);
        --background-tertiary: var(--dark-surface);
        --surface-color: var(--dark-bg-secondary);
        --border-color: #374151;
        --border-hover: #4b5563;
        --text-primary: var(--dark-text-primary);
        --text-secondary: var(--dark-text-secondary);
        --text-muted: #9ca3af;
    }
    
    body {
        background-color: var(--background-primary);
        color: var(--text-primary);
    }
    
    .card {
        background: var(--surface-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .card-header {
        background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
        border-bottom-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control, .form-select {
        background: var(--surface-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .form-control:focus, .form-select:focus {
        background: var(--surface-color);
        border-color: var(--primary-color);
        color: var(--text-primary);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
    }
    
    .btn-secondary {
        background: var(--surface-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
    
    .btn-secondary:hover {
        background: var(--background-tertiary);
        border-color: var(--border-hover);
        color: var(--text-primary);
    }
    
    .navbar {
        background: var(--surface-color) !important;
        border-bottom: 1px solid var(--border-color);
    }
    
    .navbar-brand, .nav-link {
        color: var(--text-primary) !important;
    }
    
    .alert {
        background: var(--surface-color);
        border-color: var(--border-color);
        color: var(--text-primary);
    }
}

/* Modern Utility Classes - 2024 Design System */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.text-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) 1;
    border-radius: var(--border-radius);
}

/* Enhanced Shadow System */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--box-shadow-sm) !important; }
.shadow { box-shadow: var(--box-shadow) !important; }
.shadow-lg { box-shadow: var(--box-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--box-shadow-xl) !important; }

/* Spacing Utilities */
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }

.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }

/* Border Radius Utilities */
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: 9999px !important; }

/* Background Utilities */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-surface { background-color: var(--surface-color) !important; }
.bg-muted { background-color: var(--background-secondary) !important; }

/* Text Color Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

/* Modern Badge System */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background: var(--primary-light);
    color: var(--primary-color);
}

.badge-success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.badge-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

/* Focus Visible Enhancement */
.focus-ring:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
    border-radius: var(--border-radius);
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-primary: #000000;
        --text-secondary: #000000;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
}

/* Modern Animation System - 2024 Enhancements */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(24px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-32px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Hover Animations */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* Loading Animations */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Email List Styling */
.email-list {
    max-height: 400px;
    overflow-y: auto;
}

.email-list::-webkit-scrollbar {
    width: 6px;
}

.email-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.email-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.email-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Campaign Status Badges */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-running {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-paused {
    background-color: #fff3cd;
    color: #856404;
}

.status-failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Template Cards */
.template-card {
    cursor: pointer;
    transition: var(--transition);
}

.template-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0.25rem 0.5rem rgba(13, 110, 253, 0.25);
}

.template-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(13, 110, 253, 0.05);
}

/* SMTP Configuration Cards */
.smtp-card {
    position: relative;
    overflow: hidden;
}

.smtp-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--success-color);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.smtp-card.active::before {
    opacity: 1;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        background-color: white;
        color: black;
    }
}