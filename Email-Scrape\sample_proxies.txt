# Sample Proxy Configuration File
# Format: protocol://username:password@host:port
# Or simple format: host:port

# HTTP Proxies (replace with real proxy servers)
# http://proxy1.example.com:8080
# http://proxy2.example.com:3128
# http://username:<EMAIL>:8080

# HTTPS Proxies
# https://proxy4.example.com:8080
# https://username:<EMAIL>:3128

# SOCKS Proxies
# socks5://proxy6.example.com:1080
# socks5://username:<EMAIL>:1080

# Instructions:
# 1. Replace the example proxies above with real proxy servers
# 2. Remove the # at the beginning of each line to activate the proxy
# 3. Save this file and run: python proxy_manager.py --load sample_proxies.txt
# 4. Or manually add proxies using the GUI or CLI interface

# Free proxy sources (use with caution):
# - https://www.proxy-list.download/
# - https://free-proxy-list.net/
# - https://www.proxynova.com/proxy-server-list/

# Paid proxy services (recommended for production):
# - Bright Data (formerly Luminati)
# - Oxylabs
# - Smartproxy
# - ProxyMesh
# - Storm Proxies