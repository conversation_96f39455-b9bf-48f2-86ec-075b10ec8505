#!/usr/bin/env python3
"""
Proxy Manager for Email Scraper Pro
Utility to manage, test, and configure proxies
"""

import argparse
import sys
from pathlib import Path
from config import get_config, ProxyConfig
from loguru import logger

def add_proxy_interactive():
    """Add a proxy interactively"""
    print("\nAdd New Proxy")
    print("=" * 20)
    
    host = input("Proxy host/IP: ").strip()
    if not host:
        print("Host is required!")
        return
    
    try:
        port = int(input("Proxy port: ").strip())
    except ValueError:
        print("Invalid port number!")
        return
    
    protocol = input("Protocol (http/https/socks4/socks5) [http]: ").strip().lower() or "http"
    if protocol not in ['http', 'https', 'socks4', 'socks5']:
        print("Invalid protocol!")
        return
    
    username = input("Username (optional): ").strip() or None
    password = input("Password (optional): ").strip() or None
    
    config = get_config()
    config.add_proxy(host, port, username, password, protocol)
    config.save_config()
    
    print(f"\nProxy added successfully: {protocol}://{host}:{port}")

def list_proxies():
    """List all configured proxies"""
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured.")
        return
    
    print(f"\nConfigured Proxies ({len(config.proxies)} total):")
    print("=" * 40)
    
    for i, proxy in enumerate(config.proxies, 1):
        auth_info = " (with auth)" if proxy.username else ""
        print(f"{i:2d}. {proxy}{auth_info}")

def test_proxies():
    """Test all configured proxies"""
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured to test.")
        return
    
    print(f"\nTesting {len(config.proxies)} proxies...")
    print("=" * 40)
    
    working_proxies = config.test_all_proxies()
    
    print(f"\nTest Results:")
    print(f"Working proxies: {len(working_proxies)}/{len(config.proxies)}")
    
    if working_proxies:
        print("\nWorking proxies:")
        for proxy in working_proxies:
            print(f"  ✓ {proxy}")
    
    failed_proxies = [p for p in config.proxies if p not in working_proxies]
    if failed_proxies:
        print("\nFailed proxies:")
        for proxy in failed_proxies:
            print(f"  ✗ {proxy}")

def remove_proxy_interactive():
    """Remove a proxy interactively"""
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured to remove.")
        return
    
    list_proxies()
    
    try:
        choice = int(input("\nEnter proxy number to remove: ").strip())
        if 1 <= choice <= len(config.proxies):
            proxy = config.proxies[choice - 1]
            config.remove_proxy(proxy.host, proxy.port)
            config.save_config()
            print(f"Removed proxy: {proxy}")
        else:
            print("Invalid proxy number!")
    except ValueError:
        print("Invalid input!")

def load_proxies_from_file(file_path: str):
    """Load proxies from a file"""
    config = get_config()
    
    if not Path(file_path).exists():
        print(f"File not found: {file_path}")
        return
    
    initial_count = len(config.proxies)
    config.load_proxies_from_file(file_path)
    config.save_config()
    
    added_count = len(config.proxies) - initial_count
    print(f"Loaded {added_count} new proxies from {file_path}")
    print(f"Total proxies: {len(config.proxies)}")

def clear_all_proxies():
    """Clear all configured proxies"""
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured.")
        return
    
    confirm = input(f"Are you sure you want to remove all {len(config.proxies)} proxies? (y/N): ")
    if confirm.lower() == 'y':
        config.proxies.clear()
        config.save_config()
        print("All proxies removed.")
    else:
        print("Operation cancelled.")

def export_proxies(file_path: str):
    """Export proxies to a file"""
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured to export.")
        return
    
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("# Exported Proxy Configuration\n")
            f.write(f"# Generated on {logger.opt(colors=False).info('').record['time']}\n\n")
            
            for proxy in config.proxies:
                if proxy.username and proxy.password:
                    line = f"{proxy.protocol}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}\n"
                else:
                    line = f"{proxy.protocol}://{proxy.host}:{proxy.port}\n"
                f.write(line)
        
        print(f"Exported {len(config.proxies)} proxies to {file_path}")
        
    except Exception as e:
        print(f"Failed to export proxies: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Proxy Manager for Email Scraper Pro")
    parser.add_argument('--add', action='store_true', help='Add a new proxy interactively')
    parser.add_argument('--list', action='store_true', help='List all configured proxies')
    parser.add_argument('--test', action='store_true', help='Test all configured proxies')
    parser.add_argument('--remove', action='store_true', help='Remove a proxy interactively')
    parser.add_argument('--load', type=str, help='Load proxies from file')
    parser.add_argument('--export', type=str, help='Export proxies to file')
    parser.add_argument('--clear', action='store_true', help='Clear all proxies')
    
    args = parser.parse_args()
    
    if args.add:
        add_proxy_interactive()
    elif args.list:
        list_proxies()
    elif args.test:
        test_proxies()
    elif args.remove:
        remove_proxy_interactive()
    elif args.load:
        load_proxies_from_file(args.load)
    elif args.export:
        export_proxies(args.export)
    elif args.clear:
        clear_all_proxies()
    else:
        # Interactive menu
        while True:
            print("\nProxy Manager - Email Scraper Pro")
            print("=" * 35)
            print("1. List proxies")
            print("2. Add proxy")
            print("3. Remove proxy")
            print("4. Test proxies")
            print("5. Load proxies from file")
            print("6. Export proxies to file")
            print("7. Clear all proxies")
            print("8. Exit")
            
            choice = input("\nSelect option (1-8): ").strip()
            
            if choice == '1':
                list_proxies()
            elif choice == '2':
                add_proxy_interactive()
            elif choice == '3':
                remove_proxy_interactive()
            elif choice == '4':
                test_proxies()
            elif choice == '5':
                file_path = input("Enter file path: ").strip()
                if file_path:
                    load_proxies_from_file(file_path)
            elif choice == '6':
                file_path = input("Enter export file path: ").strip()
                if file_path:
                    export_proxies(file_path)
            elif choice == '7':
                clear_all_proxies()
            elif choice == '8':
                print("Goodbye!")
                break
            else:
                print("Invalid choice!")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)