#!/usr/bin/env python3
"""
Advanced Email Delivery System - Usage Examples
Demonstrates how to use the advanced email delivery system with all features
"""

import asyncio
import json
import os
from pathlib import Path
from loguru import logger

# Import the advanced delivery system
from advanced_email_delivery import (
    AdvancedEmailDelivery,
    AttachmentConfig,
    ObfuscationConfig,
    AntiSpamConfig
)

class AdvancedDeliveryExamples:
    """Examples and demonstrations of the advanced email delivery system"""
    
    def __init__(self):
        self.delivery_system = AdvancedEmailDelivery()
    
    async def example_basic_obfuscated_email(self):
        """Example: Send basic email with obfuscation"""
        logger.info("Example 1: Basic Obfuscated Email")
        
        # Configure obfuscation
        obf_config = ObfuscationConfig(
            randomize_headers=True,
            vary_content=True,
            use_html_entities=True,
            randomize_whitespace=True,
            use_unicode_variants=True,
            insert_invisible_chars=True
        )
        
        # Send email
        results = await self.delivery_system.send_advanced_email(
            to_emails=["<EMAIL>"],
            subject="Business Opportunity - Confidential",
            body="Dear valued partner, we have an exclusive business opportunity that requires your immediate attention.",
            html_body="<html><body><h2>Exclusive Business Opportunity</h2><p>Dear valued partner, we have an <strong>exclusive business opportunity</strong> that requires your immediate attention.</p></body></html>",
            obfuscation_config=obf_config,
            sender_name="Business Development Team"
        )
        
        logger.info(f"Results: {json.dumps(results, indent=2)}")
        return results
    
    async def example_email_with_attachments(self):
        """Example: Send email with multiple attachments"""
        logger.info("Example 2: Email with Attachments")
        
        # Create sample attachment files for demonstration
        self._create_sample_files()
        
        # Configure attachments
        attachments = [
            AttachmentConfig(
                file_path="sample_document.pdf",
                filename="Business_Proposal.pdf",
                obfuscate_name=True,
                max_size_mb=10.0
            ),
            AttachmentConfig(
                file_path="sample_spreadsheet.xlsx",
                filename="Financial_Data.xlsx",
                obfuscate_name=True
            ),
            AttachmentConfig(
                file_path="sample_image.jpg",
                inline=True,
                content_id="company_logo",
                obfuscate_name=False
            )
        ]
        
        # HTML body with inline image
        html_body = """
        <html>
        <body>
            <img src="cid:company_logo" alt="Company Logo" style="width:200px;">
            <h2>Business Proposal</h2>
            <p>Please find attached our comprehensive business proposal and financial projections.</p>
            <p>We look forward to your response.</p>
        </body>
        </html>
        """
        
        results = await self.delivery_system.send_advanced_email(
            to_emails=["<EMAIL>"],
            subject="Business Proposal - Please Review Attached Documents",
            body="Please find attached our business proposal and supporting documents.",
            html_body=html_body,
            attachments=attachments,
            sender_name="Carlos Fernandez Law Firm"
        )
        
        logger.info(f"Results: {json.dumps(results, indent=2)}")
        return results
    
    async def example_anti_spam_delivery(self):
        """Example: Send emails with anti-spam measures"""
        logger.info("Example 3: Anti-Spam Email Delivery")
        
        # Configure anti-spam settings
        anti_spam_config = AntiSpamConfig(
            check_spf=True,
            check_dkim=True,
            check_dmarc=True,
            validate_mx_records=True,
            rate_limit_per_hour=30,
            delay_between_emails=5.0,
            randomize_delays=True,
            use_reputation_warming=True
        )
        
        # Configure advanced obfuscation
        obf_config = ObfuscationConfig(
            randomize_headers=True,
            vary_content=True,
            use_html_entities=True,
            randomize_whitespace=True,
            split_links=True,
            use_unicode_variants=True,
            insert_invisible_chars=True,
            use_base64_encoding=False
        )
        
        # Multiple recipients
        recipients = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        results = await self.delivery_system.send_advanced_email(
            to_emails=recipients,
            subject="Legal Services - Consultation Available",
            body="We are pleased to offer our professional legal services for your business needs.",
            html_body="<html><body><h2>Professional Legal Services</h2><p>We are pleased to offer our <strong>professional legal services</strong> for your business needs.</p><p><a href='https://example.com/contact'>Contact us today</a> for a consultation.</p></body></html>",
            obfuscation_config=obf_config,
            anti_spam_config=anti_spam_config,
            sender_name="Carlos Fernandez Law Firm",
            reply_to="<EMAIL>",
            custom_headers={
                "X-Campaign-ID": "legal_services_2025",
                "X-Client-Type": "business"
            }
        )
        
        logger.info(f"Results: {json.dumps(results, indent=2)}")
        return results
    
    async def example_bulk_campaign(self):
        """Example: Bulk email campaign with advanced features"""
        logger.info("Example 4: Bulk Email Campaign")
        
        # Load recipient list (in real scenario, load from database/CSV)
        recipients = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        # Configure for bulk sending
        anti_spam_config = AntiSpamConfig(
            rate_limit_per_hour=100,
            delay_between_emails=3.0,
            randomize_delays=True,
            validate_mx_records=True,
            track_bounces=True
        )
        
        obf_config = ObfuscationConfig(
            randomize_headers=True,
            vary_content=True,
            use_html_entities=True,
            randomize_whitespace=True,
            insert_invisible_chars=True
        )
        
        # Create personalized content template
        subject_template = "Legal Consultation - {recipient_name}"
        body_template = """
        Dear {recipient_name},
        
        We hope this message finds you well. Our law firm specializes in {practice_area} 
        and we believe we can provide valuable assistance for your legal needs.
        
        Best regards,
        Carlos Fernandez Law Firm
        """
        
        html_template = """
        <html>
        <body>
            <h2>Legal Consultation Opportunity</h2>
            <p>Dear {recipient_name},</p>
            <p>We hope this message finds you well. Our law firm specializes in <strong>{practice_area}</strong> 
            and we believe we can provide valuable assistance for your legal needs.</p>
            <p>Best regards,<br>Carlos Fernandez Law Firm</p>
        </body>
        </html>
        """
        
        # Send personalized emails
        all_results = []
        for i, recipient in enumerate(recipients):
            # Personalize content
            recipient_name = f"Client {i+1}"
            practice_area = ["Corporate Law", "Real Estate", "Immigration", "Family Law", "Criminal Defense"][i % 5]
            
            personalized_subject = subject_template.format(recipient_name=recipient_name)
            personalized_body = body_template.format(
                recipient_name=recipient_name,
                practice_area=practice_area
            )
            personalized_html = html_template.format(
                recipient_name=recipient_name,
                practice_area=practice_area
            )
            
            # Send individual email
            result = await self.delivery_system.send_advanced_email(
                to_emails=[recipient],
                subject=personalized_subject,
                body=personalized_body,
                html_body=personalized_html,
                obfuscation_config=obf_config,
                anti_spam_config=anti_spam_config,
                sender_name="Carlos Fernandez Law Firm"
            )
            
            all_results.append(result)
            logger.info(f"Sent email {i+1}/{len(recipients)} to {recipient}")
        
        # Aggregate results
        total_sent = sum(r['sent'] for r in all_results)
        total_failed = sum(r['failed'] for r in all_results)
        
        campaign_results = {
            "campaign_type": "bulk_legal_consultation",
            "total_recipients": len(recipients),
            "total_sent": total_sent,
            "total_failed": total_failed,
            "success_rate": (total_sent / len(recipients)) * 100,
            "individual_results": all_results
        }
        
        logger.info(f"Campaign Results: {json.dumps(campaign_results, indent=2)}")
        return campaign_results
    
    def _create_sample_files(self):
        """Create sample files for attachment examples"""
        # Create sample PDF content (placeholder)
        if not os.path.exists("sample_document.pdf"):
            with open("sample_document.pdf", "wb") as f:
                f.write(b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n")
        
        # Create sample Excel content (placeholder)
        if not os.path.exists("sample_spreadsheet.xlsx"):
            with open("sample_spreadsheet.xlsx", "wb") as f:
                f.write(b"PK\x03\x04\x14\x00\x00\x00\x08\x00")  # ZIP header for XLSX
        
        # Create sample image content (placeholder)
        if not os.path.exists("sample_image.jpg"):
            with open("sample_image.jpg", "wb") as f:
                f.write(b"\xff\xd8\xff\xe0\x00\x10JFIF")  # JPEG header
    
    async def run_all_examples(self):
        """Run all examples"""
        logger.info("Running Advanced Email Delivery Examples")
        logger.info("=" * 50)
        
        try:
            # Example 1: Basic obfuscated email
            await self.example_basic_obfuscated_email()
            
            # Example 2: Email with attachments
            await self.example_email_with_attachments()
            
            # Example 3: Anti-spam delivery
            await self.example_anti_spam_delivery()
            
            # Example 4: Bulk campaign
            await self.example_bulk_campaign()
            
            # Show final metrics
            metrics = self.delivery_system.get_delivery_metrics()
            logger.info("Final Delivery Metrics:")
            logger.info(json.dumps(metrics, indent=2))
            
            # Export delivery log
            log_file = self.delivery_system.export_delivery_log()
            logger.info(f"Delivery log exported to: {log_file}")
            
        except Exception as e:
            logger.error(f"Error running examples: {e}")

async def main():
    """Main function to run examples"""
    examples = AdvancedDeliveryExamples()
    await examples.run_all_examples()

if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
