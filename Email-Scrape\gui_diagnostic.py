#!/usr/bin/env python3
"""
GUI Diagnostic and Fix Tool
Diagnoses and fixes GUI visibility issues
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import subprocess
import time
import threading

def check_display_settings():
    """Check Windows display settings"""
    print("\n=== DISPLAY DIAGNOSTIC ===")
    
    try:
        # Check if multiple monitors
        root = tk.Tk()
        root.withdraw()
        
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        
        print(f"Screen resolution: {screen_width}x{screen_height}")
        
        # Check DPI scaling
        try:
            import ctypes
            user32 = ctypes.windll.user32
            user32.SetProcessDPIAware()
            
            actual_width = user32.GetSystemMetrics(0)
            actual_height = user32.GetSystemMetrics(1)
            
            dpi_scale = screen_width / actual_width if actual_width > 0 else 1
            print(f"DPI scaling: {dpi_scale:.2f}x")
            
        except Exception as e:
            print(f"Could not check DPI scaling: {e}")
        
        root.destroy()
        
    except Exception as e:
        print(f"Display check failed: {e}")

def force_window_visible(window, title="BMD EMAIL SCRAPPER"):
    """Force a window to be visible using multiple methods"""
    print(f"\nForcing window '{title}' to be visible...")
    
    try:
        # Method 1: Basic visibility
        window.deiconify()
        window.lift()
        window.focus_force()
        
        # Method 2: Topmost temporarily
        window.attributes('-topmost', True)
        window.after(2000, lambda: window.attributes('-topmost', False))
        
        # Method 3: Center on screen
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")
        
        # Method 4: Flash taskbar
        try:
            import ctypes
            hwnd = int(window.wm_frame(), 16)
            ctypes.windll.user32.FlashWindow(hwnd, True)
        except:
            pass
        
        # Method 5: Force to front
        window.after(100, lambda: window.tkraise())
        
        print(f"✓ Applied visibility fixes to '{title}'")
        
    except Exception as e:
        print(f"✗ Error forcing visibility: {e}")

def create_diagnostic_window():
    """Create diagnostic window with multiple visibility tests"""
    print("\n=== CREATING DIAGNOSTIC WINDOW ===")
    
    root = tk.Tk()
    root.title("BMD EMAIL SCRAPPER - Enhanced Diagnostic Tool")
    root.geometry("900x700")
    root.configure(bg='#000000')
    
    # Apply visibility fixes
    force_window_visible(root, "Diagnostic Tool")
    
    # Main container with padding
    main_container = tk.Frame(root, bg='#000000')
    main_container.pack(fill='both', expand=True, padx=30, pady=20)
    
    # Enhanced Header with larger text
    header_frame = tk.Frame(main_container, bg='#111111', relief='raised', bd=3)
    header_frame.pack(fill='x', pady=(0, 20))
    
    header = tk.Label(header_frame, text="🔧 BMD EMAIL SCRAPPER DIAGNOSTIC TOOL", 
                     bg='#111111', fg='#00FF00', 
                     font=('Arial', 24, 'bold'))
    header.pack(pady=15)
    
    # Subtitle
    subtitle = tk.Label(header_frame, text="Enhanced System Analysis & GUI Testing", 
                       bg='#111111', fg='#FFFF00', 
                       font=('Arial', 14, 'italic'))
    subtitle.pack(pady=(0, 15))
    
    # Enhanced Status display with scrollbar
    status_frame = tk.Frame(main_container, bg='#222222', relief='sunken', bd=3)
    status_frame.pack(fill='both', expand=True, pady=(0, 20))
    
    # Status header
    status_header = tk.Label(status_frame, text="📊 SYSTEM DIAGNOSTIC RESULTS", 
                            bg='#222222', fg='#00CCFF', 
                            font=('Arial', 16, 'bold'))
    status_header.pack(pady=10)
    
    # Text area with scrollbar
    text_frame = tk.Frame(status_frame, bg='#222222')
    text_frame.pack(fill='both', expand=True, padx=15, pady=(0, 15))
    
    status_text = tk.Text(text_frame, bg='#000000', fg='#FFFFFF', 
                         font=('Consolas', 12), height=18, wrap='word',
                         selectbackground='#0066CC', selectforeground='white',
                         insertbackground='white')
    
    scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=status_text.yview)
    status_text.configure(yscrollcommand=scrollbar.set)
    
    status_text.pack(side='left', fill='both', expand=True)
    scrollbar.pack(side='right', fill='y')
    
    def update_status(message, color='#FFFFFF'):
        status_text.insert('end', message + '\n')
        if color != '#FFFFFF':
            # Add color tags for different message types
            start_line = status_text.index('end-2l')
            end_line = status_text.index('end-1l')
            tag_name = f"color_{len(status_text.tag_names())}"
            status_text.tag_add(tag_name, start_line, end_line)
            status_text.tag_config(tag_name, foreground=color)
        status_text.see('end')
        root.update()
    
    # Run diagnostics
    def run_diagnostics():
        update_status("=== BMD EMAIL SCRAPPER DIAGNOSTICS ===", '#00FFFF')
        update_status(f"Python version: {sys.version}")
        update_status(f"Tkinter version: {tk.TkVersion}")
        
        # Test 1: Basic GUI
        update_status("\n[TEST 1] Basic GUI functionality...", '#FFFF00')
        try:
            test_window = tk.Toplevel(root)
            test_window.title("Enhanced Test Window")
            test_window.geometry("400x300")
            test_window.configure(bg='#333333')
            force_window_visible(test_window, "Test Window")
            
            tk.Label(test_window, text="✅ GUI Test Successful!", bg='#333333', fg='#00FF00', 
                    font=('Arial', 16, 'bold')).pack(expand=True)
            
            update_status("✓ Basic GUI test passed", '#00FF00')
            
            # Auto-close test window
            test_window.after(3000, test_window.destroy)
            
        except Exception as e:
            update_status(f"✗ Basic GUI test failed: {e}", '#FF0000')
        
        # Test 2: Import bulk_gui
        update_status("\n[TEST 2] Importing bulk_gui module...", '#FFFF00')
        try:
            import bulk_gui
            update_status("✓ bulk_gui imported successfully", '#00FF00')
        except Exception as e:
            update_status(f"✗ bulk_gui import failed: {e}", '#FF0000')
        
        # Test 3: Configuration
        update_status("\n[TEST 3] Loading configuration...", '#FFFF00')
        try:
            from config import get_config
            config = get_config()
            update_status("✓ Configuration loaded successfully", '#00FF00')
        except Exception as e:
            update_status(f"✗ Configuration load failed: {e}", '#FF0000')
        
        update_status("\n=== DIAGNOSTIC COMPLETE ===", '#00FFFF')
        update_status("\nIf you can see this window, GUI is working!")
        update_status("Click 'Launch Main App' to start BMD EMAIL SCRAPPER")
    
    # Enhanced Button section
    button_frame = tk.Frame(main_container, bg='#333333', relief='raised', bd=3)
    button_frame.pack(fill='x', pady=(0, 10))
    
    # Button header
    button_header = tk.Label(button_frame, text="🎮 CONTROL PANEL", 
                            bg='#333333', fg='#FFAA00', 
                            font=('Arial', 16, 'bold'))
    button_header.pack(pady=10)
    
    # Button container
    btn_container = tk.Frame(button_frame, bg='#333333')
    btn_container.pack(pady=(0, 15))
    
    def launch_main_app():
        update_status("\n[LAUNCH] Starting BMD EMAIL SCRAPPER...", '#00FFFF')
        try:
            # Close diagnostic window
            root.withdraw()
            
            # Launch main app with fixes
            main_root = tk.Tk()
            
            # Import and create app
            import bulk_gui
            app = bulk_gui.BulkScraperGUI(main_root)
            
            # Force consent window to be visible if it exists
            if hasattr(app, 'consent_window'):
                force_window_visible(app.consent_window, "Consent Screen")
            
            # Force main window to be visible
            force_window_visible(main_root, "BMD EMAIL SCRAPPER")
            
            main_root.mainloop()
            
        except Exception as e:
            messagebox.showerror("Launch Error", f"Failed to launch: {e}")
            root.deiconify()
    
    # Create enhanced buttons with hover effects
    def create_enhanced_button(parent, text, command, bg_color, hover_color):
        btn = tk.Button(parent, text=text, command=command,
                       bg=bg_color, fg='white', font=('Arial', 14, 'bold'),
                       padx=25, pady=15, relief='raised', bd=3,
                       activebackground=hover_color, activeforeground='white')
        
        def on_enter(e):
            btn.config(bg=hover_color, relief='ridge')
        def on_leave(e):
            btn.config(bg=bg_color, relief='raised')
            
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        return btn
    
    # Create buttons with enhanced styling
    diag_btn = create_enhanced_button(btn_container, "🔍 Run Full Diagnostics", 
                                     run_diagnostics, '#0066CC', '#0088FF')
    diag_btn.pack(side='left', padx=15)
    
    launch_btn = create_enhanced_button(btn_container, "🚀 Launch Main Application", 
                                       launch_main_app, '#00AA00', '#00CC00')
    launch_btn.pack(side='left', padx=15)
    
    exit_btn = create_enhanced_button(btn_container, "❌ Exit Diagnostic Tool", 
                                     root.quit, '#CC0000', '#FF0000')
    exit_btn.pack(side='left', padx=15)
    
    # Auto-run diagnostics
    root.after(1000, run_diagnostics)
    
    print("✓ Diagnostic window created and should be visible")
    print("\nLook for a black window with green text!")
    print("If you don't see it, check your taskbar or try Alt+Tab")
    
    return root

def main():
    print("BMD EMAIL SCRAPPER - GUI Diagnostic Tool")
    print("=" * 50)
    
    # Run display diagnostics
    check_display_settings()
    
    # Create and show diagnostic window
    root = create_diagnostic_window()
    root.mainloop()

if __name__ == "__main__":
    main()