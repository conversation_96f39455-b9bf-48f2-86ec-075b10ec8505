#!/usr/bin/env python3
"""
Mass Email Sender
Handles bulk email campaigns with rate limiting, error handling, and delivery tracking
"""

import asyncio
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import os
from enum import Enum
from loguru import logger
import threading
from queue import Queue, Empty
import uuid
from email_validator import validate_email, EmailNotValidError

# Import our modules
from smtp_config import SMTPManager
from email_templates import TemplateManager

class CampaignStatus(Enum):
    """Campaign status enumeration"""
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class EmailStatus(Enum):
    """Individual email status enumeration"""
    PENDING = "pending"
    SENDING = "sending"
    SENT = "sent"
    FAILED = "failed"
    BOUNCED = "bounced"
    SKIPPED = "skipped"

@dataclass
class EmailRecipient:
    """Email recipient with personalization data"""
    email: str
    variables: Dict[str, Any]
    status: EmailStatus = EmailStatus.PENDING
    sent_at: Optional[str] = None
    error_message: Optional[str] = None
    attempts: int = 0
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EmailRecipient':
        data['status'] = EmailStatus(data['status'])
        return cls(**data)

@dataclass
class CampaignConfig:
    """Email campaign configuration"""
    name: str
    template_id: str
    smtp_config_name: str
    recipients: List[EmailRecipient]
    send_rate: float = 1.0  # emails per second
    max_retries: int = 3
    retry_delay: float = 300.0  # 5 minutes
    batch_size: int = 50
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    track_opens: bool = False
    track_clicks: bool = False
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['recipients'] = [recipient.to_dict() for recipient in self.recipients]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'CampaignConfig':
        data['recipients'] = [EmailRecipient.from_dict(r) for r in data['recipients']]
        return cls(**data)

@dataclass
class Campaign:
    """Email campaign with tracking"""
    id: str
    config: CampaignConfig
    status: CampaignStatus = CampaignStatus.DRAFT
    created_at: str = ""
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    total_recipients: int = 0
    sent_count: int = 0
    failed_count: int = 0
    pending_count: int = 0
    progress_percentage: float = 0.0
    current_batch: int = 0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        self.total_recipients = len(self.config.recipients)
        self.update_counts()
    
    def update_counts(self):
        """Update email counts and progress"""
        self.sent_count = sum(1 for r in self.config.recipients if r.status == EmailStatus.SENT)
        self.failed_count = sum(1 for r in self.config.recipients if r.status == EmailStatus.FAILED)
        self.pending_count = sum(1 for r in self.config.recipients if r.status == EmailStatus.PENDING)
        
        if self.total_recipients > 0:
            self.progress_percentage = (self.sent_count + self.failed_count) / self.total_recipients * 100
    
    def to_dict(self) -> Dict:
        data = asdict(self)
        data['status'] = self.status.value
        data['config'] = self.config.to_dict()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Campaign':
        data['status'] = CampaignStatus(data['status'])
        data['config'] = CampaignConfig.from_dict(data['config'])
        return cls(**data)

class MassEmailSender:
    """Mass email sender with rate limiting and tracking"""
    
    def __init__(self, campaigns_file: str = 'email_campaigns.json'):
        self.campaigns_file = campaigns_file
        self.campaigns: Dict[str, Campaign] = {}
        self.smtp_manager = SMTPManager()
        self.template_manager = TemplateManager()
        self.running_campaigns: Dict[str, threading.Thread] = {}
        self.campaign_queues: Dict[str, Queue] = {}
        self.stop_events: Dict[str, threading.Event] = {}
        self.progress_callbacks: Dict[str, Callable] = {}
        
        self.load_campaigns()
    
    def load_campaigns(self) -> None:
        """Load campaigns from file"""
        try:
            if os.path.exists(self.campaigns_file):
                with open(self.campaigns_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for campaign_data in data.get('campaigns', []):
                    campaign = Campaign.from_dict(campaign_data)
                    self.campaigns[campaign.id] = campaign
                    
                logger.info(f"Loaded {len(self.campaigns)} email campaigns")
            else:
                logger.info("No campaigns file found, starting with empty campaigns")
        except Exception as e:
            logger.error(f"Error loading campaigns: {e}")
    
    def save_campaigns(self) -> None:
        """Save campaigns to file"""
        try:
            data = {
                'campaigns': [campaign.to_dict() for campaign in self.campaigns.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.campaigns_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(self.campaigns)} email campaigns")
        except Exception as e:
            logger.error(f"Error saving campaigns: {e}")
    
    def create_campaign(self, name: str, template_id: str, smtp_config_name: str,
                       recipients_data: List[Dict], **kwargs) -> str:
        """Create a new email campaign"""
        try:
            campaign_id = str(uuid.uuid4())
            
            # Validate template exists
            if not self.template_manager.get_template(template_id):
                raise ValueError(f"Template {template_id} not found")
            
            # Validate SMTP config exists
            if smtp_config_name not in self.smtp_manager.get_config_list():
                raise ValueError(f"SMTP config '{smtp_config_name}' not found")
            
            # Create recipients
            recipients = []
            for recipient_data in recipients_data:
                email = recipient_data.get('email', '').strip().lower()
                
                # Validate email
                try:
                    valid_email = validate_email(email)
                    email = valid_email.email
                except EmailNotValidError:
                    logger.warning(f"Invalid email address: {email}")
                    continue
                
                variables = recipient_data.get('variables', {})
                recipients.append(EmailRecipient(email=email, variables=variables))
            
            if not recipients:
                raise ValueError("No valid recipients provided")
            
            # Create campaign config
            config = CampaignConfig(
                name=name,
                template_id=template_id,
                smtp_config_name=smtp_config_name,
                recipients=recipients,
                **kwargs
            )
            
            # Create campaign
            campaign = Campaign(id=campaign_id, config=config)
            self.campaigns[campaign_id] = campaign
            
            self.save_campaigns()
            logger.info(f"Created campaign '{name}' with {len(recipients)} recipients")
            return campaign_id
            
        except Exception as e:
            logger.error(f"Error creating campaign: {e}")
            raise e
    
    def start_campaign(self, campaign_id: str, progress_callback: Callable = None) -> bool:
        """Start an email campaign"""
        try:
            if campaign_id not in self.campaigns:
                logger.error(f"Campaign {campaign_id} not found")
                return False
            
            campaign = self.campaigns[campaign_id]
            
            if campaign.status != CampaignStatus.DRAFT and campaign.status != CampaignStatus.PAUSED:
                logger.error(f"Campaign {campaign_id} cannot be started (status: {campaign.status.value})")
                return False
            
            # Check if already running
            if campaign_id in self.running_campaigns:
                logger.warning(f"Campaign {campaign_id} is already running")
                return False
            
            # Set up campaign
            campaign.status = CampaignStatus.RUNNING
            campaign.started_at = datetime.now().isoformat()
            
            # Create stop event and queue
            stop_event = threading.Event()
            self.stop_events[campaign_id] = stop_event
            
            # Set progress callback
            if progress_callback:
                self.progress_callbacks[campaign_id] = progress_callback
            
            # Start campaign thread
            campaign_thread = threading.Thread(
                target=self._run_campaign,
                args=(campaign_id, stop_event),
                daemon=True
            )
            
            self.running_campaigns[campaign_id] = campaign_thread
            campaign_thread.start()
            
            self.save_campaigns()
            logger.info(f"Started campaign {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting campaign: {e}")
            return False
    
    def pause_campaign(self, campaign_id: str) -> bool:
        """Pause a running campaign"""
        try:
            if campaign_id not in self.campaigns:
                logger.error(f"Campaign {campaign_id} not found")
                return False
            
            campaign = self.campaigns[campaign_id]
            
            if campaign.status != CampaignStatus.RUNNING:
                logger.error(f"Campaign {campaign_id} is not running")
                return False
            
            # Signal stop
            if campaign_id in self.stop_events:
                self.stop_events[campaign_id].set()
            
            campaign.status = CampaignStatus.PAUSED
            self.save_campaigns()
            
            logger.info(f"Paused campaign {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error pausing campaign: {e}")
            return False
    
    def stop_campaign(self, campaign_id: str) -> bool:
        """Stop a campaign completely"""
        try:
            if campaign_id not in self.campaigns:
                logger.error(f"Campaign {campaign_id} not found")
                return False
            
            campaign = self.campaigns[campaign_id]
            
            # Signal stop
            if campaign_id in self.stop_events:
                self.stop_events[campaign_id].set()
            
            campaign.status = CampaignStatus.CANCELLED
            campaign.completed_at = datetime.now().isoformat()
            
            # Clean up
            self._cleanup_campaign(campaign_id)
            
            self.save_campaigns()
            logger.info(f"Stopped campaign {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping campaign: {e}")
            return False
    
    def _run_campaign(self, campaign_id: str, stop_event: threading.Event) -> None:
        """Run campaign in separate thread"""
        try:
            campaign = self.campaigns[campaign_id]
            config = campaign.config
            
            logger.info(f"Running campaign {campaign_id}: {config.name}")
            
            # Get pending recipients
            pending_recipients = [r for r in config.recipients if r.status == EmailStatus.PENDING]
            
            # Process in batches
            batch_size = config.batch_size
            send_delay = 1.0 / config.send_rate if config.send_rate > 0 else 1.0
            
            for i in range(0, len(pending_recipients), batch_size):
                if stop_event.is_set():
                    logger.info(f"Campaign {campaign_id} stopped by user")
                    break
                
                batch = pending_recipients[i:i + batch_size]
                campaign.current_batch = i // batch_size + 1
                
                logger.info(f"Processing batch {campaign.current_batch} ({len(batch)} emails)")
                
                for recipient in batch:
                    if stop_event.is_set():
                        break
                    
                    self._send_single_email(campaign_id, recipient)
                    
                    # Update progress
                    campaign.update_counts()
                    if campaign_id in self.progress_callbacks:
                        try:
                            self.progress_callbacks[campaign_id](campaign)
                        except Exception as e:
                            logger.warning(f"Progress callback error: {e}")
                    
                    # Rate limiting
                    if send_delay > 0:
                        time.sleep(send_delay)
                
                # Save progress after each batch
                self.save_campaigns()
            
            # Campaign completed
            if not stop_event.is_set():
                campaign.status = CampaignStatus.COMPLETED
                campaign.completed_at = datetime.now().isoformat()
                logger.info(f"Campaign {campaign_id} completed successfully")
            
            # Final cleanup
            self._cleanup_campaign(campaign_id)
            self.save_campaigns()
            
        except Exception as e:
            logger.error(f"Campaign {campaign_id} failed: {e}")
            campaign.status = CampaignStatus.FAILED
            campaign.error_message = str(e)
            campaign.completed_at = datetime.now().isoformat()
            self._cleanup_campaign(campaign_id)
            self.save_campaigns()
    
    def _send_single_email(self, campaign_id: str, recipient: EmailRecipient) -> None:
        """Send a single email to recipient"""
        try:
            campaign = self.campaigns[campaign_id]
            config = campaign.config
            
            recipient.status = EmailStatus.SENDING
            recipient.attempts += 1
            
            # Render template
            rendered = self.template_manager.render_template(config.template_id, recipient.variables)
            if not rendered:
                raise Exception("Failed to render email template")
            
            # Send email
            success = self.smtp_manager.send_email(
                to_emails=[recipient.email],
                subject=rendered['subject'],
                body=rendered['text_body'],
                html_body=rendered['html_body'],
                config_name=config.smtp_config_name
            )
            
            if success:
                recipient.status = EmailStatus.SENT
                recipient.sent_at = datetime.now().isoformat()
                logger.debug(f"Email sent to {recipient.email}")
            else:
                raise Exception("SMTP send failed")
                
        except Exception as e:
            recipient.status = EmailStatus.FAILED
            recipient.error_message = str(e)
            logger.error(f"Failed to send email to {recipient.email}: {e}")
            
            # Retry logic
            if recipient.attempts < config.max_retries:
                recipient.status = EmailStatus.PENDING
                logger.info(f"Will retry sending to {recipient.email} (attempt {recipient.attempts + 1})")
    
    def _cleanup_campaign(self, campaign_id: str) -> None:
        """Clean up campaign resources"""
        if campaign_id in self.running_campaigns:
            del self.running_campaigns[campaign_id]
        if campaign_id in self.stop_events:
            del self.stop_events[campaign_id]
        if campaign_id in self.progress_callbacks:
            del self.progress_callbacks[campaign_id]
        if campaign_id in self.campaign_queues:
            del self.campaign_queues[campaign_id]
    
    def get_campaign(self, campaign_id: str) -> Optional[Campaign]:
        """Get campaign by ID"""
        return self.campaigns.get(campaign_id)
    
    def get_all_campaigns(self) -> List[Campaign]:
        """Get all campaigns"""
        return list(self.campaigns.values())
    
    def get_campaign_status(self, campaign_id: str) -> Optional[Dict]:
        """Get campaign status summary"""
        campaign = self.get_campaign(campaign_id)
        if not campaign:
            return None
        
        return {
            'id': campaign.id,
            'name': campaign.config.name,
            'status': campaign.status.value,
            'progress_percentage': campaign.progress_percentage,
            'total_recipients': campaign.total_recipients,
            'sent_count': campaign.sent_count,
            'failed_count': campaign.failed_count,
            'pending_count': campaign.pending_count,
            'current_batch': campaign.current_batch,
            'created_at': campaign.created_at,
            'started_at': campaign.started_at,
            'completed_at': campaign.completed_at,
            'error_message': campaign.error_message
        }
    
    def delete_campaign(self, campaign_id: str) -> bool:
        """Delete a campaign"""
        try:
            if campaign_id not in self.campaigns:
                logger.error(f"Campaign {campaign_id} not found")
                return False
            
            campaign = self.campaigns[campaign_id]
            
            # Stop if running
            if campaign.status == CampaignStatus.RUNNING:
                self.stop_campaign(campaign_id)
            
            # Delete
            del self.campaigns[campaign_id]
            self.save_campaigns()
            
            logger.info(f"Deleted campaign {campaign_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting campaign: {e}")
            return False
    
    def get_running_campaigns(self) -> List[str]:
        """Get list of currently running campaign IDs"""
        return [cid for cid, campaign in self.campaigns.items() 
                if campaign.status == CampaignStatus.RUNNING]
    
    def validate_campaign_data(self, template_id: str, smtp_config_name: str, 
                              recipients_data: List[Dict]) -> Dict[str, Any]:
        """Validate campaign data before creation"""
        errors = []
        warnings = []
        
        # Validate template
        template = self.template_manager.get_template(template_id)
        if not template:
            errors.append(f"Template {template_id} not found")
        
        # Validate SMTP config
        if smtp_config_name not in self.smtp_manager.get_config_list():
            errors.append(f"SMTP config '{smtp_config_name}' not found")
        
        # Validate recipients
        valid_recipients = 0
        invalid_emails = []
        missing_variables = set()
        
        for i, recipient_data in enumerate(recipients_data):
            email = recipient_data.get('email', '').strip()
            
            # Validate email
            try:
                validate_email(email)
                valid_recipients += 1
            except EmailNotValidError:
                invalid_emails.append(f"Row {i + 1}: {email}")
            
            # Check template variables
            if template:
                variables = recipient_data.get('variables', {})
                for var in template.variables:
                    if var not in variables:
                        missing_variables.add(var)
        
        if invalid_emails:
            warnings.append(f"Invalid email addresses: {len(invalid_emails)} found")
        
        if missing_variables:
            warnings.append(f"Missing template variables: {', '.join(missing_variables)}")
        
        if valid_recipients == 0:
            errors.append("No valid recipients found")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings,
            'valid_recipients': valid_recipients,
            'invalid_emails': invalid_emails[:10],  # Show first 10
            'missing_variables': list(missing_variables)
        }

# Example usage and testing
if __name__ == "__main__":
    # Initialize mass email sender
    sender = MassEmailSender()
    
    print("Mass Email Sender loaded successfully")
    print(f"Available campaigns: {len(sender.get_all_campaigns())}")
    print(f"Running campaigns: {len(sender.get_running_campaigns())}")