#!/usr/bin/env python3
"""
Email Scraper Pro - Configuration Management
Handles settings, proxies, and user preferences
"""

import json
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import random
from loguru import logger

@dataclass
class ProxyConfig:
    """Proxy configuration"""
    host: str
    port: int
    username: Optional[str] = None
    password: Optional[str] = None
    protocol: str = 'http'  # http, https, socks4, socks5
    
    def to_playwright_format(self) -> Dict[str, Any]:
        """Convert to Playwright proxy format"""
        proxy_dict = {
            'server': f'{self.protocol}://{self.host}:{self.port}'
        }
        
        if self.username and self.password:
            proxy_dict['username'] = self.username
            proxy_dict['password'] = self.password
        
        return proxy_dict
    
    def __str__(self):
        return f"{self.protocol}://{self.host}:{self.port}"

@dataclass
class ScrapingConfig:
    """Scraping configuration"""
    # Browser settings
    headless: bool = True
    user_agent: Optional[str] = None
    viewport_width: int = 1920
    viewport_height: int = 1080
    
    # Timing settings
    page_load_timeout: int = 30000  # milliseconds
    navigation_timeout: int = 30000  # milliseconds
    element_timeout: int = 10000  # milliseconds
    
    # Delays (in seconds)
    min_delay: float = 1.0
    max_delay: float = 3.0
    page_delay: float = 2.0
    
    # Retry settings
    max_retries: int = 3
    retry_delay: float = 5.0
    
    # Scraping limits
    max_pages_per_query: int = 10
    max_results_per_page: int = 50
    max_concurrent_pages: int = 3
    
    # Email extraction settings
    extract_emails: bool = True
    extract_phones: bool = True
    extract_linkedin: bool = True
    extract_names: bool = True
    extract_companies: bool = True
    
    # Content filtering
    min_email_length: int = 5
    max_email_length: int = 100
    exclude_domains: List[str] = None
    include_domains: List[str] = None
    
    def __post_init__(self):
        if self.exclude_domains is None:
            self.exclude_domains = [
                'example.com', 'test.com', 'localhost',
                'noreply.com', 'donotreply.com'
            ]
        if self.include_domains is None:
            self.include_domains = []

@dataclass
class DatabaseConfig:
    """Database configuration"""
    db_path: str = 'email_scraper.db'
    backup_enabled: bool = True
    backup_interval: int = 24  # hours
    max_backups: int = 7
    auto_cleanup: bool = True
    cleanup_days: int = 30

@dataclass
class ExportConfig:
    """Export configuration"""
    default_format: str = 'csv'
    include_duplicates: bool = False
    sort_by: str = 'extracted_date'  # email, name, company, extracted_date
    sort_order: str = 'desc'  # asc, desc
    max_export_rows: int = 10000
    
    # CSV settings
    csv_delimiter: str = ','
    csv_encoding: str = 'utf-8'
    
    # Excel settings
    excel_sheet_name: str = 'Contacts'
    excel_auto_filter: bool = True

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = 'INFO'
    file_enabled: bool = True
    file_path: str = 'email_scraper.log'
    file_rotation: str = '10 MB'
    file_retention: str = '7 days'
    console_enabled: bool = True
    format_string: str = '<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | {message}'

class ConfigManager:
    """Configuration manager for Email Scraper Pro"""
    
    def __init__(self, config_file: str = 'config.json'):
        self.config_file = Path(config_file)
        self.proxies: List[ProxyConfig] = []
        self.scraping = ScrapingConfig()
        self.database = DatabaseConfig()
        self.export = ExportConfig()
        self.logging = LoggingConfig()
        
        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
        ]
        
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Load proxies
                if 'proxies' in data:
                    self.proxies = [ProxyConfig(**proxy) for proxy in data['proxies']]
                
                # Load scraping config
                if 'scraping' in data:
                    self.scraping = ScrapingConfig(**data['scraping'])
                
                # Load database config
                if 'database' in data:
                    self.database = DatabaseConfig(**data['database'])
                
                # Load export config
                if 'export' in data:
                    self.export = ExportConfig(**data['export'])
                
                # Load logging config
                if 'logging' in data:
                    self.logging = LoggingConfig(**data['logging'])
                
                logger.info(f"Configuration loaded from {self.config_file}")
            else:
                logger.info("No config file found, using defaults")
                self.save_config()  # Create default config file
                
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            logger.info("Using default configuration")
    
    def save_config(self):
        """Save configuration to file"""
        try:
            data = {
                'proxies': [asdict(proxy) for proxy in self.proxies],
                'scraping': asdict(self.scraping),
                'database': asdict(self.database),
                'export': asdict(self.export),
                'logging': asdict(self.logging)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Configuration saved to {self.config_file}")
            
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
    
    def add_proxy(self, host: str, port: int, username: str = None, 
                  password: str = None, protocol: str = 'http'):
        """Add a proxy to the configuration"""
        proxy = ProxyConfig(
            host=host,
            port=port,
            username=username,
            password=password,
            protocol=protocol
        )
        self.proxies.append(proxy)
        logger.info(f"Added proxy: {proxy}")
    
    def remove_proxy(self, host: str, port: int):
        """Remove a proxy from the configuration"""
        self.proxies = [p for p in self.proxies if not (p.host == host and p.port == port)]
        logger.info(f"Removed proxy: {host}:{port}")
    
    def get_random_proxy(self) -> Optional[ProxyConfig]:
        """Get a random proxy from the list"""
        if not self.proxies:
            return None
        return random.choice(self.proxies)
    
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.user_agents)
    
    def load_proxies_from_file(self, file_path: str):
        """Load proxies from a text file
        
        Format: protocol://username:password@host:port
        Or: host:port
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            for line in lines:
                try:
                    if '://' in line:
                        # Full format: protocol://username:password@host:port
                        parts = line.split('://')
                        protocol = parts[0]
                        rest = parts[1]
                        
                        if '@' in rest:
                            auth, address = rest.split('@')
                            if ':' in auth:
                                username, password = auth.split(':', 1)
                            else:
                                username, password = auth, None
                        else:
                            username, password = None, None
                            address = rest
                        
                        host, port = address.split(':')
                        port = int(port)
                        
                    else:
                        # Simple format: host:port
                        host, port = line.split(':')
                        port = int(port)
                        protocol = 'http'
                        username, password = None, None
                    
                    self.add_proxy(host, port, username, password, protocol)
                    
                except Exception as e:
                    logger.warning(f"Failed to parse proxy line '{line}': {e}")
            
            logger.info(f"Loaded {len(self.proxies)} proxies from {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to load proxies from file: {e}")
    
    def test_proxy(self, proxy: ProxyConfig) -> bool:
        """Test if a proxy is working"""
        try:
            import requests
            
            proxy_dict = {
                'http': f'{proxy.protocol}://{proxy.host}:{proxy.port}',
                'https': f'{proxy.protocol}://{proxy.host}:{proxy.port}'
            }
            
            if proxy.username and proxy.password:
                proxy_dict['http'] = f'{proxy.protocol}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}'
                proxy_dict['https'] = f'{proxy.protocol}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}'
            
            response = requests.get('http://httpbin.org/ip', proxies=proxy_dict, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            logger.debug(f"Proxy test failed for {proxy}: {e}")
            return False
    
    def test_all_proxies(self) -> List[ProxyConfig]:
        """Test all proxies and return working ones"""
        working_proxies = []
        
        for proxy in self.proxies:
            logger.info(f"Testing proxy: {proxy}")
            if self.test_proxy(proxy):
                working_proxies.append(proxy)
                logger.success(f"Proxy working: {proxy}")
            else:
                logger.warning(f"Proxy not working: {proxy}")
        
        logger.info(f"Found {len(working_proxies)} working proxies out of {len(self.proxies)}")
        return working_proxies
    
    def get_delay(self) -> float:
        """Get a random delay between min and max delay"""
        return random.uniform(self.scraping.min_delay, self.scraping.max_delay)
    
    def should_extract_domain(self, email: str) -> bool:
        """Check if email domain should be extracted based on filters"""
        if not email or '@' not in email:
            return False
        
        domain = email.split('@')[1].lower()
        
        # Check exclude list
        if domain in [d.lower() for d in self.scraping.exclude_domains]:
            return False
        
        # Check include list (if specified)
        if self.scraping.include_domains:
            return domain in [d.lower() for d in self.scraping.include_domains]
        
        return True
    
    def update_scraping_config(self, **kwargs):
        """Update scraping configuration"""
        for key, value in kwargs.items():
            if hasattr(self.scraping, key):
                setattr(self.scraping, key, value)
                logger.info(f"Updated scraping config: {key} = {value}")
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self.proxies = []
        self.scraping = ScrapingConfig()
        self.database = DatabaseConfig()
        self.export = ExportConfig()
        self.logging = LoggingConfig()
        logger.info("Configuration reset to defaults")
    
    def export_config(self, file_path: str):
        """Export configuration to a file"""
        try:
            data = {
                'proxies': [asdict(proxy) for proxy in self.proxies],
                'scraping': asdict(self.scraping),
                'database': asdict(self.database),
                'export': asdict(self.export),
                'logging': asdict(self.logging)
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.success(f"Configuration exported to {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
            return False
    
    def import_config(self, file_path: str):
        """Import configuration from a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Backup current config
            backup_file = f"{self.config_file}.backup"
            self.export_config(backup_file)
            
            # Load new config
            if 'proxies' in data:
                self.proxies = [ProxyConfig(**proxy) for proxy in data['proxies']]
            
            if 'scraping' in data:
                self.scraping = ScrapingConfig(**data['scraping'])
            
            if 'database' in data:
                self.database = DatabaseConfig(**data['database'])
            
            if 'export' in data:
                self.export = ExportConfig(**data['export'])
            
            if 'logging' in data:
                self.logging = LoggingConfig(**data['logging'])
            
            # Save imported config
            self.save_config()
            
            logger.success(f"Configuration imported from {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to import config: {e}")
            return False

# Global config instance
config = ConfigManager()

# Convenience functions
def get_config() -> ConfigManager:
    """Get the global config instance"""
    return config

def reload_config():
    """Reload configuration from file"""
    global config
    config.load_config()

def save_config():
    """Save current configuration"""
    config.save_config()