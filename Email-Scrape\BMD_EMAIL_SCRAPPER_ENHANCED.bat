@echo off
title BMD EMAIL SCRAPPER - Enhanced Launcher
color 0A
echo.
echo ========================================
echo  BMD EMAIL SCRAPPER - Enhanced Launcher
echo ========================================
echo.
echo Starting application with enhanced compatibility...
echo Please wait while the application loads...
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    pause
    exit /b 1
)

:: Try multiple launch methods for maximum compatibility
echo Attempting Method 1: Direct GUI launch...
python -c "import sys; sys.path.insert(0, '.'); import tkinter as tk; root = tk.Tk(); root.withdraw(); import bulk_gui; root.deiconify(); root.state('zoomed'); root.lift(); root.focus_force(); root.attributes('-topmost', True); root.after(2000, lambda: root.attributes('-topmost', False)); app = bulk_gui.BulkScraperGUI(root); root.mainloop()" 2>error.log

if %ERRORLEVEL% EQU 0 (
    echo Application closed normally.
    if exist error.log del error.log
) else (
    echo Method 1 failed. Trying Method 2: Module launch...
    python bulk_gui.py 2>>error.log
    
    if %ERRORLEVEL% EQU 0 (
        echo Application closed normally.
        if exist error.log del error.log
    ) else (
        echo Method 2 failed. Trying Method 3: CLI fallback...
        python bulk_cli.py 2>>error.log
        
        if %ERRORLEVEL% EQU 0 (
            echo CLI application closed normally.
            if exist error.log del error.log
        ) else (
            echo.
            echo ========================================
            echo  ERROR: All launch methods failed!
            echo ========================================
            echo.
            echo Possible causes:
            echo 1. Windows Security is blocking the application
            echo 2. Missing Python dependencies
            echo 3. Antivirus software interference
            echo.
            echo Solutions:
            echo 1. Run Add_Windows_Security_Exclusion.bat as Administrator
            echo 2. Temporarily disable antivirus
            echo 3. Check error.log for details
            echo.
            if exist error.log (
                echo Error details:
                type error.log
            )
            echo.
            pause
        )
    )
)

echo.
echo Press any key to exit...
pause >nul