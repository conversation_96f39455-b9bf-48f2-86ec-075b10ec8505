# Core dependencies
loguru>=0.7.0
tkinter-tooltip>=2.0.0

# Web framework
Flask>=2.3.0
Flask-SocketIO>=5.3.0
Werkzeug>=2.3.0

# Email and SMTP
aiosmtplib>=3.0.0
email-validator>=2.0.0
dnspython>=2.4.0

# Template engine
Jinja2>=3.1.0

# HTTP server for tracking
aiohttp>=3.8.0

# Database
sqlite3  # Built-in with Python

# Async support
asyncio  # Built-in with Python

# Data handling
dataclasses  # Built-in with Python 3.7+
typing-extensions>=4.0.0

# JSON handling
orjson>=3.8.0  # Optional: faster JSON processing

# Security
cryptography>=41.0.0

# GUI (if not using system tkinter)
# tkinter  # Usually built-in with Python

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Additional utilities
pathlib  # Built-in with Python
datetime  # Built-in with Python
re  # Built-in with Python
base64  # Built-in with Python
hmac  # Built-in with Python
hashlib  # Built-in with Python
uuid  # Built-in with Python
urllib  # Built-in with Python
json  # Built-in with Python