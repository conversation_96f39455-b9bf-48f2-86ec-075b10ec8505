# BMD EMAIL SCRAPPER 🚀

**Enhanced Bulk Email Scraping & Generation Tool with Advanced GUI**

A powerful, user-friendly email scraping and generation application with enhanced visibility, modern UI design, and comprehensive functionality for bulk operations.

## ✨ Features

### 🎯 **Core Functionality**
- **Bulk Email Scraping** from multiple platforms (Google, Bing, DuckDuckGo, Yahoo)
- **AI-Powered Email Generation** with customizable templates
- **Combined Operations** for streamlined workflows
- **Advanced Search Configuration** with location and result limits
- **Real-time Progress Tracking** with detailed status updates

### 🖥️ **Enhanced User Interface**
- **Large, Readable Fonts** (28pt titles, 18pt headers, 14pt buttons)
- **Responsive Design** with 1600x1000 main window
- **Enhanced Interactivity** with hover effects and better spacing
- **Accessibility Features** with improved contrast and visibility
- **Professional Neumorphic Design** with modern styling

### 🛠️ **Technical Features**
- **Multi-Platform Support** with Windows-optimized GUI
- **Proxy Rotation** for enhanced scraping reliability
- **Export Capabilities** (JSON, CSV formats)
- **Configuration Management** with persistent settings
- **Diagnostic Tools** for troubleshooting and optimization

## 🚀 Quick Start

### Prerequisites
- Python 3.7+
- Windows 10/11 (optimized)
- Internet connection

### Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/bmd-email-scrapper.git
   cd bmd-email-scrapper
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application:**
   ```bash
   python bulk_gui.py
   ```

### Quick Launch Options

- **Enhanced GUI:** `BMD_EMAIL_SCRAPPER_ENHANCED.bat`
- **PowerShell:** `BMD_EMAIL_SCRAPPER_POWERSHELL.ps1`
- **Simple Launch:** `BMD_EMAIL_SCRAPPER_SIMPLE.bat`

## 📋 Usage

### Main Interface
1. **Accept Legal Terms** - Review and accept the usage agreement
2. **Configure Search** - Set query, location, and result limits
3. **Select Platforms** - Choose from Google, Bing, DuckDuckGo, Yahoo
4. **Start Scraping** - Monitor real-time progress
5. **Export Results** - Save data in JSON or CSV format

### Email Generation
1. **Navigate to Email Generation Tab**
2. **Configure Recipients** - Add names and details
3. **Customize Templates** - Modify email content
4. **Generate Emails** - Create personalized messages
5. **Export Generated Emails** - Save for use

## 🔧 Configuration

### Settings File (`config.json`)
```json
{
  "search_settings": {
    "default_max_results": 50,
    "default_location": "United States"
  },
  "ui_settings": {
    "theme": "neumorphic",
    "font_scaling": "enhanced"
  }
}
```

### Windows Security
Run `Add_Windows_Security_Exclusion.ps1` to add the application to Windows Defender exclusions for optimal performance.

## 🛡️ Security & Privacy

- **No Data Collection** - All operations are local
- **Secure Scraping** - Respects robots.txt and rate limits
- **Privacy First** - No personal data transmission
- **Windows Defender Compatible** - Includes security exclusion scripts

## 📁 Project Structure

```
bmd-email-scrapper/
├── bulk_gui.py              # Main GUI application
├── bulk_scraper.py          # Core scraping engine
├── email_generator.py       # Email generation module
├── gui_diagnostic.py        # Diagnostic and testing tool
├── config.py               # Configuration management
├── proxy_manager.py        # Proxy rotation system
├── utils.py                # Utility functions
├── requirements.txt        # Python dependencies
├── config.json            # Application settings
└── launch_scripts/        # Windows launch utilities
    ├── BMD_EMAIL_SCRAPPER_ENHANCED.bat
    ├── BMD_EMAIL_SCRAPPER_POWERSHELL.ps1
    └── Add_Windows_Security_Exclusion.ps1
```

## 🧪 Testing

### Run Diagnostic Tool
```bash
python gui_diagnostic.py
```

### Test Core Features
```bash
python test_bulk_features.py
python test_scraper.py
```

### Demo Enhanced Features
```bash
python enhanced_features_demo.py
```

## 🎨 UI Enhancements

### Font Sizes
- **Titles:** 28pt bold
- **Headers:** 18pt bold  
- **Buttons:** 14pt bold
- **Input Fields:** 13pt
- **Body Text:** 12pt

### Window Specifications
- **Main Window:** 1600x1000 pixels
- **Consent Screen:** 1000x800 pixels
- **Diagnostic Tool:** 900x700 pixels

### Interactive Elements
- **Enhanced Hover Effects** with color transitions
- **Larger Button Padding** (30x15 pixels)
- **Improved Spacing** throughout interface
- **Better Focus Indicators** for accessibility

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool is for educational and legitimate business purposes only. Users are responsible for complying with all applicable laws and website terms of service. Always respect robots.txt files and implement appropriate rate limiting.

## 🆘 Support

- **Issues:** Report bugs via GitHub Issues
- **Documentation:** Check the wiki for detailed guides
- **Diagnostic:** Use `gui_diagnostic.py` for troubleshooting

## 🔄 Recent Updates

### v2.0 - Enhanced UI Release
- ✅ Significantly increased font sizes for better visibility
- ✅ Enhanced window sizing and positioning
- ✅ Improved button interactivity and hover effects
- ✅ Better spacing and padding throughout interface
- ✅ Enhanced accessibility features
- ✅ Professional neumorphic design implementation

---

**Made with ❤️ for efficient email operations**