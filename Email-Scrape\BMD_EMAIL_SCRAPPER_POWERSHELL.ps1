# BMD EMAIL SCRAPPER - PowerShell Enhanced Launcher
# This script provides multiple launch methods with Windows Security compatibility

Write-Host "`n========================================" -ForegroundColor Green
Write-Host " BMD EMAIL SCRAPPER - PowerShell Launcher" -ForegroundColor Green
Write-Host "========================================`n" -ForegroundColor Green

# Set execution policy for current session
if ($PSVersionTable.PSVersion.Major -ge 3) {
    try {
        Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
        Write-Host "✓ Execution policy set" -ForegroundColor Green
    } catch {
        Write-Host "⚠ Warning: Could not set execution policy" -ForegroundColor Yellow
    }
}

# Change to script directory
Set-Location $PSScriptRoot
Write-Host "✓ Working directory: $PWD" -ForegroundColor Green

# Check Python availability
$pythonFound = $false
try {
    $pythonVersion = & python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
        $pythonFound = $true
    }
} catch {
    # Silently continue to error handling below
}

if (-not $pythonFound) {
    Write-Host "✗ ERROR: Python not found in PATH" -ForegroundColor Red
    Write-Host "Please install Python and add it to your PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Function to launch GUI with enhanced visibility
function Start-EnhancedGUI {
    Write-Host "`nAttempting enhanced GUI launch..." -ForegroundColor Cyan
    
    $pythonCode = @'
import sys
sys.path.insert(0, '.')
import tkinter as tk
from tkinter import messagebox
import threading
import time

def show_loading():
    loading = tk.Tk()
    loading.title("BMD EMAIL SCRAPPER")
    loading.geometry("400x200")
    loading.configure(bg="#000000")
    loading.resizable(False, False)
    
    # Center the loading window
    loading.eval('tk::PlaceWindow . center')
    
    label = tk.Label(loading, text="🚀 BMD EMAIL SCRAPPER\n\nLoading application...\nPlease wait...", 
                    bg="#000000", fg="#00FF00", font=("Arial", 12, "bold"))
    label.pack(expand=True)
    
    loading.lift()
    loading.attributes('-topmost', True)
    loading.focus_force()
    
    def close_loading():
        time.sleep(3)
        loading.destroy()
    
    threading.Thread(target=close_loading, daemon=True).start()
    loading.mainloop()

def start_main_app():
    try:
        import bulk_gui
        root = tk.Tk()
        root.withdraw()  # Hide initially
        
        # Load and show main app
        app = bulk_gui.BulkScraperGUI(root)
        
        # Ensure window is visible
        root.deiconify()
        root.state('zoomed')
        root.lift()
        root.focus_force()
        root.attributes('-topmost', True)
        root.after(2000, lambda: root.attributes('-topmost', False))
        
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")
        return False
    return True

# Show loading screen first
show_loading()

# Start main application
if not start_main_app():
    print("GUI launch failed")
    sys.exit(1)
'@
    
    try {
        python -c $pythonCode
        return $LASTEXITCODE -eq 0
    } catch {
        Write-Host "Enhanced GUI launch failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to launch standard GUI
function Start-StandardGUI {
    Write-Host "`nAttempting standard GUI launch..." -ForegroundColor Cyan
    try {
        python bulk_gui.py
        return $LASTEXITCODE -eq 0
    } catch {
        Write-Host "Standard GUI launch failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to launch CLI version
function Start-CLI {
    Write-Host "`nAttempting CLI launch..." -ForegroundColor Cyan
    try {
        python bulk_cli.py
        return $LASTEXITCODE -eq 0
    } catch {
        Write-Host "CLI launch failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main launch sequence
Write-Host "Starting BMD EMAIL SCRAPPER...`n" -ForegroundColor Yellow

# Try enhanced GUI first
$launched = $false
if (Start-EnhancedGUI) {
    Write-Host "`n✓ Application launched successfully!" -ForegroundColor Green
    $launched = $true
}

if (-not $launched -and (Start-StandardGUI)) {
    Write-Host "`n✓ Application launched with standard method!" -ForegroundColor Green
    $launched = $true
}

if (-not $launched -and (Start-CLI)) {
    Write-Host "`n✓ Application launched in CLI mode!" -ForegroundColor Green
    $launched = $true
}

if (-not $launched) {
    Write-Host "`n========================================" -ForegroundColor Red
    Write-Host " ERROR: All launch methods failed!" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    Write-Host "`nPossible solutions:" -ForegroundColor Yellow
    Write-Host "1. Run as Administrator" -ForegroundColor White
    Write-Host "2. Add Windows Security exclusions" -ForegroundColor White
    Write-Host "3. Temporarily disable antivirus" -ForegroundColor White
    Write-Host "4. Check Python dependencies" -ForegroundColor White
    
    if (Test-Path "error.log") {
        Write-Host "`nError log contents:" -ForegroundColor Yellow
        Get-Content "error.log" | Write-Host -ForegroundColor Red
    }
}

Write-Host "`nPress Enter to exit..." -ForegroundColor Gray
Read-Host