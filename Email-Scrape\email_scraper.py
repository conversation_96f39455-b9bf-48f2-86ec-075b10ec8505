#!/usr/bin/env python3
"""
Email Scraper Pro - Advanced Lead Generation Tool
Author: AI Assistant
Description: Comprehensive email scraping system for lead generation from Google, LinkedIn, and web sources
"""

import asyncio
import json
import csv
import sqlite3
import logging
import os
import sys
from datetime import datetime
from typing import List, Dict, Set, Optional
from dataclasses import dataclass
from pathlib import Path

# Third-party imports
import pandas as pd
from playwright.async_api import async_playwright, Brows<PERSON>, Page
from bs4 import BeautifulSoup
import requests
from fake_useragent import UserAgent
from email_validator import validate_email, EmailNotValidError
from loguru import logger
from tqdm import tqdm
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
import phonenumbers
from config import get_config, ProxyConfig

# Configure logging
logger.add("email_scraper.log", rotation="10 MB", level="INFO")

@dataclass
class Contact:
    """Data class for storing contact information"""
    email: str
    name: str = ""
    company: str = ""
    title: str = ""
    linkedin_url: str = ""
    phone: str = ""
    website: str = ""
    source: str = ""
    extracted_date: str = ""

class EmailExtractor:
    """Core email extraction functionality"""
    
    def __init__(self):
        self.email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        self.phone_pattern = r'\b(?:\+?1[-.]?)?\(?([0-9]{3})\)?[-.]?([0-9]{3})[-.]?([0-9]{4})\b'
        self.linkedin_pattern = r'https?://(?:www\.)?linkedin\.com/in/[\w-]+/?'
        
    def extract_emails_from_text(self, text: str) -> Set[str]:
        """Extract email addresses from text"""
        import re
        emails = set(re.findall(self.email_pattern, text, re.IGNORECASE))
        validated_emails = set()
        
        for email in emails:
            try:
                valid = validate_email(email)
                validated_emails.add(valid.email)
            except EmailNotValidError:
                continue
                
        return validated_emails
    
    def extract_phones_from_text(self, text: str) -> Set[str]:
        """Extract phone numbers from text"""
        import re
        phones = set(re.findall(self.phone_pattern, text))
        formatted_phones = set()
        
        for phone in phones:
            if isinstance(phone, tuple):
                formatted_phone = f"({phone[0]}) {phone[1]}-{phone[2]}"
                formatted_phones.add(formatted_phone)
            else:
                formatted_phones.add(phone)
                
        return formatted_phones
    
    def extract_linkedin_urls(self, text: str) -> Set[str]:
        """Extract LinkedIn profile URLs from text"""
        import re
        return set(re.findall(self.linkedin_pattern, text, re.IGNORECASE))

class WebScraper:
    """Web scraping functionality using Playwright"""
    
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.user_agent = UserAgent()
        self.extractor = EmailExtractor()
        self.config = get_config()
        self.current_proxy: Optional[ProxyConfig] = None
        self.failed_proxies: Set[str] = set()
        self.proxy_rotation_count = 0
        
    async def init_browser(self, headless: bool = True, proxy: Optional[ProxyConfig] = None):
        """Initialize Playwright browser with optional proxy"""
        self.playwright = await async_playwright().start()
        
        launch_args = ['--no-sandbox', '--disable-dev-shm-usage']
        
        # Add proxy args if provided
        if proxy:
            proxy_url = f"{proxy.protocol}://{proxy.host}:{proxy.port}"
            if proxy.username and proxy.password:
                proxy_url = f"{proxy.protocol}://{proxy.username}:{proxy.password}@{proxy.host}:{proxy.port}"
            launch_args.extend([
                f'--proxy-server={proxy_url}',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ])
            self.current_proxy = proxy
            logger.info(f"Initializing browser with proxy: {proxy}")
        
        self.browser = await self.playwright.chromium.launch(
            headless=headless,
            args=launch_args
        )
        
    async def close_browser(self):
        """Close browser and cleanup"""
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    def get_next_proxy(self) -> Optional[ProxyConfig]:
        """Get next available proxy for rotation"""
        available_proxies = [
            proxy for proxy in self.config.proxies 
            if str(proxy) not in self.failed_proxies
        ]
        
        if not available_proxies:
            logger.warning("No available proxies remaining")
            return None
        
        # Use round-robin selection
        proxy = available_proxies[self.proxy_rotation_count % len(available_proxies)]
        self.proxy_rotation_count += 1
        return proxy
    
    def mark_proxy_failed(self, proxy: ProxyConfig):
        """Mark a proxy as failed"""
        proxy_str = str(proxy)
        self.failed_proxies.add(proxy_str)
        logger.warning(f"Marked proxy as failed: {proxy_str}")
    
    async def rotate_proxy(self):
        """Rotate to next available proxy"""
        if self.current_proxy:
            self.mark_proxy_failed(self.current_proxy)
        
        next_proxy = self.get_next_proxy()
        if next_proxy:
            logger.info(f"Rotating to new proxy: {next_proxy}")
            await self.close_browser()
            await self.init_browser(proxy=next_proxy)
            return True
        else:
            logger.error("No more proxies available for rotation")
            return False
    
    async def create_page(self, use_proxy: bool = True) -> Page:
        """Create a new page with stealth settings and proxy rotation"""
        if not self.browser:
            # Initialize browser with proxy if available and requested
            proxy = None
            if use_proxy and self.config.proxies:
                proxy = self.get_next_proxy()
            await self.init_browser(proxy=proxy)
            
        # Get random user agent for rotation
        user_agent = self.config.get_random_user_agent()
        
        # Create context with stealth settings and proxy
        context_options = {
            'user_agent': user_agent,
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            'extra_http_headers': {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        }
        
        # Add proxy to context if using Playwright proxy (alternative to browser-level proxy)
        if use_proxy and self.current_proxy and not self.current_proxy.username:
            context_options['proxy'] = self.current_proxy.to_playwright_format()
        
        context = await self.browser.new_context(**context_options)
        page = await context.new_page()
        
        # Add comprehensive stealth settings
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32',
            });
            window.chrome = {
                runtime: {},
            };
            // Remove automation indicators
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        """)
        
        return page
    
    async def navigate_with_retry(self, page: Page, url: str, max_retries: int = 3) -> bool:
        """Navigate to URL with proxy rotation on failure"""
        for attempt in range(max_retries):
            try:
                logger.info(f"Navigating to {url} (attempt {attempt + 1}/{max_retries})")
                
                # Add random delay to avoid rate limiting
                await asyncio.sleep(self.config.get_delay())
                
                response = await page.goto(url, wait_until='networkidle', timeout=30000)
                
                # Check if request was successful
                if response and response.status < 400:
                    logger.success(f"Successfully loaded {url}")
                    return True
                elif response and response.status in [403, 429, 503]:
                    logger.warning(f"Blocked response {response.status} for {url}")
                    # Check if we should rotate proxy
                    if self.current_proxy and attempt < max_retries - 1:
                        logger.info("Attempting proxy rotation due to blocking")
                        if await self.rotate_proxy():
                            # Create new page with new proxy
                            await page.close()
                            page = await self.create_page()
                            continue
                else:
                    logger.warning(f"Failed to load {url} with status {response.status if response else 'None'}")
                    
            except Exception as e:
                logger.warning(f"Navigation attempt {attempt + 1} failed for {url}: {e}")
                
                # Check for specific blocking indicators
                if any(indicator in str(e).lower() for indicator in ['timeout', 'blocked', 'captcha', 'unusual traffic']):
                    if self.current_proxy and attempt < max_retries - 1:
                        logger.info("Attempting proxy rotation due to blocking indicators")
                        if await self.rotate_proxy():
                            # Create new page with new proxy
                            await page.close()
                            page = await self.create_page()
                            continue
                
                # Wait before retry
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2
                    logger.info(f"Waiting {wait_time} seconds before retry...")
                    await asyncio.sleep(wait_time)
        
        logger.error(f"Failed to navigate to {url} after {max_retries} attempts")
        return False
    
    async def google_search_scrape(self, query: str, max_pages: int = 3) -> List[Contact]:
        """Scrape Google search results for contact information with proxy rotation"""
        contacts = []
        page = await self.create_page(use_proxy=True)
        
        try:
            for page_num in range(max_pages):
                search_url = f"https://www.google.com/search?q={query}&start={page_num * 10}"
                logger.info(f"Scraping Google search page {page_num + 1}/{max_pages}")
                
                # Use navigate_with_retry for proxy rotation
                if not await self.navigate_with_retry(page, search_url):
                    logger.warning(f"Failed to load Google search page {page_num + 1} after retries")
                    continue
                
                # Check for CAPTCHA or blocking
                page_content = await page.content()
                if "captcha" in page_content.lower() or "unusual traffic" in page_content.lower():
                    logger.warning("Google CAPTCHA detected - switching to alternative search engines")
                    await page.close()
                    # Use alternative search engines
                    alternative_contacts = await self.alternative_search_scrape(query, max_pages)
                    return alternative_contacts
                
                # Wait for search results with multiple fallback selectors
                search_found = False
                selectors_to_try = ['#search', '.g', '[data-ved]', '#main', '#center_col']
                
                for selector in selectors_to_try:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        logger.info(f"Found search results using selector: {selector}")
                        search_found = True
                        break
                    except:
                        continue
                
                if not search_found:
                    logger.warning(f"Could not find search results on page {page_num + 1}")
                    # Save page content for debugging
                    with open(f"debug_page_{page_num}.html", "w", encoding="utf-8") as f:
                        f.write(await page.content())
                    continue
                
                # Extract search result links with improved selectors
                links = await page.evaluate("""
                    () => {
                        const results = [];
                        // Try multiple selectors for search result links
                        const selectors = [
                            'h3 a[href]',
                            '.g a[href]',
                            '[data-ved] a[href]',
                            'a[href^="http"]'
                        ];
                        
                        for (const selector of selectors) {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                if (el.href && 
                                    !el.href.includes('google.com') && 
                                    !el.href.includes('youtube.com') &&
                                    !el.href.includes('maps.google') &&
                                    !results.includes(el.href)) {
                                    results.push(el.href);
                                }
                            });
                            if (results.length > 0) break;
                        }
                        return results;
                    }
                """)
                
                logger.info(f"Found {len(links)} links on page {page_num + 1}")
                
                # Visit each link and extract emails with proxy rotation
                for i, link in enumerate(links[:8]):  # Limit to 8 links per page
                    logger.info(f"Scraping link {i+1}/{min(len(links), 8)}: {link[:100]}...")
                    
                    # Use navigate_with_retry for each link
                    if await self.navigate_with_retry(page, link, max_retries=2):
                        try:
                            content = await page.content()
                            
                            emails = self.extractor.extract_emails_from_text(content)
                            phones = self.extractor.extract_phones_from_text(content)
                            
                            for email in emails:
                                contact = Contact(
                                    email=email,
                                    website=link,
                                    source=f"Google Search (Proxy: {self.current_proxy or 'Direct'})",
                                    extracted_date=datetime.now().isoformat()
                                )
                                contacts.append(contact)
                                logger.success(f"Found email: {email}")
                                
                        except Exception as e:
                            logger.warning(f"Failed to extract content from {link}: {e}")
                    else:
                        logger.warning(f"Failed to load link after retries: {link}")
                        
                await asyncio.sleep(2)  # Rate limiting
                
        except Exception as e:
            logger.error(f"Google search scraping failed: {e}")
        finally:
            await page.close()
            
        return contacts
    
    async def alternative_search_scrape(self, query: str, max_pages: int = 2) -> List[Contact]:
        """Use alternative search engines when Google is blocked"""
        contacts = []
        page = await self.create_page()
        
        try:
            # Try Bing first
            logger.info("Trying Bing search as alternative...")
            bing_contacts = await self._bing_search(page, query, max_pages)
            contacts.extend(bing_contacts)
            
            if len(contacts) < 5:  # If not enough results, try DuckDuckGo
                logger.info("Trying DuckDuckGo search as additional source...")
                ddg_contacts = await self._duckduckgo_search(page, query, 1)
                contacts.extend(ddg_contacts)
                
        except Exception as e:
            logger.error(f"Alternative search failed: {e}")
        finally:
            await page.close()
            
        return contacts
    
    async def _bing_search(self, page: Page, query: str, max_pages: int) -> List[Contact]:
        """Search using Bing"""
        contacts = []
        
        try:
            for page_num in range(max_pages):
                search_url = f"https://www.bing.com/search?q={query}&first={page_num * 10}"
                await page.goto(search_url, timeout=30000)
                await page.wait_for_selector('#b_results', timeout=10000)
                
                links = await page.evaluate("""
                    () => {
                        const results = [];
                        const elements = document.querySelectorAll('#b_results h2 a');
                        elements.forEach(el => {
                            if (el.href && !el.href.includes('bing.com') && !el.href.includes('microsoft.com')) {
                                results.push(el.href);
                            }
                        });
                        return results;
                    }
                """)
                
                logger.info(f"Found {len(links)} Bing results on page {page_num + 1}")
                
                for link in links[:5]:
                    try:
                        await page.goto(link, timeout=15000)
                        content = await page.content()
                        emails = self.extractor.extract_emails_from_text(content)
                        
                        for email in emails:
                            contact = Contact(
                                email=email,
                                website=link,
                                source="Bing Search",
                                extracted_date=datetime.now().isoformat()
                            )
                            contacts.append(contact)
                    except Exception as e:
                        logger.warning(f"Failed to scrape {link}: {e}")
                        continue
                
                await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"Bing search failed: {e}")
            
        return contacts
    
    async def _duckduckgo_search(self, page: Page, query: str, max_pages: int) -> List[Contact]:
        """Search using DuckDuckGo"""
        contacts = []
        
        try:
            search_url = f"https://duckduckgo.com/?q={query}"
            await page.goto(search_url, timeout=30000)
            await page.wait_for_selector('[data-testid="result"]', timeout=10000)
            
            links = await page.evaluate("""
                () => {
                    const results = [];
                    const elements = document.querySelectorAll('[data-testid="result"] h2 a, [data-testid="result"] .result__a');
                    elements.forEach(el => {
                        if (el.href && !el.href.includes('duckduckgo.com')) {
                            results.push(el.href);
                        }
                    });
                    return results;
                }
            """)
            
            logger.info(f"Found {len(links)} DuckDuckGo results")
            
            for link in links[:6]:
                try:
                    await page.goto(link, timeout=15000)
                    content = await page.content()
                    emails = self.extractor.extract_emails_from_text(content)
                    
                    for email in emails:
                        contact = Contact(
                            email=email,
                            website=link,
                            source="DuckDuckGo Search",
                            extracted_date=datetime.now().isoformat()
                        )
                        contacts.append(contact)
                except Exception as e:
                    logger.warning(f"Failed to scrape {link}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {e}")
            
        return contacts

    async def linkedin_profile_scrape(self, profile_urls: List[str]) -> List[Contact]:
        """Scrape LinkedIn profiles for contact information"""
        contacts = []
        page = await self.create_page()
        
        try:
            for url in profile_urls:
                try:
                    await page.goto(url, timeout=15000)
                    
                    # Wait for profile to load
                    await page.wait_for_selector('h1', timeout=10000)
                    
                    # Extract profile information
                    profile_data = await page.evaluate("""
                        () => {
                            const name = document.querySelector('h1')?.textContent?.trim() || '';
                            const title = document.querySelector('.text-body-medium')?.textContent?.trim() || '';
                            const company = document.querySelector('.inline-show-more-text')?.textContent?.trim() || '';
                            return { name, title, company };
                        }
                    """)
                    
                    content = await page.content()
                    emails = self.extractor.extract_emails_from_text(content)
                    
                    for email in emails:
                        contact = Contact(
                            email=email,
                            name=profile_data.get('name', ''),
                            title=profile_data.get('title', ''),
                            company=profile_data.get('company', ''),
                            linkedin_url=url,
                            source="LinkedIn",
                            extracted_date=datetime.now().isoformat()
                        )
                        contacts.append(contact)
                        
                    await asyncio.sleep(3)  # Rate limiting for LinkedIn
                    
                except Exception as e:
                    logger.warning(f"Failed to scrape LinkedIn profile {url}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"LinkedIn scraping failed: {e}")
        finally:
            await page.close()
            
        return contacts
    
    async def website_scrape(self, urls: List[str]) -> List[Contact]:
        """Scrape websites for contact information"""
        contacts = []
        page = await self.create_page()
        
        try:
            for url in urls:
                try:
                    await page.goto(url, timeout=15000)
                    content = await page.content()
                    
                    # Parse with BeautifulSoup for better text extraction
                    soup = BeautifulSoup(content, 'html.parser')
                    text_content = soup.get_text()
                    
                    emails = self.extractor.extract_emails_from_text(text_content)
                    phones = self.extractor.extract_phones_from_text(text_content)
                    
                    # Try to extract company name from title or h1
                    company_name = ""
                    title_tag = soup.find('title')
                    if title_tag:
                        company_name = title_tag.get_text().strip()
                    
                    for email in emails:
                        contact = Contact(
                            email=email,
                            company=company_name,
                            website=url,
                            source="Website Scrape",
                            extracted_date=datetime.now().isoformat()
                        )
                        contacts.append(contact)
                        
                    await asyncio.sleep(1)  # Rate limiting
                    
                except Exception as e:
                    logger.warning(f"Failed to scrape website {url}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Website scraping failed: {e}")
        finally:
            await page.close()
            
        return contacts

class DataManager:
    """Data storage and export functionality"""
    
    def __init__(self, db_path: str = "contacts.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS contacts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                name TEXT,
                company TEXT,
                title TEXT,
                linkedin_url TEXT,
                phone TEXT,
                website TEXT,
                source TEXT,
                extracted_date TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
    
    def save_contacts(self, contacts: List[Contact]) -> int:
        """Save contacts to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        saved_count = 0
        for contact in contacts:
            try:
                cursor.execute("""
                    INSERT OR IGNORE INTO contacts 
                    (email, name, company, title, linkedin_url, phone, website, source, extracted_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    contact.email, contact.name, contact.company, contact.title,
                    contact.linkedin_url, contact.phone, contact.website,
                    contact.source, contact.extracted_date
                ))
                if cursor.rowcount > 0:
                    saved_count += 1
            except sqlite3.Error as e:
                logger.error(f"Database error saving contact {contact.email}: {e}")
        
        conn.commit()
        conn.close()
        return saved_count
    
    def get_all_contacts(self) -> List[Contact]:
        """Retrieve all contacts from database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM contacts ORDER BY created_at DESC")
        rows = cursor.fetchall()
        
        contacts = []
        for row in rows:
            contact = Contact(
                email=row[1], name=row[2], company=row[3], title=row[4],
                linkedin_url=row[5], phone=row[6], website=row[7],
                source=row[8], extracted_date=row[9]
            )
            contacts.append(contact)
        
        conn.close()
        return contacts
    
    def export_to_csv(self, filename: str) -> bool:
        """Export contacts to CSV file"""
        try:
            contacts = self.get_all_contacts()
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['email', 'name', 'company', 'title', 'linkedin_url', 
                             'phone', 'website', 'source', 'extracted_date']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for contact in contacts:
                    writer.writerow({
                        'email': contact.email,
                        'name': contact.name,
                        'company': contact.company,
                        'title': contact.title,
                        'linkedin_url': contact.linkedin_url,
                        'phone': contact.phone,
                        'website': contact.website,
                        'source': contact.source,
                        'extracted_date': contact.extracted_date
                    })
            
            return True
        except Exception as e:
            logger.error(f"CSV export failed: {e}")
            return False
    
    def export_to_excel(self, filename: str) -> bool:
        """Export contacts to Excel file"""
        try:
            contacts = self.get_all_contacts()
            
            data = []
            for contact in contacts:
                data.append({
                    'Email': contact.email,
                    'Name': contact.name,
                    'Company': contact.company,
                    'Title': contact.title,
                    'LinkedIn URL': contact.linkedin_url,
                    'Phone': contact.phone,
                    'Website': contact.website,
                    'Source': contact.source,
                    'Extracted Date': contact.extracted_date
                })
            
            df = pd.DataFrame(data)
            df.to_excel(filename, index=False)
            
            return True
        except Exception as e:
            logger.error(f"Excel export failed: {e}")
            return False

if __name__ == "__main__":
    print("Email Scraper Pro - Advanced Lead Generation Tool")
    print("Use the GUI application by running: python gui_app.py")
    print("Or import this module to use programmatically")