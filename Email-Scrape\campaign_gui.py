#!/usr/bin/env python3
"""
Email Campaign Management GUI
Modern GUI application for managing mass email campaigns
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import asyncio
import threading
import json
import csv
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from loguru import logger
from smtp_config import SMTPManager
from email_templates import TemplateManager
from mass_email_sender import MassEmailSender, CampaignStatus, EmailStatus
from config_loader import get_mass_email_config

# Use the same color scheme as bulk_gui.py for consistency
COLORS = {
    'bg_primary': '#000000',       # Pure black background
    'bg_secondary': '#111111',     # Slightly lighter black
    'bg_card': '#000000',          # Black cards
    'accent_primary': '#667eea',   # Modern blue
    'accent_secondary': '#764ba2', # Purple accent
    'accent_success': '#48bb78',   # Green
    'accent_warning': '#ed8936',   # Orange
    'accent_danger': '#f56565',    # Red
    'text_primary': '#ffffff',     # White text
    'text_secondary': '#cccccc',   # Light gray text
    'text_light': '#999999',       # Medium gray text
    'shadow_light': '#333333',     # Dark shadow
    'shadow_dark': '#000000',      # Pure black shadow
    'border': '#333333',           # Dark border
    'hover': '#222222'             # Dark hover state
}

NEUMORPHIC_STYLE = {
    'relief': 'flat',
    'borderwidth': 0,
    'highlightthickness': 0,
    'font': ('Segoe UI', 14, 'bold'),
    'cursor': 'hand2'
}

FONT_SIZES = {
    'title': ('Segoe UI', 28, 'bold'),
    'subtitle': ('Segoe UI', 16, 'normal'),
    'header': ('Segoe UI', 18, 'bold'),
    'subheader': ('Segoe UI', 14, 'bold'),
    'body': ('Segoe UI', 12, 'normal'),
    'button': ('Segoe UI', 14, 'bold'),
    'input': ('Segoe UI', 13, 'normal'),
    'small': ('Segoe UI', 11, 'normal')
}

class CampaignGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Email Campaign Manager")
        self.root.geometry("1400x900")
        self.root.configure(bg=COLORS['bg_primary'])
        
        # Initialize managers
        self.smtp_manager = SMTPManager()
        self.template_manager = TemplateManager()
        self.email_sender = MassEmailSender()
        self.config = get_mass_email_config()
        
        # Campaign data
        self.campaigns = {}
        self.current_campaign = None
        self.running_campaigns = set()
        
        # Setup GUI
        self.setup_styles()
        self.create_widgets()
        self.load_campaigns()
        self.refresh_smtp_configs()
        self.refresh_templates()
        
        # Start status update loop
        self.update_campaign_status()
    
    def setup_styles(self):
        """Setup custom styles for the application"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles with dark theme
        style.configure('Dark.TNotebook', background=COLORS['bg_primary'], borderwidth=0)
        style.configure('Dark.TNotebook.Tab', 
                       background=COLORS['bg_secondary'], 
                       foreground=COLORS['text_primary'],
                       padding=[20, 10],
                       font=FONT_SIZES['subheader'])
        style.map('Dark.TNotebook.Tab',
                 background=[('selected', COLORS['accent_primary']),
                           ('active', COLORS['hover'])])
        
        style.configure('Dark.TFrame', background=COLORS['bg_primary'])
        style.configure('Dark.TLabel', background=COLORS['bg_primary'], foreground=COLORS['text_primary'])
        style.configure('Dark.TEntry', fieldbackground=COLORS['bg_secondary'], foreground=COLORS['text_primary'])
        style.configure('Dark.TCombobox', fieldbackground=COLORS['bg_secondary'], foreground=COLORS['text_primary'])
        style.configure('Dark.TText', fieldbackground=COLORS['bg_secondary'], foreground=COLORS['text_primary'])
    
    def create_widgets(self):
        """Create the main GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, style='Dark.TFrame')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(main_frame, text="📧 Email Campaign Manager", 
                              font=FONT_SIZES['title'], 
                              bg=COLORS['bg_primary'], 
                              fg=COLORS['text_primary'])
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame, style='Dark.TNotebook')
        self.notebook.pack(fill='both', expand=True)
        
        # Create tabs
        self.create_campaigns_tab()
        self.create_smtp_tab()
        self.create_templates_tab()
        self.create_analytics_tab()
    
    def create_neumorphic_button(self, parent, text, command, color):
        """Create a neumorphic style button"""
        button = tk.Button(parent, text=text, command=command,
                          bg=color, fg=COLORS['text_primary'],
                          activebackground=color, activeforeground=COLORS['text_primary'],
                          **NEUMORPHIC_STYLE)
        
        def on_enter(e):
            button.configure(bg=self.lighten_color(color, 0.1))
        
        def on_leave(e):
            button.configure(bg=color)
        
        button.bind('<Enter>', on_enter)
        button.bind('<Leave>', on_leave)
        
        return button
    
    def lighten_color(self, color, factor):
        """Lighten a hex color by a factor"""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        rgb = tuple(min(255, int(c + (255 - c) * factor)) for c in rgb)
        return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"
    
    def create_campaigns_tab(self):
        """Create the campaigns management tab"""
        campaigns_frame = ttk.Frame(self.notebook, style='Dark.TFrame')
        self.notebook.add(campaigns_frame, text='📋 Campaigns')
        
        # Left panel - Campaign list
        left_panel = ttk.Frame(campaigns_frame, style='Dark.TFrame')
        left_panel.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        # Campaign list header
        list_header = tk.Label(left_panel, text="Active Campaigns", 
                              font=FONT_SIZES['header'], 
                              bg=COLORS['bg_primary'], 
                              fg=COLORS['text_primary'])
        list_header.pack(pady=(0, 10))
        
        # Campaign list
        list_frame = tk.Frame(left_panel, bg=COLORS['bg_secondary'])
        list_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Scrollable campaign list
        self.campaign_listbox = tk.Listbox(list_frame, 
                                          bg=COLORS['bg_secondary'], 
                                          fg=COLORS['text_primary'],
                                          selectbackground=COLORS['accent_primary'],
                                          font=FONT_SIZES['body'],
                                          borderwidth=0,
                                          highlightthickness=0)
        self.campaign_listbox.pack(fill='both', expand=True, padx=5, pady=5)
        self.campaign_listbox.bind('<<ListboxSelect>>', self.on_campaign_select)
        
        # Campaign control buttons
        button_frame = tk.Frame(left_panel, bg=COLORS['bg_primary'])
        button_frame.pack(fill='x', pady=(0, 10))
        
        self.create_neumorphic_button(button_frame, "➕ New Campaign", 
                                     self.create_new_campaign, 
                                     COLORS['accent_success']).pack(side='left', padx=(0, 5))
        
        self.create_neumorphic_button(button_frame, "▶️ Start", 
                                     self.start_campaign, 
                                     COLORS['accent_primary']).pack(side='left', padx=5)
        
        self.create_neumorphic_button(button_frame, "⏸️ Pause", 
                                     self.pause_campaign, 
                                     COLORS['accent_warning']).pack(side='left', padx=5)
        
        self.create_neumorphic_button(button_frame, "🗑️ Delete", 
                                     self.delete_campaign, 
                                     COLORS['accent_danger']).pack(side='left', padx=5)
        
        # Right panel - Campaign details
        right_panel = ttk.Frame(campaigns_frame, style='Dark.TFrame')
        right_panel.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # Campaign details header
        details_header = tk.Label(right_panel, text="Campaign Details", 
                                 font=FONT_SIZES['header'], 
                                 bg=COLORS['bg_primary'], 
                                 fg=COLORS['text_primary'])
        details_header.pack(pady=(0, 10))
        
        # Campaign details form
        self.create_campaign_form(right_panel)
    
    def create_campaign_form(self, parent):
        """Create the campaign creation/editing form"""
        form_frame = tk.Frame(parent, bg=COLORS['bg_secondary'])
        form_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Campaign name
        tk.Label(form_frame, text="Campaign Name:", 
                font=FONT_SIZES['subheader'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(10, 5))
        
        self.campaign_name_var = tk.StringVar()
        self.campaign_name_entry = tk.Entry(form_frame, 
                                           textvariable=self.campaign_name_var,
                                           font=FONT_SIZES['input'],
                                           bg=COLORS['bg_primary'],
                                           fg=COLORS['text_primary'],
                                           insertbackground=COLORS['text_primary'])
        self.campaign_name_entry.pack(fill='x', padx=10, pady=(0, 10))
        
        # SMTP Configuration
        tk.Label(form_frame, text="SMTP Configuration:", 
                font=FONT_SIZES['subheader'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(0, 5))
        
        self.smtp_config_var = tk.StringVar()
        self.smtp_config_combo = ttk.Combobox(form_frame, 
                                             textvariable=self.smtp_config_var,
                                             font=FONT_SIZES['input'],
                                             state='readonly')
        self.smtp_config_combo.pack(fill='x', padx=10, pady=(0, 10))
        
        # Email Template
        tk.Label(form_frame, text="Email Template:", 
                font=FONT_SIZES['subheader'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(0, 5))
        
        self.template_var = tk.StringVar()
        self.template_combo = ttk.Combobox(form_frame, 
                                          textvariable=self.template_var,
                                          font=FONT_SIZES['input'],
                                          state='readonly')
        self.template_combo.pack(fill='x', padx=10, pady=(0, 10))
        
        # Recipients file
        tk.Label(form_frame, text="Recipients File (CSV):", 
                font=FONT_SIZES['subheader'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(0, 5))
        
        recipients_frame = tk.Frame(form_frame, bg=COLORS['bg_secondary'])
        recipients_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        self.recipients_file_var = tk.StringVar()
        self.recipients_entry = tk.Entry(recipients_frame, 
                                        textvariable=self.recipients_file_var,
                                        font=FONT_SIZES['input'],
                                        bg=COLORS['bg_primary'],
                                        fg=COLORS['text_primary'],
                                        insertbackground=COLORS['text_primary'])
        self.recipients_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))
        
        self.create_neumorphic_button(recipients_frame, "Browse", 
                                     self.browse_recipients_file, 
                                     COLORS['accent_secondary']).pack(side='right')
        
        # Campaign settings
        settings_frame = tk.LabelFrame(form_frame, text="Campaign Settings", 
                                      font=FONT_SIZES['subheader'],
                                      bg=COLORS['bg_secondary'], 
                                      fg=COLORS['text_primary'])
        settings_frame.pack(fill='x', padx=10, pady=10)
        
        # Send rate
        tk.Label(settings_frame, text="Emails per minute:", 
                font=FONT_SIZES['body'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(5, 0))
        
        self.send_rate_var = tk.IntVar(value=self.config.rate_limiting.emails_per_minute)
        self.send_rate_scale = tk.Scale(settings_frame, 
                                       from_=1, to=100, 
                                       orient='horizontal',
                                       variable=self.send_rate_var,
                                       bg=COLORS['bg_secondary'],
                                       fg=COLORS['text_primary'],
                                       highlightthickness=0,
                                       troughcolor=COLORS['bg_primary'])
        self.send_rate_scale.pack(fill='x', padx=10, pady=(0, 5))
        
        # Schedule sending
        self.schedule_var = tk.BooleanVar()
        schedule_check = tk.Checkbutton(settings_frame, 
                                       text="Schedule for later",
                                       variable=self.schedule_var,
                                       font=FONT_SIZES['body'],
                                       bg=COLORS['bg_secondary'],
                                       fg=COLORS['text_primary'],
                                       selectcolor=COLORS['bg_primary'],
                                       activebackground=COLORS['bg_secondary'],
                                       activeforeground=COLORS['text_primary'])
        schedule_check.pack(anchor='w', padx=10, pady=5)
        
        # Save campaign button
        self.create_neumorphic_button(form_frame, "💾 Save Campaign", 
                                     self.save_campaign, 
                                     COLORS['accent_success']).pack(pady=20)
    
    def create_smtp_tab(self):
        """Create the SMTP configuration tab"""
        smtp_frame = ttk.Frame(self.notebook, style='Dark.TFrame')
        self.notebook.add(smtp_frame, text='📧 SMTP Config')
        
        # SMTP configurations list
        list_frame = tk.Frame(smtp_frame, bg=COLORS['bg_secondary'])
        list_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        tk.Label(list_frame, text="SMTP Configurations", 
                font=FONT_SIZES['header'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(pady=(10, 20))
        
        # SMTP list
        self.smtp_listbox = tk.Listbox(list_frame, 
                                      bg=COLORS['bg_primary'], 
                                      fg=COLORS['text_primary'],
                                      selectbackground=COLORS['accent_primary'],
                                      font=FONT_SIZES['body'],
                                      height=10)
        self.smtp_listbox.pack(fill='x', padx=10, pady=(0, 10))
        
        # SMTP buttons
        smtp_button_frame = tk.Frame(list_frame, bg=COLORS['bg_secondary'])
        smtp_button_frame.pack(fill='x', padx=10, pady=10)
        
        self.create_neumorphic_button(smtp_button_frame, "➕ Add SMTP", 
                                     self.add_smtp_config, 
                                     COLORS['accent_success']).pack(side='left', padx=(0, 5))
        
        self.create_neumorphic_button(smtp_button_frame, "🧪 Test", 
                                     self.test_smtp_config, 
                                     COLORS['accent_primary']).pack(side='left', padx=5)
        
        self.create_neumorphic_button(smtp_button_frame, "🗑️ Delete", 
                                     self.delete_smtp_config, 
                                     COLORS['accent_danger']).pack(side='left', padx=5)
    
    def create_templates_tab(self):
        """Create the email templates tab"""
        templates_frame = ttk.Frame(self.notebook, style='Dark.TFrame')
        self.notebook.add(templates_frame, text='📝 Templates')
        
        # Templates list and editor
        list_frame = tk.Frame(templates_frame, bg=COLORS['bg_secondary'])
        list_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        tk.Label(list_frame, text="Email Templates", 
                font=FONT_SIZES['header'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(pady=(10, 20))
        
        # Templates list
        self.templates_listbox = tk.Listbox(list_frame, 
                                           bg=COLORS['bg_primary'], 
                                           fg=COLORS['text_primary'],
                                           selectbackground=COLORS['accent_primary'],
                                           font=FONT_SIZES['body'],
                                           height=8)
        self.templates_listbox.pack(fill='x', padx=10, pady=(0, 10))
        self.templates_listbox.bind('<<ListboxSelect>>', self.on_template_select)
        
        # Template buttons
        template_button_frame = tk.Frame(list_frame, bg=COLORS['bg_secondary'])
        template_button_frame.pack(fill='x', padx=10, pady=10)
        
        self.create_neumorphic_button(template_button_frame, "➕ New Template", 
                                     self.create_new_template, 
                                     COLORS['accent_success']).pack(side='left', padx=(0, 5))
        
        self.create_neumorphic_button(template_button_frame, "💾 Save", 
                                     self.save_template, 
                                     COLORS['accent_primary']).pack(side='left', padx=5)
        
        self.create_neumorphic_button(template_button_frame, "🗑️ Delete", 
                                     self.delete_template, 
                                     COLORS['accent_danger']).pack(side='left', padx=5)
        
        # Template editor
        editor_frame = tk.LabelFrame(list_frame, text="Template Editor", 
                                    font=FONT_SIZES['subheader'],
                                    bg=COLORS['bg_secondary'], 
                                    fg=COLORS['text_primary'])
        editor_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Template name
        tk.Label(editor_frame, text="Template Name:", 
                font=FONT_SIZES['body'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(10, 5))
        
        self.template_name_var = tk.StringVar()
        self.template_name_entry = tk.Entry(editor_frame, 
                                           textvariable=self.template_name_var,
                                           font=FONT_SIZES['input'],
                                           bg=COLORS['bg_primary'],
                                           fg=COLORS['text_primary'])
        self.template_name_entry.pack(fill='x', padx=10, pady=(0, 10))
        
        # Subject
        tk.Label(editor_frame, text="Subject:", 
                font=FONT_SIZES['body'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(0, 5))
        
        self.template_subject_var = tk.StringVar()
        self.template_subject_entry = tk.Entry(editor_frame, 
                                              textvariable=self.template_subject_var,
                                              font=FONT_SIZES['input'],
                                              bg=COLORS['bg_primary'],
                                              fg=COLORS['text_primary'])
        self.template_subject_entry.pack(fill='x', padx=10, pady=(0, 10))
        
        # Body
        tk.Label(editor_frame, text="Body (HTML/Text):", 
                font=FONT_SIZES['body'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(anchor='w', padx=10, pady=(0, 5))
        
        self.template_body_text = scrolledtext.ScrolledText(editor_frame, 
                                                           height=10,
                                                           font=FONT_SIZES['input'],
                                                           bg=COLORS['bg_primary'],
                                                           fg=COLORS['text_primary'],
                                                           insertbackground=COLORS['text_primary'])
        self.template_body_text.pack(fill='both', expand=True, padx=10, pady=(0, 10))
    
    def create_analytics_tab(self):
        """Create the analytics and monitoring tab"""
        analytics_frame = ttk.Frame(self.notebook, style='Dark.TFrame')
        self.notebook.add(analytics_frame, text='📊 Analytics')
        
        # Analytics content
        content_frame = tk.Frame(analytics_frame, bg=COLORS['bg_secondary'])
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        tk.Label(content_frame, text="Campaign Analytics", 
                font=FONT_SIZES['header'], 
                bg=COLORS['bg_secondary'], 
                fg=COLORS['text_primary']).pack(pady=(10, 20))
        
        # Stats display
        stats_frame = tk.Frame(content_frame, bg=COLORS['bg_secondary'])
        stats_frame.pack(fill='x', padx=10, pady=10)
        
        # Create stats cards
        self.create_stats_card(stats_frame, "Total Campaigns", "0", 0, 0)
        self.create_stats_card(stats_frame, "Emails Sent", "0", 0, 1)
        self.create_stats_card(stats_frame, "Success Rate", "0%", 0, 2)
        self.create_stats_card(stats_frame, "Active Campaigns", "0", 1, 0)
        self.create_stats_card(stats_frame, "Failed Emails", "0", 1, 1)
        self.create_stats_card(stats_frame, "Bounce Rate", "0%", 1, 2)
        
        # Campaign status display
        status_frame = tk.LabelFrame(content_frame, text="Campaign Status", 
                                    font=FONT_SIZES['subheader'],
                                    bg=COLORS['bg_secondary'], 
                                    fg=COLORS['text_primary'])
        status_frame.pack(fill='both', expand=True, padx=10, pady=20)
        
        self.status_text = scrolledtext.ScrolledText(status_frame, 
                                                    height=15,
                                                    font=FONT_SIZES['small'],
                                                    bg=COLORS['bg_primary'],
                                                    fg=COLORS['text_primary'],
                                                    state='disabled')
        self.status_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_stats_card(self, parent, title, value, row, col):
        """Create a statistics card"""
        card = tk.Frame(parent, bg=COLORS['bg_primary'], relief='raised', bd=1)
        card.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
        
        parent.grid_columnconfigure(col, weight=1)
        
        tk.Label(card, text=title, 
                font=FONT_SIZES['small'], 
                bg=COLORS['bg_primary'], 
                fg=COLORS['text_secondary']).pack(pady=(10, 5))
        
        value_label = tk.Label(card, text=value, 
                              font=FONT_SIZES['header'], 
                              bg=COLORS['bg_primary'], 
                              fg=COLORS['accent_primary'])
        value_label.pack(pady=(0, 10))
        
        # Store reference for updating
        setattr(self, f"stats_{title.lower().replace(' ', '_')}", value_label)
    
    # Event handlers and utility methods
    def on_campaign_select(self, event):
        """Handle campaign selection"""
        selection = self.campaign_listbox.curselection()
        if selection:
            campaign_name = self.campaign_listbox.get(selection[0])
            self.load_campaign_details(campaign_name)
    
    def on_template_select(self, event):
        """Handle template selection"""
        selection = self.templates_listbox.curselection()
        if selection:
            template_name = self.templates_listbox.get(selection[0])
            self.load_template_details(template_name)
    
    def create_new_campaign(self):
        """Create a new campaign"""
        self.clear_campaign_form()
        self.campaign_name_var.set(f"Campaign_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    def save_campaign(self):
        """Save the current campaign"""
        try:
            campaign_data = {
                'name': self.campaign_name_var.get(),
                'smtp_config': self.smtp_config_var.get(),
                'template': self.template_var.get(),
                'recipients_file': self.recipients_file_var.get(),
                'send_rate': self.send_rate_var.get(),
                'scheduled': self.schedule_var.get(),
                'created_at': datetime.now().isoformat(),
                'status': 'draft'
            }
            
            if not all([campaign_data['name'], campaign_data['smtp_config'], 
                       campaign_data['template'], campaign_data['recipients_file']]):
                messagebox.showerror("Error", "Please fill in all required fields")
                return
            
            # Save campaign using email sender
            campaign_id = self.email_sender.create_campaign(
                name=campaign_data['name'],
                smtp_config_name=campaign_data['smtp_config'],
                template_name=campaign_data['template'],
                recipients_file=campaign_data['recipients_file']
            )
            
            if campaign_id:
                messagebox.showinfo("Success", "Campaign saved successfully!")
                self.refresh_campaigns()
            else:
                messagebox.showerror("Error", "Failed to save campaign")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save campaign: {str(e)}")
    
    def start_campaign(self):
        """Start the selected campaign"""
        selection = self.campaign_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a campaign to start")
            return
        
        campaign_name = self.campaign_listbox.get(selection[0])
        try:
            # Find campaign by name
            campaigns = self.mass_sender.get_all_campaigns()
            campaign_id = None
            for campaign in campaigns:
                if campaign.config.name == campaign_name:
                    campaign_id = campaign.id
                    break
            
            if campaign_id:
                success = self.email_sender.start_campaign(campaign_id)
                if success:
                    self.running_campaigns.add(campaign_id)
                    messagebox.showinfo("Success", f"Campaign '{campaign_name}' started!")
                    self.refresh_campaigns()
                else:
                    messagebox.showerror("Error", "Failed to start campaign")
            else:
                messagebox.showerror("Error", "Campaign not found")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start campaign: {str(e)}")
    
    def pause_campaign(self):
        """Pause the selected campaign"""
        selection = self.campaign_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a campaign to pause")
            return
        
        campaign_name = self.campaign_listbox.get(selection[0])
        try:
            campaigns = self.email_sender.get_all_campaigns()
            campaign_id = None
            for campaign in campaigns:
                if campaign.name == campaign_name:
                    campaign_id = campaign.id
                    break
            
            if campaign_id:
                success = self.email_sender.pause_campaign(campaign_id)
                if success:
                    self.running_campaigns.discard(campaign_id)
                    messagebox.showinfo("Success", f"Campaign '{campaign_name}' paused!")
                    self.refresh_campaigns()
                else:
                    messagebox.showerror("Error", "Failed to pause campaign")
            else:
                messagebox.showerror("Error", "Campaign not found")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to pause campaign: {str(e)}")
    
    def delete_campaign(self):
        """Delete the selected campaign"""
        selection = self.campaign_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a campaign to delete")
            return
        
        campaign_name = self.campaign_listbox.get(selection[0])
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{campaign_name}'?"):
            try:
                campaigns = self.email_sender.get_all_campaigns()
                campaign_id = None
                for campaign in campaigns:
                    if campaign.name == campaign_name:
                        campaign_id = campaign.id
                        break
                
                if campaign_id:
                    success = self.email_sender.delete_campaign(campaign_id)
                    if success:
                        messagebox.showinfo("Success", f"Campaign '{campaign_name}' deleted!")
                        self.refresh_campaigns()
                        self.clear_campaign_form()
                    else:
                        messagebox.showerror("Error", "Failed to delete campaign")
                else:
                    messagebox.showerror("Error", "Campaign not found")
                    
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete campaign: {str(e)}")
    
    def browse_recipients_file(self):
        """Browse for recipients CSV file"""
        filename = filedialog.askopenfilename(
            title="Select Recipients File",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            self.recipients_file_var.set(filename)
    
    def add_smtp_config(self):
        """Add new SMTP configuration"""
        # Create a simple dialog for SMTP config
        dialog = SMTPConfigDialog(self.root, self.smtp_manager)
        if dialog.result:
            self.refresh_smtp_configs()
    
    def test_smtp_config(self):
        """Test selected SMTP configuration"""
        selection = self.smtp_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an SMTP configuration to test")
            return
        
        config_name = self.smtp_listbox.get(selection[0])
        try:
            success = self.smtp_manager.test_connection(config_name)
            if success:
                messagebox.showinfo("Success", f"SMTP configuration '{config_name}' is working!")
            else:
                messagebox.showerror("Error", f"SMTP configuration '{config_name}' failed to connect")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to test SMTP: {str(e)}")
    
    def delete_smtp_config(self):
        """Delete selected SMTP configuration"""
        selection = self.smtp_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an SMTP configuration to delete")
            return
        
        config_name = self.smtp_listbox.get(selection[0])
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{config_name}'?"):
            try:
                self.smtp_manager.remove_config(config_name)
                messagebox.showinfo("Success", f"SMTP configuration '{config_name}' deleted!")
                self.refresh_smtp_configs()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete SMTP config: {str(e)}")
    
    def create_new_template(self):
        """Create a new email template"""
        self.clear_template_form()
        self.template_name_var.set(f"Template_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    def save_template(self):
        """Save the current template"""
        try:
            template_data = {
                'name': self.template_name_var.get(),
                'subject': self.template_subject_var.get(),
                'body': self.template_body_text.get('1.0', 'end-1c'),
                'is_html': True
            }
            
            if not all([template_data['name'], template_data['subject'], template_data['body']]):
                messagebox.showerror("Error", "Please fill in all template fields")
                return
            
            success = self.template_manager.create_template(**template_data)
            if success:
                messagebox.showinfo("Success", "Template saved successfully!")
                self.refresh_templates()
            else:
                messagebox.showerror("Error", "Failed to save template")
                
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save template: {str(e)}")
    
    def delete_template(self):
        """Delete selected template"""
        selection = self.templates_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a template to delete")
            return
        
        template_name = self.templates_listbox.get(selection[0])
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{template_name}'?"):
            try:
                self.template_manager.delete_template(template_name)
                messagebox.showinfo("Success", f"Template '{template_name}' deleted!")
                self.refresh_templates()
                self.clear_template_form()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to delete template: {str(e)}")
    
    # Data loading and refreshing methods
    def load_campaigns(self):
        """Load campaigns from the email sender"""
        try:
            self.campaigns = self.email_sender.get_all_campaigns()
            self.refresh_campaigns()
        except Exception as e:
            logger.error(f"Failed to load campaigns: {e}")
    
    def refresh_campaigns(self):
        """Refresh the campaigns list"""
        self.campaign_listbox.delete(0, 'end')
        for campaign in self.campaigns:
            status_icon = "▶️" if campaign.status == CampaignStatus.RUNNING else "⏸️" if campaign.status == CampaignStatus.PAUSED else "📋"
            self.campaign_listbox.insert('end', f"{status_icon} {campaign.config.name}")
    
    def refresh_smtp_configs(self):
        """Refresh SMTP configurations list"""
        self.smtp_listbox.delete(0, 'end')
        self.smtp_config_combo['values'] = []
        
        config_names = self.smtp_manager.get_config_list()
        
        for name in config_names:
            self.smtp_listbox.insert('end', name)
        
        self.smtp_config_combo['values'] = config_names
        if config_names:
            self.smtp_config_combo.set(config_names[0])
    
    def refresh_templates(self):
        """Refresh templates list"""
        self.templates_listbox.delete(0, 'end')
        self.template_combo['values'] = []
        
        templates = self.template_manager.get_all_templates()
        template_names = [template.name for template in templates]
        
        for name in template_names:
            self.templates_listbox.insert('end', name)
        
        self.template_combo['values'] = template_names
        if template_names:
            self.template_combo.set(template_names[0])
    
    def load_campaign_details(self, campaign_name):
        """Load campaign details into the form"""
        for campaign in self.campaigns:
            if campaign.config.name == campaign_name:
                self.campaign_name_var.set(campaign.config.name)
                self.smtp_config_var.set(campaign.config.smtp_config_name)
                self.template_var.set(campaign.config.template_id)
                # Recipients file path is not stored in campaign, leave empty
                self.recipients_file_var.set("")
                break
    
    def load_template_details(self, template_name):
        """Load template details into the editor"""
        template = self.template_manager.get_template(template_name)
        if template:
            self.template_name_var.set(template.name)
            self.template_subject_var.set(template.subject)
            self.template_body_text.delete('1.0', 'end')
            self.template_body_text.insert('1.0', template.body)
    
    def clear_campaign_form(self):
        """Clear the campaign form"""
        self.campaign_name_var.set("")
        self.smtp_config_var.set("")
        self.template_var.set("")
        self.recipients_file_var.set("")
        self.send_rate_var.set(self.config.rate_limiting.emails_per_minute)
        self.schedule_var.set(False)
    
    def clear_template_form(self):
        """Clear the template form"""
        self.template_name_var.set("")
        self.template_subject_var.set("")
        self.template_body_text.delete('1.0', 'end')
    
    def update_campaign_status(self):
        """Update campaign status display"""
        try:
            # Update campaign list
            self.campaigns = self.email_sender.get_all_campaigns()
            
            # Update status text
            self.status_text.config(state='normal')
            self.status_text.delete('1.0', 'end')
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.status_text.insert('end', f"Last Updated: {current_time}\n\n")
            
            for campaign in self.campaigns:
                status_line = f"Campaign: {campaign.config.name}\n"
                status_line += f"Status: {campaign.status.value}\n"
                status_line += f"Emails Sent: {campaign.sent_count}\n"
                # Calculate success rate from sent and failed counts
                total_processed = campaign.sent_count + campaign.failed_count
                success_rate = campaign.sent_count / total_processed if total_processed > 0 else 0
                status_line += f"Success Rate: {success_rate:.1%}\n"
                status_line += "-" * 40 + "\n"
                self.status_text.insert('end', status_line)
            
            self.status_text.config(state='disabled')
            
            # Update stats
            total_campaigns = len(self.campaigns)
            active_campaigns = sum(1 for c in self.campaigns if c.status == CampaignStatus.RUNNING)
            total_emails = sum(c.sent_count for c in self.campaigns)
            
            if hasattr(self, 'stats_total_campaigns'):
                self.stats_total_campaigns.config(text=str(total_campaigns))
            if hasattr(self, 'stats_emails_sent'):
                self.stats_emails_sent.config(text=str(total_emails))
            if hasattr(self, 'stats_active_campaigns'):
                self.stats_active_campaigns.config(text=str(active_campaigns))
            
        except Exception as e:
            logger.error(f"Failed to update campaign status: {e}")
        
        # Schedule next update
        self.root.after(5000, self.update_campaign_status)

class SMTPConfigDialog:
    """Dialog for adding SMTP configuration"""
    
    def __init__(self, parent, smtp_manager):
        self.smtp_manager = smtp_manager
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add SMTP Configuration")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg=COLORS['bg_primary'])
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        self.create_dialog_widgets()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        self.dialog.wait_window()
    
    def create_dialog_widgets(self):
        """Create dialog widgets"""
        main_frame = tk.Frame(self.dialog, bg=COLORS['bg_primary'])
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Title
        tk.Label(main_frame, text="SMTP Configuration", 
                font=FONT_SIZES['header'], 
                bg=COLORS['bg_primary'], 
                fg=COLORS['text_primary']).pack(pady=(0, 20))
        
        # Form fields
        fields = [
            ('Name:', 'name'),
            ('SMTP Server:', 'server'),
            ('Port:', 'port'),
            ('Username:', 'username'),
            ('Password:', 'password')
        ]
        
        self.vars = {}
        for label, key in fields:
            tk.Label(main_frame, text=label, 
                    font=FONT_SIZES['body'], 
                    bg=COLORS['bg_primary'], 
                    fg=COLORS['text_primary']).pack(anchor='w', pady=(10, 5))
            
            self.vars[key] = tk.StringVar()
            entry = tk.Entry(main_frame, 
                           textvariable=self.vars[key],
                           font=FONT_SIZES['input'],
                           bg=COLORS['bg_secondary'],
                           fg=COLORS['text_primary'],
                           show='*' if key == 'password' else '')
            entry.pack(fill='x', pady=(0, 5))
        
        # Use TLS checkbox
        self.use_tls_var = tk.BooleanVar(value=True)
        tk.Checkbutton(main_frame, 
                      text="Use TLS",
                      variable=self.use_tls_var,
                      font=FONT_SIZES['body'],
                      bg=COLORS['bg_primary'],
                      fg=COLORS['text_primary'],
                      selectcolor=COLORS['bg_secondary']).pack(anchor='w', pady=10)
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg=COLORS['bg_primary'])
        button_frame.pack(fill='x', pady=20)
        
        tk.Button(button_frame, text="Save", 
                 command=self.save_config,
                 bg=COLORS['accent_success'], 
                 fg=COLORS['text_primary'],
                 font=FONT_SIZES['button'],
                 **NEUMORPHIC_STYLE).pack(side='left', padx=(0, 10))
        
        tk.Button(button_frame, text="Cancel", 
                 command=self.dialog.destroy,
                 bg=COLORS['accent_danger'], 
                 fg=COLORS['text_primary'],
                 font=FONT_SIZES['button'],
                 **NEUMORPHIC_STYLE).pack(side='left')
    
    def save_config(self):
        """Save the SMTP configuration"""
        try:
            config_data = {
                'server': self.vars['server'].get(),
                'port': int(self.vars['port'].get()),
                'username': self.vars['username'].get(),
                'password': self.vars['password'].get(),
                'use_tls': self.use_tls_var.get()
            }
            
            if not all([config_data['server'], config_data['username'], config_data['password']]):
                messagebox.showerror("Error", "Please fill in all required fields")
                return
            
            success = self.smtp_manager.add_config(self.vars['name'].get(), **config_data)
            if success:
                self.result = True
                messagebox.showinfo("Success", "SMTP configuration saved successfully!")
                self.dialog.destroy()
            else:
                messagebox.showerror("Error", "Failed to save SMTP configuration")
                
        except ValueError:
            messagebox.showerror("Error", "Port must be a valid number")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

def main():
    """Main function to run the campaign GUI"""
    root = tk.Tk()
    app = CampaignGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()