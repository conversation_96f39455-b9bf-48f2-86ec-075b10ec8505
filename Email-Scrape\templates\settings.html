{% extends "base.html" %}

{% block title %}Settings - Advanced Email Delivery System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-cog"></i> Settings
                </h1>
                <button class="btn btn-success" onclick="saveAllSettings()">
                    <i class="fas fa-save"></i> Save All Settings
                </button>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="smtp-tab" data-bs-toggle="tab" data-bs-target="#smtp" type="button" role="tab">
                                <i class="fas fa-envelope"></i> SMTP Configuration
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="delivery-tab" data-bs-toggle="tab" data-bs-target="#delivery" type="button" role="tab">
                                <i class="fas fa-paper-plane"></i> Delivery Settings
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="obfuscation-tab" data-bs-toggle="tab" data-bs-target="#obfuscation" type="button" role="tab">
                                <i class="fas fa-shield-alt"></i> Obfuscation
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="antispam-tab" data-bs-toggle="tab" data-bs-target="#antispam" type="button" role="tab">
                                <i class="fas fa-ban"></i> Anti-Spam
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                <i class="fas fa-server"></i> System
                            </button>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="settingsTabContent">
                        <!-- SMTP Configuration Tab -->
                        <div class="tab-pane fade show active" id="smtp" role="tabpanel">
                            <h5 class="mb-3">SMTP Server Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtpProvider" class="form-label">Provider</label>
                                        <select class="form-select" id="smtpProvider">
                                            <option value="mailhop">MailHop</option>
                                            <option value="gmail">Gmail</option>
                                            <option value="outlook">Outlook</option>
                                            <option value="custom">Custom</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtpServer" class="form-label">SMTP Server</label>
                                        <input type="text" class="form-control" id="smtpServer" value="outbound.mailhop.org">
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtpPort" class="form-label">Port</label>
                                        <input type="number" class="form-control" id="smtpPort" value="465">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtpUsername" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="smtpUsername" value="Carlosfenandezlawfirm">
                                    </div>
                                    <div class="mb-3">
                                        <label for="smtpPassword" class="form-label">Password</label>
                                        <input type="password" class="form-control" id="smtpPassword" value="Americana123456789@">
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useSSL" checked>
                                            <label class="form-check-label" for="useSSL">Use SSL</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useTLS">
                                            <label class="form-check-label" for="useTLS">Use TLS</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button class="btn btn-primary me-2" onclick="testSMTPConnection()">
                                        <i class="fas fa-plug"></i> Test Connection
                                    </button>
                                    <button class="btn btn-success" onclick="saveSMTPConfig()">
                                        <i class="fas fa-save"></i> Save SMTP Config
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Delivery Settings Tab -->
                        <div class="tab-pane fade" id="delivery" role="tabpanel">
                            <h5 class="mb-3">Email Delivery Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="rateLimit" class="form-label">Rate Limit (emails/hour)</label>
                                        <input type="number" class="form-control" id="rateLimit" value="100">
                                    </div>
                                    <div class="mb-3">
                                        <label for="delayBetween" class="form-label">Delay Between Emails (seconds)</label>
                                        <input type="number" class="form-control" id="delayBetween" value="2" step="0.1">
                                    </div>
                                    <div class="mb-3">
                                        <label for="maxRetries" class="form-label">Max Retries</label>
                                        <input type="number" class="form-control" id="maxRetries" value="3">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="randomizeDelays" checked>
                                            <label class="form-check-label" for="randomizeDelays">Randomize Delays</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="validateMX" checked>
                                            <label class="form-check-label" for="validateMX">Validate MX Records</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trackBounces" checked>
                                            <label class="form-check-label" for="trackBounces">Track Bounces</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Obfuscation Tab -->
                        <div class="tab-pane fade" id="obfuscation" role="tabpanel">
                            <h5 class="mb-3">Content Obfuscation Settings</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="randomizeHeaders" checked>
                                            <label class="form-check-label" for="randomizeHeaders">Randomize Headers</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="varyContent" checked>
                                            <label class="form-check-label" for="varyContent">Vary Content</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useHtmlEntities" checked>
                                            <label class="form-check-label" for="useHtmlEntities">Use HTML Entities</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="randomizeWhitespace" checked>
                                            <label class="form-check-label" for="randomizeWhitespace">Randomize Whitespace</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="splitLinks" checked>
                                            <label class="form-check-label" for="splitLinks">Split Links</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useUnicodeVariants" checked>
                                            <label class="form-check-label" for="useUnicodeVariants">Use Unicode Variants</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="insertInvisibleChars" checked>
                                            <label class="form-check-label" for="insertInvisibleChars">Insert Invisible Characters</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="obfuscateFilenames" checked>
                                            <label class="form-check-label" for="obfuscateFilenames">Obfuscate Attachment Names</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Anti-Spam Tab -->
                        <div class="tab-pane fade" id="antispam" role="tabpanel">
                            <h5 class="mb-3">Anti-Spam Protection</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkSPF" checked>
                                            <label class="form-check-label" for="checkSPF">Check SPF Records</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkDKIM" checked>
                                            <label class="form-check-label" for="checkDKIM">Check DKIM</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkDMARC" checked>
                                            <label class="form-check-label" for="checkDMARC">Check DMARC</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="checkBlacklists" checked>
                                            <label class="form-check-label" for="checkBlacklists">Check Blacklists</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="useReputationWarming" checked>
                                            <label class="form-check-label" for="useReputationWarming">Use Reputation Warming</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Tab -->
                        <div class="tab-pane fade" id="system" role="tabpanel">
                            <h5 class="mb-3">System Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="logLevel" class="form-label">Log Level</label>
                                        <select class="form-select" id="logLevel">
                                            <option value="DEBUG">Debug</option>
                                            <option value="INFO" selected>Info</option>
                                            <option value="WARNING">Warning</option>
                                            <option value="ERROR">Error</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="maxLogSize" class="form-label">Max Log Size (MB)</label>
                                        <input type="number" class="form-control" id="maxLogSize" value="10">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableFileLogging" checked>
                                            <label class="form-check-label" for="enableFileLogging">Enable File Logging</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableConsoleLogging" checked>
                                            <label class="form-check-label" for="enableConsoleLogging">Enable Console Logging</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <h6>System Actions</h6>
                                    <button class="btn btn-warning me-2" onclick="clearLogs()">
                                        <i class="fas fa-trash"></i> Clear Logs
                                    </button>
                                    <button class="btn btn-info me-2" onclick="exportSettings()">
                                        <i class="fas fa-download"></i> Export Settings
                                    </button>
                                    <button class="btn btn-secondary" onclick="resetToDefaults()">
                                        <i class="fas fa-undo"></i> Reset to Defaults
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="statusMessages"></div>
</div>

<script>
// Settings JavaScript functionality
function testSMTPConnection() {
    showStatus('Testing SMTP connection...', 'info');
    
    const config = {
        server: document.getElementById('smtpServer').value,
        port: document.getElementById('smtpPort').value,
        username: document.getElementById('smtpUsername').value,
        password: document.getElementById('smtpPassword').value,
        use_ssl: document.getElementById('useSSL').checked,
        use_tls: document.getElementById('useTLS').checked
    };
    
    fetch('/api/smtp/test', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus('SMTP connection successful!', 'success');
        } else {
            showStatus('SMTP connection failed: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showStatus('Error testing SMTP: ' + error, 'danger');
    });
}

function saveSMTPConfig() {
    showStatus('Saving SMTP configuration...', 'info');
    
    const config = {
        provider: document.getElementById('smtpProvider').value,
        server: document.getElementById('smtpServer').value,
        port: document.getElementById('smtpPort').value,
        username: document.getElementById('smtpUsername').value,
        password: document.getElementById('smtpPassword').value,
        use_ssl: document.getElementById('useSSL').checked,
        use_tls: document.getElementById('useTLS').checked
    };
    
    fetch('/api/smtp/save', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus('SMTP configuration saved successfully!', 'success');
        } else {
            showStatus('Failed to save SMTP configuration: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showStatus('Error saving SMTP configuration: ' + error, 'danger');
    });
}

function saveAllSettings() {
    showStatus('Saving all settings...', 'info');
    
    const settings = {
        smtp: {
            provider: document.getElementById('smtpProvider').value,
            server: document.getElementById('smtpServer').value,
            port: document.getElementById('smtpPort').value,
            username: document.getElementById('smtpUsername').value,
            password: document.getElementById('smtpPassword').value,
            use_ssl: document.getElementById('useSSL').checked,
            use_tls: document.getElementById('useTLS').checked
        },
        delivery: {
            rate_limit: document.getElementById('rateLimit').value,
            delay_between: document.getElementById('delayBetween').value,
            max_retries: document.getElementById('maxRetries').value,
            randomize_delays: document.getElementById('randomizeDelays').checked,
            validate_mx: document.getElementById('validateMX').checked,
            track_bounces: document.getElementById('trackBounces').checked
        },
        obfuscation: {
            randomize_headers: document.getElementById('randomizeHeaders').checked,
            vary_content: document.getElementById('varyContent').checked,
            use_html_entities: document.getElementById('useHtmlEntities').checked,
            randomize_whitespace: document.getElementById('randomizeWhitespace').checked,
            split_links: document.getElementById('splitLinks').checked,
            use_unicode_variants: document.getElementById('useUnicodeVariants').checked,
            insert_invisible_chars: document.getElementById('insertInvisibleChars').checked,
            obfuscate_filenames: document.getElementById('obfuscateFilenames').checked
        },
        antispam: {
            check_spf: document.getElementById('checkSPF').checked,
            check_dkim: document.getElementById('checkDKIM').checked,
            check_dmarc: document.getElementById('checkDMARC').checked,
            check_blacklists: document.getElementById('checkBlacklists').checked,
            use_reputation_warming: document.getElementById('useReputationWarming').checked
        },
        system: {
            log_level: document.getElementById('logLevel').value,
            max_log_size: document.getElementById('maxLogSize').value,
            enable_file_logging: document.getElementById('enableFileLogging').checked,
            enable_console_logging: document.getElementById('enableConsoleLogging').checked
        }
    };
    
    fetch('/api/settings/save', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatus('All settings saved successfully!', 'success');
        } else {
            showStatus('Failed to save settings: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showStatus('Error saving settings: ' + error, 'danger');
    });
}

function showStatus(message, type) {
    const statusDiv = document.getElementById('statusMessages');
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    statusDiv.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function clearLogs() {
    if (confirm('Are you sure you want to clear all logs?')) {
        fetch('/api/system/clear-logs', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            showStatus(data.message, data.success ? 'success' : 'danger');
        });
    }
}

function exportSettings() {
    fetch('/api/settings/export')
    .then(response => response.blob())
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'email_system_settings.json';
        a.click();
        window.URL.revokeObjectURL(url);
        showStatus('Settings exported successfully!', 'success');
    });
}

function resetToDefaults() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
        fetch('/api/settings/reset', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                showStatus('Failed to reset settings: ' + data.error, 'danger');
            }
        });
    }
}

// Load current settings on page load
document.addEventListener('DOMContentLoaded', function() {
    fetch('/api/settings/load')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Populate form fields with current settings
            // This would be implemented based on the actual API response structure
        }
    })
    .catch(error => {
        console.error('Error loading settings:', error);
    });
});
</script>
{% endblock %}
