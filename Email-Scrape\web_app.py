from flask import Flask, render_template, request, jsonify, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import asyncio
import threading
import json
import os
from datetime import datetime
from bulk_scraper import BulkScraper
from email_generator import EmailGenerator
from mass_email_sender import Mass<PERSON>mail<PERSON><PERSON>
from email_templates import Template<PERSON>anager
from smtp_config import SMTPManager

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize components
bulk_scraper = BulkScraper()
email_generator = EmailGenerator()
mass_email_sender = MassEmailSender()
template_manager = TemplateManager()
smtp_config_manager = SMTPManager()

# Global variables for tracking
scraping_progress = {
    'is_running': False,
    'current_platform': '',
    'progress': 0,
    'results': {},
    'total_found': 0
}

@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html')

@app.route('/scraper')
def scraper():
    """Email scraper interface"""
    return render_template('scraper.html')

@app.route('/campaigns')
def campaigns():
    """Campaign management interface"""
    return render_template('campaigns.html')

@app.route('/templates')
def templates():
    """Template management interface"""
    return render_template('templates.html')

@app.route('/settings')
def settings():
    """Settings interface"""
    return render_template('settings.html')

# Static files
@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

# API Routes
@app.route('/api/scrape/start', methods=['POST'])
def start_scraping():
    """Start email scraping process"""
    global scraping_progress
    
    if scraping_progress['is_running']:
        return jsonify({'error': 'Scraping already in progress'}), 400
    
    data = request.get_json()
    query = data.get('query', '')
    location = data.get('location', '')
    max_results = data.get('max_results', 100)
    
    if not query:
        return jsonify({'error': 'Query is required'}), 400
    
    # Reset progress
    scraping_progress = {
        'is_running': True,
        'current_platform': '',
        'progress': 0,
        'results': {},
        'total_found': 0
    }
    
    # Start scraping in background thread
    thread = threading.Thread(
        target=run_scraping_async,
        args=(query, location, max_results)
    )
    thread.daemon = True
    thread.start()
    
    return jsonify({'message': 'Scraping started', 'status': 'started'})

@app.route('/api/scrape/stop', methods=['POST'])
def stop_scraping():
    """Stop email scraping process"""
    global scraping_progress
    scraping_progress['is_running'] = False
    return jsonify({'message': 'Scraping stopped', 'status': 'stopped'})

@app.route('/api/scrape/status', methods=['GET'])
def scraping_status():
    """Get current scraping status"""
    return jsonify(scraping_progress)

@app.route('/api/generate/emails', methods=['POST'])
def generate_emails():
    """Generate email variations"""
    data = request.get_json()
    names = data.get('names', [])
    region = data.get('region', 'global')
    count = data.get('count', 100)
    
    if not names:
        return jsonify({'error': 'Names are required'}), 400
    
    try:
        generated_emails = email_generator.generate_bulk_emails(
            names, [region], emails_per_name=count
        )
        
        total_generated = sum(len(emails) for emails in generated_emails.values())
        
        return jsonify({
            'generated_emails': generated_emails,
            'total_generated': total_generated,
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/templates', methods=['GET'])
def get_templates():
    """Get all email templates"""
    try:
        templates = template_manager.get_all_templates()
        return jsonify({
            'templates': [{
                'id': template.id,
                'name': template.name,
                'subject': template.subject,
                'content': template.content,
                'created_at': template.created_at.isoformat() if template.created_at else None
            } for template in templates],
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/campaigns', methods=['GET'])
def get_campaigns():
    """Get all campaigns"""
    try:
        campaigns = mass_email_sender.get_all_campaigns()
        return jsonify({
            'campaigns': [{
                'id': campaign.id,
                'name': campaign.config.name,
                'status': campaign.status,
                'sent_count': campaign.sent_count,
                'failed_count': campaign.failed_count,
                'created_at': campaign.created_at.isoformat() if campaign.created_at else None
            } for campaign in campaigns],
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/smtp-configs', methods=['GET'])
def get_smtp_configs():
    """Get all SMTP configurations"""
    try:
        configs = smtp_config_manager.get_config_list()
        return jsonify({
            'smtp_configs': [{
                'name': config.name,
                'host': config.host,
                'port': config.port,
                'username': config.username,
                'use_tls': config.use_tls,
                'use_ssl': config.use_ssl
            } for config in configs],
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dashboard/stats', methods=['GET'])
def get_dashboard_stats():
    """Get dashboard statistics"""
    try:
        templates = template_manager.get_all_templates()
        campaigns = mass_email_sender.get_all_campaigns()
        smtp_configs = smtp_config_manager.get_config_list()
        
        total_emails_sent = sum(campaign.sent_count for campaign in campaigns)
        active_campaigns = sum(1 for campaign in campaigns if campaign.status == 'running')
        
        return jsonify({
            'total_emails': total_emails_sent,
            'active_campaigns': active_campaigns,
            'templates': len(templates),
            'smtp_configs': len(smtp_configs),
            'status': 'success'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def run_scraping_async(query, location, max_results):
    """Run scraping in async context"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(scrape_platforms(query, location, max_results))
    except Exception as e:
        print(f"Scraping error: {e}")
        socketio.emit('scraping_error', {'error': str(e)})
    finally:
        loop.close()

async def scrape_platforms(query, location, max_results):
    """Scrape all platforms and emit progress updates"""
    global scraping_progress
    
    try:
        # Get platform list
        all_platforms = []
        for category, platforms in bulk_scraper.platforms.items():
            all_platforms.extend([(category, name, config) for name, config in platforms.items()])
        
        total_platforms = len(all_platforms)
        
        for i, (category, platform_name, platform_config) in enumerate(all_platforms):
            if not scraping_progress['is_running']:
                break
                
            scraping_progress['current_platform'] = platform_name
            scraping_progress['progress'] = int((i / total_platforms) * 100)
            
            # Emit progress update
            socketio.emit('scraping_progress', {
                'platform': platform_name,
                'progress': scraping_progress['progress'],
                'total_platforms': total_platforms,
                'current_index': i + 1
            })
            
            try:
                # Scrape individual platform
                results = await bulk_scraper._scrape_platform(
                    platform_name, platform_config, query, location, max_results
                )
                
                if results:
                    scraping_progress['results'][platform_name] = results
                    scraping_progress['total_found'] += len(results)
                    
                    # Emit results update
                    socketio.emit('scraping_results', {
                        'platform': platform_name,
                        'results': results,
                        'total_found': scraping_progress['total_found']
                    })
                    
            except Exception as e:
                print(f"Error scraping {platform_name}: {e}")
                socketio.emit('scraping_platform_error', {
                    'platform': platform_name,
                    'error': str(e)
                })
        
        # Scraping completed
        scraping_progress['is_running'] = False
        scraping_progress['progress'] = 100
        
        socketio.emit('scraping_complete', {
            'total_found': scraping_progress['total_found'],
            'platforms_scraped': len(scraping_progress['results']),
            'results': scraping_progress['results']
        })
        
    except Exception as e:
        scraping_progress['is_running'] = False
        socketio.emit('scraping_error', {'error': str(e)})

# WebSocket Events
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    emit('connected', {'status': 'Connected to Email Scraper'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('get_status')
def handle_get_status():
    emit('status_update', scraping_progress)

# Settings API Routes
@app.route('/api/smtp/test', methods=['POST'])
def test_smtp():
    """Test SMTP connection"""
    try:
        data = request.get_json()
        # Test SMTP connection with provided settings
        success = smtp_config_manager.test_connection()
        return jsonify({
            'success': success,
            'message': 'SMTP connection successful' if success else 'SMTP connection failed'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/smtp/save', methods=['POST'])
def save_smtp():
    """Save SMTP configuration"""
    try:
        data = request.get_json()
        # Save SMTP configuration
        config_name = f"{data['provider']}_config"
        success = smtp_config_manager.add_config(
            name=config_name,
            provider=data['provider'],
            username=data['username'],
            password=data['password'],
            custom_server=data.get('server'),
            custom_port=data.get('port')
        )
        return jsonify({
            'success': success,
            'message': 'SMTP configuration saved successfully' if success else 'Failed to save SMTP configuration'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/save', methods=['POST'])
def save_settings():
    """Save all settings"""
    try:
        data = request.get_json()
        # Save settings to configuration file
        import json
        with open('advanced_settings.json', 'w') as f:
            json.dump(data, f, indent=2)
        return jsonify({
            'success': True,
            'message': 'All settings saved successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/load', methods=['GET'])
def load_settings():
    """Load current settings"""
    try:
        import json
        try:
            with open('advanced_settings.json', 'r') as f:
                settings = json.load(f)
        except FileNotFoundError:
            # Return default settings
            settings = {
                'smtp': {
                    'provider': 'mailhop',
                    'server': 'outbound.mailhop.org',
                    'port': 465,
                    'username': 'Carlosfenandezlawfirm',
                    'use_ssl': True,
                    'use_tls': False
                },
                'delivery': {
                    'rate_limit': 100,
                    'delay_between': 2.0,
                    'max_retries': 3,
                    'randomize_delays': True,
                    'validate_mx': True,
                    'track_bounces': True
                },
                'obfuscation': {
                    'randomize_headers': True,
                    'vary_content': True,
                    'use_html_entities': True,
                    'randomize_whitespace': True,
                    'split_links': True,
                    'use_unicode_variants': True,
                    'insert_invisible_chars': True,
                    'obfuscate_filenames': True
                },
                'antispam': {
                    'check_spf': True,
                    'check_dkim': True,
                    'check_dmarc': True,
                    'check_blacklists': True,
                    'use_reputation_warming': True
                },
                'system': {
                    'log_level': 'INFO',
                    'max_log_size': 10,
                    'enable_file_logging': True,
                    'enable_console_logging': True
                }
            }

        return jsonify({
            'success': True,
            'settings': settings
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/export', methods=['GET'])
def export_settings():
    """Export settings as JSON file"""
    try:
        import json
        from flask import send_file
        import tempfile

        # Load current settings
        try:
            with open('advanced_settings.json', 'r') as f:
                settings = json.load(f)
        except FileNotFoundError:
            settings = {}

        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(settings, temp_file, indent=2)
        temp_file.close()

        return send_file(temp_file.name, as_attachment=True, download_name='email_system_settings.json')
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/settings/reset', methods=['POST'])
def reset_settings():
    """Reset settings to defaults"""
    try:
        import os
        # Remove settings file to reset to defaults
        if os.path.exists('advanced_settings.json'):
            os.remove('advanced_settings.json')
        return jsonify({
            'success': True,
            'message': 'Settings reset to defaults'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/system/clear-logs', methods=['POST'])
def clear_logs():
    """Clear system logs"""
    try:
        import os
        import glob

        # Clear log files
        log_files = glob.glob('*.log')
        for log_file in log_files:
            try:
                with open(log_file, 'w') as f:
                    f.write('')
            except Exception:
                pass

        return jsonify({
            'success': True,
            'message': 'Logs cleared successfully'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    print("Starting Email Scraper Web Application...")
    print("Dashboard: http://localhost:63475")
    print("Email Scraper: http://localhost:63475/scraper")
    print("Campaigns: http://localhost:63475/campaigns")
    print("Templates: http://localhost:63475/templates")
    print("Settings: http://localhost:63475/settings")
    
    socketio.run(app, host='0.0.0.0', port=63475, debug=True)