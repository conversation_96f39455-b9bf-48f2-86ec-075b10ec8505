#!/usr/bin/env python3
"""
Bulk Scraper Module
Handles scraping from multiple platforms: social media, business directories, B2B sites
"""

import asyncio
import time
import random
from typing import List, Dict, Set, Optional
from urllib.parse import urljoin, quote_plus
from loguru import logger
from email_scraper import WebScraper
from email_generator import EmailGenerator
from config import get_config

class BulkScraper(WebScraper):
    def __init__(self):
        super().__init__()
        self.email_generator = EmailGenerator()
        self.platforms = {
            'social_media': {
                'linkedin': {
                    'search_url': 'https://www.linkedin.com/search/results/people/?keywords={query}',
                    'selectors': {
                        'profiles': '.reusable-search__result-container',
                        'name': '.entity-result__title-text a span[aria-hidden="true"]',
                        'title': '.entity-result__primary-subtitle',
                        'company': '.entity-result__secondary-subtitle'
                    }
                },
                'facebook': {
                    'search_url': 'https://www.facebook.com/search/people/?q={query}',
                    'selectors': {
                        'profiles': '[data-testid="search_result"]',
                        'name': 'a[role="link"] span',
                        'info': '.x1lliihq'
                    }
                },
                'twitter': {
                    'search_url': 'https://twitter.com/search?q={query}&src=typed_query&f=user',
                    'selectors': {
                        'profiles': '[data-testid="UserCell"]',
                        'name': '[data-testid="UserName"]',
                        'handle': '[data-testid="UserScreenName"]'
                    }
                },
                'instagram': {
                    'search_url': 'https://www.instagram.com/explore/tags/{query}/',
                    'selectors': {
                        'posts': 'article a',
                        'profile': 'a[href*="/p/"]'
                    }
                }
            },
            'business_directories': {
                'yellowpages': {
                    'search_url': 'https://www.yellowpages.com/search?search_terms={query}&geo_location_terms={location}',
                    'selectors': {
                        'businesses': '.result',
                        'name': '.business-name',
                        'phone': '.phones',
                        'address': '.street-address',
                        'website': '.track-visit-website'
                    }
                },
                'yelp': {
                    'search_url': 'https://www.yelp.com/search?find_desc={query}&find_loc={location}',
                    'selectors': {
                        'businesses': '[data-testid="serp-ia-card"]',
                        'name': 'h3 a',
                        'phone': '[data-testid="phone-number"]',
                        'address': '[data-testid="address"]'
                    }
                },
                'google_business': {
                    'search_url': 'https://www.google.com/maps/search/{query}+{location}',
                    'selectors': {
                        'businesses': '[data-result-index]',
                        'name': '[data-value="title"]',
                        'address': '[data-value="address"]',
                        'phone': '[data-value="phone"]'
                    }
                },
                'whitepages': {
                    'search_url': 'https://www.whitepages.com/search/FindPerson?name={query}&where={location}',
                    'selectors': {
                        'people': '.person-result',
                        'name': '.person-name',
                        'address': '.person-address',
                        'phone': '.person-phone'
                    }
                }
            },
            'b2b_platforms': {
                'alibaba': {
                    'search_url': 'https://www.alibaba.com/trade/search?fsb=y&IndexArea=product_en&CatId=&SearchText={query}',
                    'selectors': {
                        'suppliers': '.organic-offer-wrapper',
                        'company': '.organic-offer-title',
                        'contact': '.contact-info'
                    }
                },
                'aliexpress': {
                    'search_url': 'https://www.aliexpress.com/wholesale?SearchText={query}',
                    'selectors': {
                        'products': '.product-item',
                        'store': '.store-name',
                        'seller': '.seller-info'
                    }
                },
                'thomasnet': {
                    'search_url': 'https://www.thomasnet.com/search?cov=NA&heading={query}',
                    'selectors': {
                        'companies': '.company-result',
                        'name': '.company-name',
                        'contact': '.contact-info'
                    }
                },
                'globalsources': {
                    'search_url': 'https://www.globalsources.com/gsol/I/{query}.htm',
                    'selectors': {
                        'suppliers': '.supplier-item',
                        'company': '.supplier-name',
                        'contact': '.contact-btn'
                    }
                },
                'made_in_china': {
                    'search_url': 'https://www.made-in-china.com/products-search/hot-china-products/{query}.html',
                    'selectors': {
                        'suppliers': '.item-main',
                        'company': '.company-name',
                        'contact': '.contact-supplier'
                    }
                }
            },
            'professional_networks': {
                'crunchbase': {
                    'search_url': 'https://www.crunchbase.com/discover/organization.companies/field/organizations/num_employees_enum/{query}',
                    'selectors': {
                        'companies': '.cb-card',
                        'name': '.cb-card__title',
                        'description': '.cb-card__body'
                    }
                },
                'angel_list': {
                    'search_url': 'https://angel.co/companies?query={query}',
                    'selectors': {
                        'startups': '.startup-item',
                        'name': '.startup-name',
                        'team': '.team-info'
                    }
                },
                'zoominfo': {
                    'search_url': 'https://www.zoominfo.com/s/#{query}',
                    'selectors': {
                        'contacts': '.contact-card',
                        'name': '.contact-name',
                        'title': '.contact-title',
                        'company': '.contact-company'
                    }
                }
            }
        }
    
    async def bulk_scrape_all_platforms(self, query: str, location: str = "", 
                                      max_results_per_platform: int = 100) -> Dict[str, List[Dict]]:
        """
        Scrape contacts from all available platforms
        
        Args:
            query: Search query/keyword
            location: Location filter (for business directories)
            max_results_per_platform: Maximum results per platform
            
        Returns:
            Dictionary with platform results
        """
        results = {}
        
        logger.info(f"Starting bulk scrape for query: '{query}'")
        
        # Scrape each platform category
        for category, platforms in self.platforms.items():
            logger.info(f"Scraping {category} platforms...")
            category_results = []
            
            for platform_name, platform_config in platforms.items():
                try:
                    platform_results = await self._scrape_platform(
                        platform_name, platform_config, query, location, max_results_per_platform
                    )
                    category_results.extend(platform_results)
                    
                    # Add delay between platforms
                    await asyncio.sleep(random.uniform(2, 5))
                    
                except Exception as e:
                    logger.error(f"Error scraping {platform_name}: {str(e)}")
                    continue
            
            results[category] = category_results
            
        return results
    
    async def _scrape_platform(self, platform_name: str, platform_config: Dict, 
                             query: str, location: str, max_results: int) -> List[Dict]:
        """
        Scrape a specific platform
        """
        logger.info(f"Scraping {platform_name}...")
        
        # Format search URL
        search_url = platform_config['search_url'].format(
            query=quote_plus(query),
            location=quote_plus(location) if location else ""
        )
        
        try:
            page = await self.create_page(use_proxy=True)
            
            # Navigate with retry logic
            success = await self.navigate_with_retry(page, search_url)
            if not success:
                logger.warning(f"Failed to load {platform_name} search page")
                return []
            
            # Wait for content to load
            await asyncio.sleep(random.uniform(2, 4))
            
            # Extract data based on platform selectors
            results = await self._extract_platform_data(page, platform_config, platform_name, max_results)
            
            await page.close()
            
            logger.info(f"Extracted {len(results)} results from {platform_name}")
            return results
            
        except Exception as e:
            logger.error(f"Error scraping {platform_name}: {str(e)}")
            return []
    
    async def _extract_platform_data(self, page, platform_config: Dict, 
                                   platform_name: str, max_results: int) -> List[Dict]:
        """
        Extract data from platform page based on selectors
        """
        results = []
        selectors = platform_config['selectors']
        
        try:
            # Get main container elements
            main_selector = list(selectors.values())[0]  # First selector is usually the main container
            elements = await page.query_selector_all(main_selector)
            
            for i, element in enumerate(elements[:max_results]):
                try:
                    result = {
                        'platform': platform_name,
                        'source_url': page.url,
                        'scraped_at': time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    # Extract data based on available selectors
                    for field, selector in selectors.items():
                        if field == list(selectors.keys())[0]:  # Skip main container selector
                            continue
                            
                        try:
                            field_element = await element.query_selector(selector)
                            if field_element:
                                text = await field_element.inner_text()
                                result[field] = text.strip() if text else ""
                            else:
                                result[field] = ""
                        except:
                            result[field] = ""
                    
                    # Try to extract email if present
                    email = await self._extract_email_from_element(element)
                    if email:
                        result['email'] = email
                    
                    # Generate potential emails based on extracted name
                    if 'name' in result and result['name']:
                        generated_emails = self.email_generator.generate_email_variations(
                            result['name'], 'global', 10
                        )
                        result['generated_emails'] = generated_emails[:5]  # Top 5 variations
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.debug(f"Error extracting data from element {i}: {str(e)}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error extracting data from {platform_name}: {str(e)}")
            
        return results
    
    async def _extract_email_from_element(self, element) -> Optional[str]:
        """
        Try to extract email from an element
        """
        try:
            # Get all text content
            text = await element.inner_text()
            
            # Look for email patterns
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text)
            
            if emails:
                return emails[0]  # Return first found email
                
        except:
            pass
            
        return None
    
    def generate_targeted_emails(self, names: List[str], companies: List[str] = None, 
                               region: str = 'global') -> Dict[str, List[str]]:
        """
        Generate targeted emails for specific names and companies
        
        Args:
            names: List of names to generate emails for
            companies: List of company names (optional)
            region: Geographic region for domain selection
            
        Returns:
            Dictionary with generated emails
        """
        results = {}
        
        # Generate personal emails
        for name in names:
            personal_emails = self.email_generator.generate_email_variations(
                name, region, 1000, include_business=True
            )
            results[f"{name}_personal"] = personal_emails
            
            # Generate company-specific emails if companies provided
            if companies:
                for company in companies:
                    company_domain = f"{company.lower().replace(' ', '')}.com"
                    company_emails = []
                    
                    # Generate variations with company domain
                    name_variations = self.email_generator._generate_name_variations(name.lower())
                    for variation in name_variations[:50]:  # Limit variations
                        company_emails.append(f"{variation}@{company_domain}")
                    
                    results[f"{name}_{company}"] = company_emails
        
        return results
    
    async def scrape_with_generated_emails(self, query: str, names: List[str], 
                                         region: str = 'global') -> Dict[str, any]:
        """
        Combine platform scraping with email generation
        
        Args:
            query: Search query for platforms
            names: Names to generate emails for
            region: Geographic region
            
        Returns:
            Combined results with scraped data and generated emails
        """
        # Scrape platforms
        scraped_results = await self.bulk_scrape_all_platforms(query)
        
        # Generate emails
        generated_emails = self.email_generator.generate_bulk_emails(
            names, [region], emails_per_name=1000
        )
        
        # Combine results
        combined_results = {
            'scraped_contacts': scraped_results,
            'generated_emails': generated_emails,
            'total_scraped': sum(len(contacts) for contacts in scraped_results.values()),
            'total_generated': sum(len(emails) for emails in generated_emails.values()),
            'query': query,
            'region': region,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return combined_results

# Example usage
if __name__ == "__main__":
    async def main():
        scraper = BulkScraper()
        
        # Example: Scrape for "marketing agency" and generate emails for common names
        query = "marketing agency"
        names = ["john", "sarah", "michael", "anna", "david"]
        
        results = await scraper.scrape_with_generated_emails(query, names, "north_america")
        
        print(f"Total scraped contacts: {results['total_scraped']}")
        print(f"Total generated emails: {results['total_generated']}")
        
        # Save results
        import json
        with open('bulk_scrape_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print("Results saved to bulk_scrape_results.json")
    
    asyncio.run(main())