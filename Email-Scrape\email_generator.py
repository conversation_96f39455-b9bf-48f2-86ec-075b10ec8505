#!/usr/bin/env python3
"""
Email Generator Module
Generates email variations based on names and regional domains
"""

import itertools
import random
from typing import List, Dict, Set
import json
from loguru import logger

class EmailGenerator:
    def __init__(self):
        self.regional_domains = {
            'global': [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
                'icloud.com', 'protonmail.com', 'tutanota.com', 'zoho.com', 'mail.com'
            ],
            'north_america': [
                # USA - Major providers
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
                'comcast.net', 'verizon.net', 'att.net', 'sbcglobal.net', 'cox.net',
                'charter.net', 'earthlink.net', 'juno.com', 'msn.com', 'live.com',
                'me.com', 'icloud.com', 'bellsouth.net', 'roadrunner.com', 'netzero.net',
                # USA - Regional/Business
                'optonline.net', 'rr.com', 'windstream.net', 'suddenlink.net',
                'centurylink.net', 'frontier.com', 'optimum.net', 'twc.com',
                'cablevision.com', 'rcn.com', 'mediacom.net', 'wow.com',
                # Canada
                'rogers.com', 'bell.net', 'sympatico.ca', 'shaw.ca', 'telus.net',
                'videotron.ca', 'cogeco.ca', 'eastlink.ca', 'sasktel.net',
                'mts.net', 'gmail.ca', 'yahoo.ca', 'hotmail.ca'
            ],
            'europe': [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'web.de', 'gmx.de', 't-online.de', 'freenet.de', 'arcor.de',
                'orange.fr', 'free.fr', 'wanadoo.fr', 'laposte.net',
                'libero.it', 'virgilio.it', 'alice.it', 'tiscali.it',
                'terra.es', 'telefonica.net', 'ya.ru', 'mail.ru', 'yandex.ru'
            ],
            'asia': [
                # China
                '126.com', '163.com', 'qq.com', 'sina.com', 'sohu.com', 'yeah.net',
                'tom.com', 'foxmail.com', 'vip.sina.com', 'vip.163.com', 'aliyun.com',
                # Japan
                'yahoo.co.jp', 'gmail.com', 'hotmail.com', 'softbank.ne.jp', 
                'docomo.ne.jp', 'au.com', 'biglobe.ne.jp', 'nifty.com', 'so-net.ne.jp',
                # South Korea
                'naver.com', 'daum.net', 'hanmail.net', 'yahoo.co.kr', 'nate.com',
                'korea.com', 'chol.com', 'empal.com', 'dreamwiz.com',
                # India
                'rediffmail.com', 'yahoo.co.in', 'gmail.com', 'hotmail.co.in',
                'indiatimes.com', 'sify.com', 'in.com', 'vsnl.net', 'sancharnet.in',
                # Southeast Asia
                'yahoo.com.sg', 'singnet.com.sg', 'pacific.net.sg', 'starhub.net.sg',
                'yahoo.com.my', 'hotmail.my', 'tm.net.my', 'streamyx.com',
                'yahoo.co.th', 'hotmail.co.th', 'gmail.co.th', 'sanook.com',
                'yahoo.com.ph', 'gmail.com.ph', 'yahoo.co.id', 'gmail.co.id',
                # Middle East
                'maktoob.com', 'yahoo.com.sa', 'hotmail.sa', 'gmail.sa',
                'emirates.net.ae', 'eim.ae', 'yahoo.ae'
            ],
            'oceania': [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'bigpond.com', 'optusnet.com.au', 'iinet.net.au',
                'xtra.co.nz', 'clear.net.nz'
            ],
            'africa': [
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'webmail.co.za', 'mweb.co.za', 'vodamail.co.za'
            ],
            'south_america': [
                # Brazil
                'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
                'uol.com.br', 'globo.com', 'terra.com.br', 'ig.com.br',
                'bol.com.br', 'zipmail.com.br', 'yahoo.com.br', 'hotmail.com.br',
                'gmail.com.br', 'r7.com', 'click21.com.br', 'pop.com.br',
                'superig.com.br', 'ibest.com.br', 'itelefonica.com.br',
                # Argentina
                'yahoo.com.ar', 'hotmail.com.ar', 'gmail.com.ar', 'ciudad.com.ar',
                'arnet.com.ar', 'speedy.com.ar', 'fibertel.com.ar',
                # Colombia
                'yahoo.com.co', 'hotmail.com.co', 'gmail.com.co', 'une.net.co',
                'etb.net.co', 'cable.net.co', 'emcali.net.co',
                # Chile
                'yahoo.cl', 'hotmail.cl', 'gmail.cl', 'vtr.net', 'tie.cl',
                # Other South American countries
                'yahoo.com.pe', 'hotmail.com.pe', 'yahoo.com.ve', 'cantv.net',
                'yahoo.com.uy', 'adinet.com.uy', 'yahoo.com.ec', 'ecua.net.ec'
            ],
            
            'caribbean': [
                # Jamaica
                'yahoo.com', 'gmail.com', 'hotmail.com', 'cwjamaica.com',
                'kasnet.com', 'infochan.com', 'jamaica-gleaner.com',
                # Trinidad & Tobago
                'yahoo.com', 'gmail.com', 'hotmail.com', 'tstt.net.tt',
                'wow.net', 'carib-link.net', 'opus.co.tt',
                # Barbados
                'yahoo.com', 'gmail.com', 'hotmail.com', 'caribsurf.com',
                'sunbeach.net', 'caribnet.net',
                # Dominican Republic
                'yahoo.com', 'gmail.com', 'hotmail.com', 'codetel.net.do',
                'tricom.net', 'verizon.com.do',
                # Puerto Rico
                'yahoo.com', 'gmail.com', 'hotmail.com', 'prtc.net',
                'coqui.net', 'caribe.net',
                # Other Caribbean
                'yahoo.com', 'gmail.com', 'hotmail.com', 'candw.ag',
                'candw.lc', 'candw.vc', 'cwbahamas.bs', 'batelnet.bs'
            ]
        }
        
        self.business_domains = [
            'company.com', 'corp.com', 'inc.com', 'ltd.com', 'llc.com',
            'business.com', 'enterprise.com', 'org.com', 'group.com',
            'solutions.com', 'services.com', 'consulting.com', 'agency.com',
            'firm.com', 'partners.com', 'associates.com', 'holdings.com',
            'ventures.com', 'capital.com', 'investments.com', 'management.com',
            'technologies.com', 'systems.com', 'networks.com', 'digital.com',
            'global.com', 'international.com', 'worldwide.com', 'industries.com'
        ]
        
        self.separators = ['', '.', '_', '-']
        self.number_patterns = [
            '', '1', '2', '3', '4', '5', '01', '02', '03', '04', '05',
            '10', '11', '12', '21', '22', '23', '99', '100', '123', '321',
            # Years
            '2020', '2021', '2022', '2023', '2024', '2025',
            # Birth years (common ranges)
            '85', '86', '87', '88', '89', '90', '91', '92', '93', '94', '95',
            '96', '97', '98', '99', '00', '01', '02', '03', '04', '05'
        ]
        
    def generate_email_variations(self, name: str, region: str = 'global', 
                                count: int = 20000, include_business: bool = True) -> List[str]:
        """
        Generate email variations based on name and region
        
        Args:
            name: Base name for email generation
            region: Geographic region for domain selection
            count: Maximum number of emails to generate
            include_business: Include business domain variations
            
        Returns:
            List of generated email addresses
        """
        emails = set()
        name_clean = name.lower().strip().replace(' ', '')
        
        # Get domains for the region
        domains = self.regional_domains.get(region, self.regional_domains['global'])
        if include_business:
            domains.extend(self.business_domains)
            
        # Generate name variations
        name_variations = self._generate_name_variations(name_clean)
        
        logger.info(f"Generating emails for '{name}' in region '{region}'")
        
        # Generate combinations
        for name_var in name_variations:
            for domain in domains:
                email = f"{name_var}@{domain}"
                emails.add(email)
                
                if len(emails) >= count:
                    break
            if len(emails) >= count:
                break
                
        result = list(emails)[:count]
        logger.info(f"Generated {len(result)} email variations")
        return result
    
    def _generate_name_variations(self, name: str) -> List[str]:
        """
        Generate different variations of a name
        """
        variations = set()
        
        # Base name
        variations.add(name)
        
        # With separators and numbers
        for sep in self.separators:
            for num in self.number_patterns:
                if sep == '':
                    variations.add(f"{name}{num}")
                else:
                    variations.add(f"{name}{sep}{num}")
                    if num:
                        variations.add(f"{num}{sep}{name}")
        
        # First letter + numbers
        if name:
            first_letter = name[0]
            for num in ['1', '12', '123', '2023', '2024']:
                variations.add(f"{first_letter}{num}")
                variations.add(f"{first_letter}.{num}")
                variations.add(f"{first_letter}_{num}")
        
        # Name with common additions
        common_additions = [
            # Professional prefixes
            'sales', 'support', 'admin', 'office', 'contact', 'info', 'help',
            'service', 'team', 'manager', 'director', 'ceo', 'cto', 'cfo',
            'marketing', 'hr', 'finance', 'tech', 'dev', 'design', 'ops',
            # Random realistic additions
            'pro', 'biz', 'work', 'mail', 'email', 'me', 'my', 'the', 'new',
            'official', 'real', 'true', 'best', 'top', 'main', 'primary'
        ]
        for addition in common_additions:
            for sep in ['.', '_', '-']:
                variations.add(f"{name}{sep}{addition}")
                variations.add(f"{addition}{sep}{name}")
        
        # Abbreviated versions
        if len(name) > 3:
            variations.add(name[:3])  # First 3 letters
            variations.add(name[:4])  # First 4 letters
            
        # Double name (common pattern)
        variations.add(f"{name}{name}")
        variations.add(f"{name}.{name}")
        
        return list(variations)
    
    def generate_bulk_emails(self, names: List[str], regions: List[str] = None, 
                           emails_per_name: int = 1000) -> Dict[str, List[str]]:
        """
        Generate emails for multiple names across multiple regions
        
        Args:
            names: List of names to generate emails for
            regions: List of regions to use (default: all regions)
            emails_per_name: Number of emails to generate per name
            
        Returns:
            Dictionary mapping names to their generated emails
        """
        if regions is None:
            regions = list(self.regional_domains.keys())
            
        results = {}
        
        for name in names:
            all_emails = set()
            emails_per_region = emails_per_name // len(regions)
            
            for region in regions:
                region_emails = self.generate_email_variations(
                    name, region, emails_per_region, include_business=True
                )
                all_emails.update(region_emails)
                
            results[name] = list(all_emails)[:emails_per_name]
            
        return results
    
    def save_generated_emails(self, emails: Dict[str, List[str]], filename: str):
        """
        Save generated emails to file
        """
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(emails, f, indent=2, ensure_ascii=False)
        logger.info(f"Saved generated emails to {filename}")
    
    def export_to_csv(self, emails: Dict[str, List[str]], filename: str):
        """
        Export generated emails to CSV format
        """
        import csv
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Name', 'Email', 'Domain', 'Region'])
            
            for name, email_list in emails.items():
                for email in email_list:
                    domain = email.split('@')[1]
                    region = self._get_domain_region(domain)
                    writer.writerow([name, email, domain, region])
                    
        logger.info(f"Exported emails to CSV: {filename}")
    
    def _get_domain_region(self, domain: str) -> str:
        """
        Determine the region of a domain
        """
        for region, domains in self.regional_domains.items():
            if domain in domains:
                return region
        return 'unknown'

# Example usage
if __name__ == "__main__":
    generator = EmailGenerator()
    
    # Generate emails for a single name
    emails = generator.generate_email_variations("anna", "global", 100)
    print(f"Generated {len(emails)} emails for 'anna'")
    print("Sample emails:", emails[:10])
    
    # Generate for multiple names
    names = ["john", "mary", "david", "sarah"]
    bulk_emails = generator.generate_bulk_emails(names, emails_per_name=50)
    
    for name, email_list in bulk_emails.items():
        print(f"\n{name}: {len(email_list)} emails")
        print("Sample:", email_list[:5])