#!/usr/bin/env python3
"""
Email Scraper Pro - Utility Functions
Helper functions for validation, cleaning, and data processing
"""

import re
import hashlib
import urllib.parse
from typing import List, Set, Optional, Dict, Any
from datetime import datetime, timedelta
import phonenumbers
from phonenumbers import NumberParseException
from loguru import logger
import tldextract
import requests
from urllib.parse import urljoin, urlparse
import time
import random

class EmailValidator:
    """Email validation utilities"""
    
    # Comprehensive email regex pattern
    EMAIL_PATTERN = re.compile(
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
    )
    
    # More strict email pattern
    STRICT_EMAIL_PATTERN = re.compile(
        r'^[a-zA-Z0-9.!#$%&\'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    )
    
    # Common disposable email domains
    DISPOSABLE_DOMAINS = {
        '10minutemail.com', 'tempmail.org', 'guerrillamail.com',
        'mailinator.com', 'yopmail.com', 'temp-mail.org',
        'throwaway.email', 'getnada.com', 'maildrop.cc'
    }
    
    # Common invalid/test domains
    INVALID_DOMAINS = {
        'example.com', 'test.com', 'localhost', 'invalid.com',
        'noreply.com', 'donotreply.com', 'no-reply.com'
    }
    
    @classmethod
    def extract_emails(cls, text: str) -> List[str]:
        """Extract all email addresses from text"""
        if not text:
            return []
        
        emails = cls.EMAIL_PATTERN.findall(text)
        return list(set(emails))  # Remove duplicates
    
    @classmethod
    def is_valid_email(cls, email: str, strict: bool = False) -> bool:
        """Validate email address"""
        if not email or not isinstance(email, str):
            return False
        
        email = email.strip().lower()
        
        # Check length
        if len(email) < 5 or len(email) > 254:
            return False
        
        # Check pattern
        pattern = cls.STRICT_EMAIL_PATTERN if strict else cls.EMAIL_PATTERN
        if not pattern.match(email):
            return False
        
        # Check domain
        domain = email.split('@')[1]
        
        # Check for invalid domains
        if domain in cls.INVALID_DOMAINS:
            return False
        
        # Check for disposable domains (optional)
        if strict and domain in cls.DISPOSABLE_DOMAINS:
            return False
        
        return True
    
    @classmethod
    def clean_email(cls, email: str) -> Optional[str]:
        """Clean and normalize email address"""
        if not email:
            return None
        
        email = email.strip().lower()
        
        # Remove common prefixes/suffixes
        email = re.sub(r'^mailto:', '', email)
        email = re.sub(r'[<>"\']', '', email)
        
        # Validate cleaned email
        if cls.is_valid_email(email):
            return email
        
        return None
    
    @classmethod
    def get_domain(cls, email: str) -> Optional[str]:
        """Extract domain from email"""
        if not cls.is_valid_email(email):
            return None
        
        return email.split('@')[1].lower()
    
    @classmethod
    def is_business_email(cls, email: str) -> bool:
        """Check if email appears to be a business email"""
        if not cls.is_valid_email(email):
            return False
        
        domain = cls.get_domain(email)
        if not domain:
            return False
        
        # Common personal email providers
        personal_domains = {
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'aol.com', 'icloud.com', 'live.com', 'msn.com',
            'ymail.com', 'rocketmail.com'
        }
        
        return domain not in personal_domains

class PhoneValidator:
    """Phone number validation utilities"""
    
    # Phone number patterns
    PHONE_PATTERNS = [
        re.compile(r'\+?1?[-\s\.]?\(?([0-9]{3})\)?[-\s\.]?([0-9]{3})[-\s\.]?([0-9]{4})'),  # US format
        re.compile(r'\+?([0-9]{1,4})[-\s\.]?\(?([0-9]{1,4})\)?[-\s\.]?([0-9]{1,4})[-\s\.]?([0-9]{1,4})'),  # International
        re.compile(r'\b\d{10,15}\b'),  # Simple digit sequence
    ]
    
    @classmethod
    def extract_phones(cls, text: str) -> List[str]:
        """Extract phone numbers from text"""
        if not text:
            return []
        
        phones = set()
        
        for pattern in cls.PHONE_PATTERNS:
            matches = pattern.findall(text)
            for match in matches:
                if isinstance(match, tuple):
                    phone = ''.join(match)
                else:
                    phone = match
                
                # Clean phone number
                phone = re.sub(r'[^0-9+]', '', phone)
                
                # Basic validation
                if 10 <= len(phone) <= 15:
                    phones.add(phone)
        
        return list(phones)
    
    @classmethod
    def is_valid_phone(cls, phone: str, country_code: str = 'US') -> bool:
        """Validate phone number using phonenumbers library"""
        try:
            parsed = phonenumbers.parse(phone, country_code)
            return phonenumbers.is_valid_number(parsed)
        except NumberParseException:
            return False
    
    @classmethod
    def format_phone(cls, phone: str, country_code: str = 'US') -> Optional[str]:
        """Format phone number"""
        try:
            parsed = phonenumbers.parse(phone, country_code)
            if phonenumbers.is_valid_number(parsed):
                return phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL)
        except NumberParseException:
            pass
        
        return None

class URLValidator:
    """URL validation and processing utilities"""
    
    @classmethod
    def is_valid_url(cls, url: str) -> bool:
        """Check if URL is valid"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @classmethod
    def normalize_url(cls, url: str) -> Optional[str]:
        """Normalize URL"""
        if not url:
            return None
        
        url = url.strip()
        
        # Add protocol if missing
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        # Validate
        if cls.is_valid_url(url):
            return url
        
        return None
    
    @classmethod
    def extract_domain(cls, url: str) -> Optional[str]:
        """Extract domain from URL"""
        try:
            extracted = tldextract.extract(url)
            if extracted.domain and extracted.suffix:
                return f"{extracted.domain}.{extracted.suffix}"
        except Exception:
            pass
        
        return None
    
    @classmethod
    def is_linkedin_url(cls, url: str) -> bool:
        """Check if URL is a LinkedIn profile"""
        if not url:
            return False
        
        return 'linkedin.com/in/' in url.lower()
    
    @classmethod
    def extract_linkedin_urls(cls, text: str) -> List[str]:
        """Extract LinkedIn profile URLs from text"""
        if not text:
            return []
        
        # LinkedIn URL pattern
        pattern = re.compile(
            r'https?://(?:www\.)?linkedin\.com/in/[a-zA-Z0-9-]+/?',
            re.IGNORECASE
        )
        
        urls = pattern.findall(text)
        return list(set(urls))

class TextCleaner:
    """Text cleaning and processing utilities"""
    
    @classmethod
    def clean_text(cls, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ''
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s@.,-]', '', text)
        
        return text.strip()
    
    @classmethod
    def extract_names(cls, text: str) -> List[str]:
        """Extract potential names from text"""
        if not text:
            return []
        
        # Simple name pattern (2-3 capitalized words)
        pattern = re.compile(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+){1,2}\b')
        names = pattern.findall(text)
        
        # Filter out common non-names
        filtered_names = []
        common_words = {
            'About Us', 'Contact Us', 'Privacy Policy', 'Terms Service',
            'Home Page', 'Sign Up', 'Log In', 'Get Started'
        }
        
        for name in names:
            if name not in common_words and len(name.split()) <= 3:
                filtered_names.append(name)
        
        return list(set(filtered_names))
    
    @classmethod
    def extract_companies(cls, text: str) -> List[str]:
        """Extract potential company names from text"""
        if not text:
            return []
        
        companies = []
        
        # Look for common company suffixes
        company_patterns = [
            re.compile(r'\b[A-Z][\w\s&]+(?:Inc|LLC|Corp|Ltd|Co|Company|Corporation|Limited)\b', re.IGNORECASE),
            re.compile(r'\b[A-Z][\w\s&]{2,30}\s+(?:Inc|LLC|Corp|Ltd|Co)\b', re.IGNORECASE)
        ]
        
        for pattern in company_patterns:
            matches = pattern.findall(text)
            companies.extend(matches)
        
        return list(set(companies))

class DataDeduplicator:
    """Data deduplication utilities"""
    
    @classmethod
    def generate_hash(cls, email: str, name: str = '', company: str = '') -> str:
        """Generate hash for contact deduplication"""
        # Normalize inputs
        email = email.lower().strip() if email else ''
        name = name.lower().strip() if name else ''
        company = company.lower().strip() if company else ''
        
        # Create hash from email (primary key)
        hash_input = email
        
        return hashlib.md5(hash_input.encode()).hexdigest()
    
    @classmethod
    def are_similar_contacts(cls, contact1: Dict, contact2: Dict, threshold: float = 0.8) -> bool:
        """Check if two contacts are similar"""
        # Email exact match
        if contact1.get('email', '').lower() == contact2.get('email', '').lower():
            return True
        
        # Name and company similarity (if available)
        name1 = contact1.get('name', '').lower()
        name2 = contact2.get('name', '').lower()
        company1 = contact1.get('company', '').lower()
        company2 = contact2.get('company', '').lower()
        
        if name1 and name2 and company1 and company2:
            name_sim = cls.string_similarity(name1, name2)
            company_sim = cls.string_similarity(company1, company2)
            
            if name_sim > threshold and company_sim > threshold:
                return True
        
        return False
    
    @classmethod
    def string_similarity(cls, s1: str, s2: str) -> float:
        """Calculate string similarity using Jaccard index"""
        if not s1 or not s2:
            return 0.0
        
        set1 = set(s1.lower().split())
        set2 = set(s2.lower().split())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0

class RateLimiter:
    """Rate limiting utilities"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
    
    def can_make_request(self) -> bool:
        """Check if request can be made"""
        now = time.time()
        
        # Remove old requests
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < self.time_window]
        
        return len(self.requests) < self.max_requests
    
    def make_request(self):
        """Record a request"""
        if self.can_make_request():
            self.requests.append(time.time())
            return True
        return False
    
    def wait_time(self) -> float:
        """Get time to wait before next request"""
        if not self.requests:
            return 0.0
        
        oldest_request = min(self.requests)
        wait_time = self.time_window - (time.time() - oldest_request)
        
        return max(0.0, wait_time)

class ProxyRotator:
    """Proxy rotation utilities"""
    
    def __init__(self, proxies: List[Dict]):
        self.proxies = proxies
        self.current_index = 0
        self.failed_proxies = set()
    
    def get_next_proxy(self) -> Optional[Dict]:
        """Get next working proxy"""
        if not self.proxies:
            return None
        
        attempts = 0
        while attempts < len(self.proxies):
            proxy = self.proxies[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.proxies)
            
            proxy_key = f"{proxy.get('host')}:{proxy.get('port')}"
            if proxy_key not in self.failed_proxies:
                return proxy
            
            attempts += 1
        
        return None
    
    def mark_proxy_failed(self, proxy: Dict):
        """Mark proxy as failed"""
        proxy_key = f"{proxy.get('host')}:{proxy.get('port')}"
        self.failed_proxies.add(proxy_key)
        logger.warning(f"Marked proxy as failed: {proxy_key}")
    
    def reset_failed_proxies(self):
        """Reset failed proxies list"""
        self.failed_proxies.clear()
        logger.info("Reset failed proxies list")

class UserAgentRotator:
    """User agent rotation utilities"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0'
        ]
        self.current_index = 0
    
    def get_random_user_agent(self) -> str:
        """Get random user agent"""
        return random.choice(self.user_agents)
    
    def get_next_user_agent(self) -> str:
        """Get next user agent in rotation"""
        user_agent = self.user_agents[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.user_agents)
        return user_agent

class PerformanceMonitor:
    """Performance monitoring utilities"""
    
    def __init__(self):
        self.start_time = None
        self.metrics = {}
    
    def start_timer(self, name: str):
        """Start timing an operation"""
        self.metrics[name] = {'start': time.time()}
    
    def end_timer(self, name: str):
        """End timing an operation"""
        if name in self.metrics and 'start' in self.metrics[name]:
            self.metrics[name]['duration'] = time.time() - self.metrics[name]['start']
            return self.metrics[name]['duration']
        return 0
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all metrics"""
        return self.metrics
    
    def log_metrics(self):
        """Log performance metrics"""
        for name, data in self.metrics.items():
            if 'duration' in data:
                logger.info(f"Performance - {name}: {data['duration']:.2f}s")

# Utility functions
def sleep_random(min_seconds: float = 1.0, max_seconds: float = 3.0):
    """Sleep for a random duration"""
    duration = random.uniform(min_seconds, max_seconds)
    time.sleep(duration)

def format_timestamp(dt: datetime = None) -> str:
    """Format timestamp for display"""
    if dt is None:
        dt = datetime.now()
    return dt.strftime('%Y-%m-%d %H:%M:%S')

def parse_timestamp(timestamp_str: str) -> Optional[datetime]:
    """Parse timestamp string"""
    try:
        return datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        return None

def is_recent(timestamp_str: str, hours: int = 24) -> bool:
    """Check if timestamp is recent"""
    dt = parse_timestamp(timestamp_str)
    if not dt:
        return False
    
    return datetime.now() - dt < timedelta(hours=hours)

def truncate_string(text: str, max_length: int = 100) -> str:
    """Truncate string with ellipsis"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length-3] + '...'

def safe_filename(filename: str) -> str:
    """Create safe filename by removing invalid characters"""
    # Remove invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing dots and spaces
    filename = filename.strip('. ')
    
    # Ensure not empty
    if not filename:
        filename = 'untitled'
    
    return filename

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return '0 B'
    
    size_names = ['B', 'KB', 'MB', 'GB', 'TB']
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f'{size_bytes:.1f} {size_names[i]}'