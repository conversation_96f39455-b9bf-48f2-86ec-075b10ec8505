#!/usr/bin/env python3
"""
Test script for MailHop SMTP connection
This script tests the MailHop SMTP configuration
"""

import sys
import os
from loguru import logger

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smtp_config import SMTPManager

def test_mailhop_connection():
    """Test MailHop SMTP connection with detailed debugging"""
    try:
        # Initialize SMTP manager
        smtp_manager = SMTPManager()
        
        # Get the active configuration
        active_config = smtp_manager.get_active_config()
        if not active_config:
            logger.error("No active SMTP configuration found")
            return False
            
        logger.info(f"Testing SMTP configuration: {smtp_manager.active_config}")
        logger.info(f"Server: {active_config.smtp_server}")
        logger.info(f"Port: {active_config.smtp_port}")
        logger.info(f"Username: {active_config.username}")
        logger.info(f"Use SSL: {active_config.use_ssl}")
        logger.info(f"Use TLS: {active_config.use_tls}")
        
        # Test connection
        logger.info("Testing SMTP connection...")
        success = smtp_manager.test_connection()
        
        if success:
            logger.success("✅ SMTP connection successful!")
            return True
        else:
            logger.error("❌ SMTP connection failed")
            return False
            
    except Exception as e:
        logger.error(f"Error testing SMTP connection: {e}")
        return False

def send_test_email():
    """Send a test email"""
    try:
        smtp_manager = SMTPManager()
        
        # Test email details
        to_email = "<EMAIL>"  # Replace with actual test email
        subject = "MailHop SMTP Test"
        body = "This is a test email from the MailHop SMTP configuration."
        
        logger.info(f"Sending test email to: {to_email}")
        
        success = smtp_manager.send_email(
            to_emails=[to_email],
            subject=subject,
            body=body
        )
        
        if success:
            logger.success("✅ Test email sent successfully!")
            return True
        else:
            logger.error("❌ Failed to send test email")
            return False
            
    except Exception as e:
        logger.error(f"Error sending test email: {e}")
        return False

if __name__ == "__main__":
    logger.info("MailHop SMTP Connection Test")
    logger.info("=" * 40)
    
    # Test connection
    if test_mailhop_connection():
        logger.info("SMTP connection test passed!")
        
        # Uncomment the line below to send a test email
        # send_test_email()
        
    else:
        logger.error("SMTP connection test failed!")
        logger.info("Please verify the credentials and server settings.")
