@echo off
title BMD EMAIL SCRAPPER - Simple Launcher
color 0A
echo.
echo ========================================
echo  BMD EMAIL SCRAPPER - Simple Launcher
echo ========================================
echo.
echo Checking system compatibility...
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python and try again.
    echo.
    pause
    exit /b 1
)
echo ✓ Python found

:: Check if tkinter is available
python -c "import tkinter" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: tkinter module not found
    echo Please ensure Python was installed with tkinter support.
    echo.
    pause
    exit /b 1
)
echo ✓ tkinter available

:: Check if bulk_gui.py exists
if not exist "bulk_gui.py" (
    echo ERROR: bulk_gui.py not found in current directory
    echo Please ensure you're running this from the correct folder.
    echo.
    pause
    exit /b 1
)
echo ✓ Application files found

echo.
echo Starting BMD EMAIL SCRAPPER...
echo Please wait while the application loads...
echo.
echo If the application doesn't appear:
echo 1. Check your taskbar for the application window
echo 2. Try Alt+Tab to switch between windows
echo 3. The consent screen should appear first
echo.

:: Launch with maximum visibility
python -c "import tkinter as tk; import sys; sys.stdout.flush(); root = tk.Tk(); root.withdraw(); print('Loading GUI...'); sys.stdout.flush(); import bulk_gui; print('Creating application...'); sys.stdout.flush(); root.deiconify(); root.state('zoomed'); root.lift(); root.focus_force(); root.attributes('-topmost', True); root.after(3000, lambda: root.attributes('-topmost', False)); print('Starting application...'); sys.stdout.flush(); app = bulk_gui.BulkScraperGUI(root); root.mainloop(); print('Application closed.')" 2>launch_error.log

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ Application closed normally.
    if exist launch_error.log del launch_error.log
) else (
    echo.
    echo ✗ Application encountered an error.
    echo.
    if exist launch_error.log (
        echo Error details:
        type launch_error.log
        echo.
    )
    echo Possible solutions:
    echo 1. Run as Administrator
    echo 2. Add folder to Windows Security exclusions
    echo 3. Temporarily disable antivirus
    echo 4. Check if Python dependencies are installed
    echo.
)

echo Press any key to exit...
pause >nul