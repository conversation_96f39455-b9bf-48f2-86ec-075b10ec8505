{% extends "base.html" %}

{% block title %}Template Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1><i class="fas fa-file-alt"></i> Template Management</h1>
                <p class="text-muted">Create and manage your email templates</p>
            </div>
        </div>
    </div>

    <!-- Template Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stats-content">
                    <h3 id="total-templates">0</h3>
                    <p>Total Templates</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-success">
                    <i class="fas fa-check"></i>
                </div>
                <div class="stats-content">
                    <h3 id="active-templates">0</h3>
                    <p>Active Templates</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-info">
                    <i class="fas fa-paper-plane"></i>
                </div>
                <div class="stats-content">
                    <h3 id="templates-used">0</h3>
                    <p>Templates Used</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-content">
                    <h3 id="recent-templates">0</h3>
                    <p>Recent Templates</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Creation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-plus"></i> Create New Template</h5>
                </div>
                <div class="card-body">
                    <form id="template-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="template-name">Template Name</label>
                                    <input type="text" class="form-control" id="template-name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="template-category">Category</label>
                                    <select class="form-control" id="template-category">
                                        <option value="marketing">Marketing</option>
                                        <option value="sales">Sales</option>
                                        <option value="follow-up">Follow-up</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="announcement">Announcement</option>
                                        <option value="other">Other</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="template-subject">Subject Line</label>
                            <input type="text" class="form-control" id="template-subject" required>
                            <small class="form-text text-muted">Use variables like {{name}}, {{company}}, {{title}} for personalization</small>
                        </div>
                        <div class="form-group">
                            <label for="template-content">Email Content</label>
                            <textarea class="form-control" id="template-content" rows="10" required></textarea>
                            <small class="form-text text-muted">HTML and plain text supported. Use variables for personalization.</small>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="template-html">
                                        <label class="form-check-label" for="template-html">
                                            HTML Template
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="template-active" checked>
                                        <label class="form-check-label" for="template-active">
                                            Active Template
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Template
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="previewTemplate()">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                        <button type="button" class="btn btn-outline-secondary ml-2" onclick="resetForm()">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Template List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list"></i> Email Templates</h5>
                    <div>
                        <select class="form-control form-control-sm d-inline-block" id="category-filter" style="width: auto;">
                            <option value="">All Categories</option>
                            <option value="marketing">Marketing</option>
                            <option value="sales">Sales</option>
                            <option value="follow-up">Follow-up</option>
                            <option value="newsletter">Newsletter</option>
                            <option value="announcement">Announcement</option>
                            <option value="other">Other</option>
                        </select>
                        <button class="btn btn-outline-primary btn-sm ml-2" onclick="refreshTemplates()">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="templates-loading" class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading templates...</span>
                        </div>
                        <p class="mt-2">Loading templates...</p>
                    </div>
                    <div id="templates-grid" class="row d-none">
                        <!-- Templates will be loaded here -->
                    </div>
                    <div id="no-templates" class="text-center py-4 d-none">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No templates found</h5>
                        <p class="text-muted">Create your first template to get started</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="preview-content">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Template Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Template</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="edit-template-form">
                    <input type="hidden" id="edit-template-id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit-template-name">Template Name</label>
                                <input type="text" class="form-control" id="edit-template-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="edit-template-category">Category</label>
                                <select class="form-control" id="edit-template-category">
                                    <option value="marketing">Marketing</option>
                                    <option value="sales">Sales</option>
                                    <option value="follow-up">Follow-up</option>
                                    <option value="newsletter">Newsletter</option>
                                    <option value="announcement">Announcement</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="edit-template-subject">Subject Line</label>
                        <input type="text" class="form-control" id="edit-template-subject" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-template-content">Email Content</label>
                        <textarea class="form-control" id="edit-template-content" rows="10" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateTemplate()">
                    <i class="fas fa-save"></i> Update Template
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let templates = [];
let currentEditingTemplate = null;

// Initialize page
$(document).ready(function() {
    loadTemplates();
    
    // Form submission
    $('#template-form').on('submit', function(e) {
        e.preventDefault();
        createTemplate();
    });
    
    // Category filter
    $('#category-filter').on('change', function() {
        filterTemplates();
    });
});

// Load templates
function loadTemplates() {
    $('#templates-loading').removeClass('d-none');
    $('#templates-grid').addClass('d-none');
    $('#no-templates').addClass('d-none');
    
    $.get('/api/templates')
        .done(function(response) {
            templates = response.templates;
            displayTemplates();
            updateStats();
        })
        .fail(function() {
            showAlert('Failed to load templates', 'danger');
        })
        .always(function() {
            $('#templates-loading').addClass('d-none');
        });
}

// Display templates
function displayTemplates(filteredTemplates = null) {
    const templatesToShow = filteredTemplates || templates;
    
    if (templatesToShow.length === 0) {
        $('#no-templates').removeClass('d-none');
        return;
    }
    
    const container = $('#templates-grid');
    container.empty();
    
    templatesToShow.forEach(template => {
        const templateCard = `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="template-card card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${template.name}</h6>
                        <span class="badge badge-primary">${template.category || 'other'}</span>
                    </div>
                    <div class="card-body">
                        <h6 class="card-subtitle mb-2 text-muted">Subject: ${template.subject}</h6>
                        <p class="card-text">${truncateText(template.content, 100)}</p>
                        <small class="text-muted">
                            Created: ${template.created_at ? new Date(template.created_at).toLocaleDateString() : 'Unknown'}
                        </small>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group btn-group-sm w-100" role="group">
                            <button class="btn btn-outline-primary" onclick="viewTemplate('${template.id}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                            <button class="btn btn-outline-secondary" onclick="editTemplate('${template.id}')">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-outline-success" onclick="duplicateTemplate('${template.id}')">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteTemplate('${template.id}')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.append(templateCard);
    });
    
    container.removeClass('d-none');
}

// Filter templates by category
function filterTemplates() {
    const category = $('#category-filter').val();
    
    if (!category) {
        displayTemplates();
        return;
    }
    
    const filtered = templates.filter(template => 
        (template.category || 'other') === category
    );
    
    displayTemplates(filtered);
}

// Update statistics
function updateStats() {
    const totalTemplates = templates.length;
    const activeTemplates = templates.filter(t => t.active !== false).length;
    const recentTemplates = templates.filter(t => {
        if (!t.created_at) return false;
        const created = new Date(t.created_at);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return created > weekAgo;
    }).length;
    
    $('#total-templates').text(totalTemplates);
    $('#active-templates').text(activeTemplates);
    $('#templates-used').text('N/A'); // Would need campaign data
    $('#recent-templates').text(recentTemplates);
}

// Create new template
function createTemplate() {
    const templateData = {
        name: $('#template-name').val(),
        category: $('#template-category').val(),
        subject: $('#template-subject').val(),
        content: $('#template-content').val(),
        is_html: $('#template-html').is(':checked'),
        active: $('#template-active').is(':checked')
    };
    
    $.ajax({
        url: '/api/templates',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(templateData),
        success: function(response) {
            showAlert('Template created successfully!', 'success');
            resetForm();
            loadTemplates();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to create template';
            showAlert(error, 'danger');
        }
    });
}

// View template
function viewTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;
    
    const previewContent = `
        <div class="template-preview">
            <h5>Subject: ${template.subject}</h5>
            <hr>
            <div class="template-content">
                ${template.is_html ? template.content : `<pre>${template.content}</pre>`}
            </div>
        </div>
    `;
    
    $('#preview-content').html(previewContent);
    $('#previewModal').modal('show');
}

// Edit template
function editTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;
    
    currentEditingTemplate = template;
    
    $('#edit-template-id').val(template.id);
    $('#edit-template-name').val(template.name);
    $('#edit-template-category').val(template.category || 'other');
    $('#edit-template-subject').val(template.subject);
    $('#edit-template-content').val(template.content);
    
    $('#editModal').modal('show');
}

// Update template
function updateTemplate() {
    const templateId = $('#edit-template-id').val();
    const templateData = {
        name: $('#edit-template-name').val(),
        category: $('#edit-template-category').val(),
        subject: $('#edit-template-subject').val(),
        content: $('#edit-template-content').val()
    };
    
    $.ajax({
        url: `/api/templates/${templateId}`,
        type: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(templateData),
        success: function(response) {
            showAlert('Template updated successfully!', 'success');
            $('#editModal').modal('hide');
            loadTemplates();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to update template';
            showAlert(error, 'danger');
        }
    });
}

// Duplicate template
function duplicateTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;
    
    const templateData = {
        name: template.name + ' (Copy)',
        category: template.category,
        subject: template.subject,
        content: template.content,
        is_html: template.is_html,
        active: true
    };
    
    $.ajax({
        url: '/api/templates',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(templateData),
        success: function(response) {
            showAlert('Template duplicated successfully!', 'success');
            loadTemplates();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to duplicate template';
            showAlert(error, 'danger');
        }
    });
}

// Delete template
function deleteTemplate(templateId) {
    const template = templates.find(t => t.id === templateId);
    if (!template) return;
    
    if (!confirm(`Are you sure you want to delete the template "${template.name}"?`)) {
        return;
    }
    
    $.ajax({
        url: `/api/templates/${templateId}`,
        type: 'DELETE',
        success: function(response) {
            showAlert('Template deleted successfully!', 'success');
            loadTemplates();
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || 'Failed to delete template';
            showAlert(error, 'danger');
        }
    });
}

// Preview template
function previewTemplate() {
    const subject = $('#template-subject').val();
    const content = $('#template-content').val();
    const isHtml = $('#template-html').is(':checked');
    
    if (!subject || !content) {
        showAlert('Please fill in subject and content to preview', 'warning');
        return;
    }
    
    const previewContent = `
        <div class="template-preview">
            <h5>Subject: ${subject}</h5>
            <hr>
            <div class="template-content">
                ${isHtml ? content : `<pre>${content}</pre>`}
            </div>
        </div>
    `;
    
    $('#preview-content').html(previewContent);
    $('#previewModal').modal('show');
}

// Reset form
function resetForm() {
    $('#template-form')[0].reset();
    $('#template-active').prop('checked', true);
}

// Refresh templates
function refreshTemplates() {
    loadTemplates();
}

// Utility function to truncate text
function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Show alert
function showAlert(message, type) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    $('.container-fluid').prepend(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}