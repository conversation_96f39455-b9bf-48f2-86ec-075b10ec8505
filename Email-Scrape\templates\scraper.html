{% extends "base.html" %}

{% block title %}Em<PERSON> Scraper - Email Scraper Pro{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    Email Scraper
                </h4>
            </div>
            <div class="card-body">
                <p class="text-muted mb-4">
                    Extract email addresses from websites and domains. Enter URLs or domains to scrape for email addresses.
                </p>
                
                <!-- Scraping Form -->
                <form id="scraping-form">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="urls" class="form-label">URLs to Scrape</label>
                            <textarea class="form-control" id="urls" rows="4" 
                                placeholder="Enter URLs, one per line:
https://example.com
https://company.com/contact
https://business.org/about"></textarea>
                            <div class="form-text">Enter one URL per line. Include http:// or https://</div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="search-terms" class="form-label">Search Terms (Optional)</label>
                            <textarea class="form-control" id="search-terms" rows="4" 
                                placeholder="contact
support
info
sales
admin"></textarea>
                            <div class="form-text">Keywords to focus the search</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max-depth" class="form-label">Crawl Depth</label>
                            <select class="form-select" id="max-depth">
                                <option value="1">1 - Current page only</option>
                                <option value="2" selected>2 - Include linked pages</option>
                                <option value="3">3 - Deep crawl</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="delay" class="form-label">Delay Between Requests (seconds)</label>
                            <input type="number" class="form-control" id="delay" value="1" min="0" max="10" step="0.5">
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="start-scraping">
                            <i class="fas fa-play me-2"></i>
                            Start Scraping
                        </button>
                        <button type="button" class="btn btn-danger d-none" id="stop-scraping">
                            <i class="fas fa-stop me-2"></i>
                            Stop Scraping
                        </button>
                        <button type="button" class="btn btn-secondary" id="clear-form">
                            <i class="fas fa-trash me-2"></i>
                            Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Progress Section -->
<div class="row" id="progress-section" style="display: none;">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    Scraping Progress
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Overall Progress</span>
                        <span id="progress-text">0 / 0</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" id="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Current Site:</strong></p>
                        <p class="text-muted" id="current-site">-</p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Status:</strong></p>
                        <p id="scraping-status" class="text-info">Initializing...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="row">
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-envelope me-2"></i>
                    Scraped Emails (<span id="email-count">0</span>)
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" id="export-emails">
                        <i class="fas fa-download me-1"></i>
                        Export
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" id="clear-results">
                        <i class="fas fa-trash me-1"></i>
                        Clear
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="email-results">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <p>No emails scraped yet. Start scraping to see results here.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Statistics
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-0" id="unique-emails">0</h4>
                            <small class="text-muted">Unique Emails</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-0" id="domains-found">0</h4>
                        <small class="text-muted">Domains</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-info mb-0" id="pages-scraped">0</h4>
                            <small class="text-muted">Pages Scraped</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning mb-0" id="errors-count">0</h4>
                        <small class="text-muted">Errors</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>Top Domains</h6>
                    <div id="top-domains">
                        <small class="text-muted">No data available</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Email Generator -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-magic me-2"></i>
                    Email Generator
                </h5>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-3">
                    Generate potential email addresses from domains
                </p>
                
                <div class="mb-3">
                    <label for="generator-domains" class="form-label">Domains</label>
                    <textarea class="form-control" id="generator-domains" rows="3" 
                        placeholder="example.com
company.org
business.net"></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="email-patterns" class="form-label">Patterns</label>
                    <select class="form-select" id="email-patterns" multiple>
                        <option value="info" selected>info@domain</option>
                        <option value="contact" selected>contact@domain</option>
                        <option value="support">support@domain</option>
                        <option value="sales">sales@domain</option>
                        <option value="admin">admin@domain</option>
                    </select>
                </div>
                
                <button class="btn btn-success btn-sm w-100" id="generate-emails">
                    <i class="fas fa-cogs me-1"></i>
                    Generate Emails
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Errors Section -->
<div class="row" id="errors-section" style="display: none;">
    <div class="col-12">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Errors & Warnings
                </h5>
            </div>
            <div class="card-body">
                <div id="error-list"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let scrapingInProgress = false;
let scrapedEmails = new Set();
let domainStats = {};

document.addEventListener('DOMContentLoaded', function() {
    // Form event listeners
    document.getElementById('scraping-form').addEventListener('submit', startScraping);
    document.getElementById('stop-scraping').addEventListener('click', stopScraping);
    document.getElementById('clear-form').addEventListener('click', clearForm);
    document.getElementById('clear-results').addEventListener('click', clearResults);
    document.getElementById('export-emails').addEventListener('click', exportEmails);
    document.getElementById('generate-emails').addEventListener('click', generateEmails);
    
    // Socket.IO event listeners
    if (typeof socket !== 'undefined') {
        socket.on('scraping_progress', updateProgress);
        socket.on('scraping_complete', onScrapingComplete);
    }
});

function startScraping(e) {
    e.preventDefault();
    
    const urls = document.getElementById('urls').value.trim().split('\n').filter(url => url.trim());
    const searchTerms = document.getElementById('search-terms').value.trim().split('\n').filter(term => term.trim());
    
    if (urls.length === 0) {
        showAlert('Please enter at least one URL to scrape.', 'warning');
        return;
    }
    
    scrapingInProgress = true;
    updateScrapingUI(true);
    
    fetch('/api/scrape', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            urls: urls,
            search_terms: searchTerms
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        showAlert('Scraping started successfully!', 'success');
    })
    .catch(error => {
        console.error('Error starting scraping:', error);
        showAlert('Error starting scraping: ' + error.message, 'danger');
        scrapingInProgress = false;
        updateScrapingUI(false);
    });
}

function stopScraping() {
    fetch('/api/scrape/stop', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        showAlert('Scraping stopped.', 'info');
        scrapingInProgress = false;
        updateScrapingUI(false);
    })
    .catch(error => {
        console.error('Error stopping scraping:', error);
    });
}

function updateProgress(data) {
    const progressSection = document.getElementById('progress-section');
    progressSection.style.display = 'block';
    
    const progress = data.total > 0 ? (data.progress / data.total) * 100 : 0;
    document.getElementById('progress-bar').style.width = progress + '%';
    document.getElementById('progress-text').textContent = `${data.progress} / ${data.total}`;
    document.getElementById('current-site').textContent = data.current_site || '-';
    document.getElementById('scraping-status').textContent = data.status;
    
    // Update results
    if (data.results && data.results.length > 0) {
        data.results.forEach(email => {
            if (!scrapedEmails.has(email)) {
                scrapedEmails.add(email);
                addEmailToResults(email);
            }
        });
        updateStatistics();
    }
    
    // Show errors if any
    if (data.errors && data.errors.length > 0) {
        showErrors(data.errors);
    }
}

function onScrapingComplete(data) {
    scrapingInProgress = false;
    updateScrapingUI(false);
    
    const status = data.status === 'completed' ? 'success' : 'warning';
    const message = data.status === 'completed' ? 
        `Scraping completed! Found ${scrapedEmails.size} unique emails.` :
        'Scraping finished with some issues.';
    
    showAlert(message, status);
}

function updateScrapingUI(isRunning) {
    document.getElementById('start-scraping').style.display = isRunning ? 'none' : 'inline-block';
    document.getElementById('stop-scraping').style.display = isRunning ? 'inline-block' : 'none';
    
    const formElements = document.querySelectorAll('#scraping-form input, #scraping-form textarea, #scraping-form select');
    formElements.forEach(element => {
        element.disabled = isRunning;
    });
}

function addEmailToResults(email) {
    const resultsDiv = document.getElementById('email-results');
    
    if (scrapedEmails.size === 1) {
        resultsDiv.innerHTML = '';
    }
    
    const emailDiv = document.createElement('div');
    emailDiv.className = 'border-bottom py-2';
    emailDiv.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <span class="font-monospace">${email}</span>
            <button class="btn btn-sm btn-outline-primary" onclick="copyEmail('${email}')">
                <i class="fas fa-copy"></i>
            </button>
        </div>
    `;
    
    resultsDiv.appendChild(emailDiv);
    
    // Update domain stats
    const domain = email.split('@')[1];
    if (domain) {
        domainStats[domain] = (domainStats[domain] || 0) + 1;
    }
}

function updateStatistics() {
    document.getElementById('email-count').textContent = scrapedEmails.size;
    document.getElementById('unique-emails').textContent = scrapedEmails.size;
    document.getElementById('domains-found').textContent = Object.keys(domainStats).length;
    
    // Update top domains
    const topDomains = Object.entries(domainStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    const topDomainsDiv = document.getElementById('top-domains');
    if (topDomains.length > 0) {
        topDomainsDiv.innerHTML = topDomains.map(([domain, count]) => 
            `<div class="d-flex justify-content-between"><small>${domain}</small><small class="text-muted">${count}</small></div>`
        ).join('');
    }
}

function clearForm() {
    document.getElementById('urls').value = '';
    document.getElementById('search-terms').value = '';
    document.getElementById('max-depth').value = '2';
    document.getElementById('delay').value = '1';
}

function clearResults() {
    scrapedEmails.clear();
    domainStats = {};
    document.getElementById('email-results').innerHTML = `
        <div class="text-center text-muted py-4">
            <i class="fas fa-inbox fa-3x mb-3"></i>
            <p>No emails scraped yet. Start scraping to see results here.</p>
        </div>
    `;
    updateStatistics();
}

function exportEmails() {
    if (scrapedEmails.size === 0) {
        showAlert('No emails to export.', 'warning');
        return;
    }
    
    const emailList = Array.from(scrapedEmails).join('\n');
    const blob = new Blob([emailList], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `scraped_emails_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function generateEmails() {
    const domains = document.getElementById('generator-domains').value.trim().split('\n').filter(d => d.trim());
    const patterns = Array.from(document.getElementById('email-patterns').selectedOptions).map(option => option.value);
    
    if (domains.length === 0) {
        showAlert('Please enter at least one domain.', 'warning');
        return;
    }
    
    fetch('/api/generate-emails', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            domains: domains,
            patterns: patterns
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        
        data.emails.forEach(email => {
            if (!scrapedEmails.has(email)) {
                scrapedEmails.add(email);
                addEmailToResults(email);
            }
        });
        
        updateStatistics();
        showAlert(`Generated ${data.count} email addresses.`, 'success');
    })
    .catch(error => {
        console.error('Error generating emails:', error);
        showAlert('Error generating emails: ' + error.message, 'danger');
    });
}

function copyEmail(email) {
    navigator.clipboard.writeText(email).then(() => {
        showAlert('Email copied to clipboard!', 'success');
    });
}

function showErrors(errors) {
    const errorsSection = document.getElementById('errors-section');
    const errorList = document.getElementById('error-list');
    
    errorList.innerHTML = errors.map(error => 
        `<div class="alert alert-warning alert-sm mb-2">${error}</div>`
    ).join('');
    
    errorsSection.style.display = 'block';
    document.getElementById('errors-count').textContent = errors.length;
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('main').insertBefore(alertDiv, document.querySelector('main').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}