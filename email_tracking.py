#!/usr/bin/env python3
"""
Email Tracking and Delivery Status Monitoring System
Provides comprehensive tracking for email campaigns including opens, clicks, bounces, and delivery status
"""

import sqlite3
import uuid
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json
from pathlib import Path
from urllib.parse import urlencode, parse_qs
import hashlib
import hmac
from loguru import logger

class TrackingEventType(Enum):
    """Types of tracking events"""
    SENT = "sent"
    DELIVERED = "delivered"
    OPENED = "opened"
    CLICKED = "clicked"
    BOUNCED = "bounced"
    COMPLAINED = "complained"
    UNSUBSCRIBED = "unsubscribed"
    FAILED = "failed"

@dataclass
class TrackingEvent:
    """Represents a tracking event"""
    id: str
    email_id: str
    campaign_id: str
    event_type: TrackingEventType
    timestamp: datetime
    recipient_email: str
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    click_url: Optional[str] = None
    bounce_reason: Optional[str] = None
    metadata: Optional[Dict] = None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['event_type'] = self.event_type.value
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TrackingEvent':
        """Create from dictionary"""
        data['event_type'] = TrackingEventType(data['event_type'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)

@dataclass
class EmailMetrics:
    """Email metrics for a campaign or individual email"""
    total_sent: int = 0
    total_delivered: int = 0
    total_opened: int = 0
    total_clicked: int = 0
    total_bounced: int = 0
    total_complained: int = 0
    total_unsubscribed: int = 0
    total_failed: int = 0
    unique_opens: int = 0
    unique_clicks: int = 0
    
    @property
    def delivery_rate(self) -> float:
        """Calculate delivery rate"""
        return (self.total_delivered / self.total_sent) if self.total_sent > 0 else 0.0
    
    @property
    def open_rate(self) -> float:
        """Calculate open rate"""
        return (self.unique_opens / self.total_delivered) if self.total_delivered > 0 else 0.0
    
    @property
    def click_rate(self) -> float:
        """Calculate click rate"""
        return (self.unique_clicks / self.total_delivered) if self.total_delivered > 0 else 0.0
    
    @property
    def bounce_rate(self) -> float:
        """Calculate bounce rate"""
        return (self.total_bounced / self.total_sent) if self.total_sent > 0 else 0.0
    
    @property
    def complaint_rate(self) -> float:
        """Calculate complaint rate"""
        return (self.total_complained / self.total_delivered) if self.total_delivered > 0 else 0.0

class EmailTracker:
    """Email tracking and analytics system"""
    
    def __init__(self, db_path: str = "email_tracking.db", secret_key: str = None):
        self.db_path = Path(db_path)
        self.secret_key = secret_key or self._generate_secret_key()
        self.init_database()
    
    def _generate_secret_key(self) -> str:
        """Generate a secret key for tracking URLs"""
        return base64.urlsafe_b64encode(uuid.uuid4().bytes).decode('utf-8')
    
    def init_database(self):
        """Initialize the tracking database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS tracking_events (
                    id TEXT PRIMARY KEY,
                    email_id TEXT NOT NULL,
                    campaign_id TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    recipient_email TEXT NOT NULL,
                    user_agent TEXT,
                    ip_address TEXT,
                    click_url TEXT,
                    bounce_reason TEXT,
                    metadata TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS email_tracking (
                    email_id TEXT PRIMARY KEY,
                    campaign_id TEXT NOT NULL,
                    recipient_email TEXT NOT NULL,
                    tracking_pixel_id TEXT UNIQUE,
                    created_at TEXT NOT NULL,
                    last_updated TEXT NOT NULL
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS click_tracking (
                    id TEXT PRIMARY KEY,
                    email_id TEXT NOT NULL,
                    original_url TEXT NOT NULL,
                    tracking_url TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (email_id) REFERENCES email_tracking (email_id)
                )
            ''')
            
            # Create indexes for better performance
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tracking_events_email_id ON tracking_events(email_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tracking_events_campaign_id ON tracking_events(campaign_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tracking_events_type ON tracking_events(event_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_tracking_events_timestamp ON tracking_events(timestamp)')
            
            conn.commit()
    
    def create_email_tracking(self, email_id: str, campaign_id: str, recipient_email: str) -> str:
        """Create tracking record for an email"""
        tracking_pixel_id = str(uuid.uuid4())
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO email_tracking 
                (email_id, campaign_id, recipient_email, tracking_pixel_id, created_at, last_updated)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                email_id, campaign_id, recipient_email, tracking_pixel_id,
                datetime.now().isoformat(), datetime.now().isoformat()
            ))
            conn.commit()
        
        return tracking_pixel_id
    
    def generate_tracking_pixel_url(self, tracking_pixel_id: str, base_url: str = "http://localhost:8080") -> str:
        """Generate tracking pixel URL"""
        # Create signed tracking pixel ID
        signature = self._sign_data(tracking_pixel_id)
        params = {
            'id': tracking_pixel_id,
            'sig': signature
        }
        return f"{base_url}/track/pixel?{urlencode(params)}"
    
    def generate_click_tracking_url(self, email_id: str, original_url: str, base_url: str = "http://localhost:8080") -> str:
        """Generate click tracking URL"""
        click_id = str(uuid.uuid4())
        
        # Store click tracking record
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT INTO click_tracking (id, email_id, original_url, tracking_url, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (click_id, email_id, original_url, "", datetime.now().isoformat()))
            conn.commit()
        
        # Create signed click ID
        signature = self._sign_data(click_id)
        params = {
            'id': click_id,
            'url': base64.urlsafe_b64encode(original_url.encode()).decode(),
            'sig': signature
        }
        
        tracking_url = f"{base_url}/track/click?{urlencode(params)}"
        
        # Update tracking URL in database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('UPDATE click_tracking SET tracking_url = ? WHERE id = ?', 
                        (tracking_url, click_id))
            conn.commit()
        
        return tracking_url
    
    def _sign_data(self, data: str) -> str:
        """Create HMAC signature for data"""
        return hmac.new(
            self.secret_key.encode(),
            data.encode(),
            hashlib.sha256
        ).hexdigest()
    
    def _verify_signature(self, data: str, signature: str) -> bool:
        """Verify HMAC signature"""
        expected_signature = self._sign_data(data)
        return hmac.compare_digest(expected_signature, signature)
    
    def track_event(self, email_id: str, campaign_id: str, event_type: TrackingEventType, 
                   recipient_email: str, **kwargs) -> str:
        """Track an email event"""
        event_id = str(uuid.uuid4())
        
        event = TrackingEvent(
            id=event_id,
            email_id=email_id,
            campaign_id=campaign_id,
            event_type=event_type,
            timestamp=datetime.now(),
            recipient_email=recipient_email,
            user_agent=kwargs.get('user_agent'),
            ip_address=kwargs.get('ip_address'),
            click_url=kwargs.get('click_url'),
            bounce_reason=kwargs.get('bounce_reason'),
            metadata=kwargs.get('metadata')
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT INTO tracking_events 
                (id, email_id, campaign_id, event_type, timestamp, recipient_email, 
                 user_agent, ip_address, click_url, bounce_reason, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                event.id, event.email_id, event.campaign_id, event.event_type.value,
                event.timestamp.isoformat(), event.recipient_email,
                event.user_agent, event.ip_address, event.click_url,
                event.bounce_reason, json.dumps(event.metadata) if event.metadata else None
            ))
            
            # Update email tracking last_updated
            conn.execute('''
                UPDATE email_tracking SET last_updated = ? WHERE email_id = ?
            ''', (datetime.now().isoformat(), email_id))
            
            conn.commit()
        
        logger.info(f"Tracked {event_type.value} event for email {email_id}")
        return event_id
    
    def track_pixel_open(self, tracking_pixel_id: str, user_agent: str = None, ip_address: str = None) -> bool:
        """Track email open via tracking pixel"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT email_id, campaign_id, recipient_email 
                    FROM email_tracking 
                    WHERE tracking_pixel_id = ?
                ''', (tracking_pixel_id,))
                
                result = cursor.fetchone()
                if not result:
                    logger.warning(f"No email found for tracking pixel {tracking_pixel_id}")
                    return False
                
                email_id, campaign_id, recipient_email = result
                
                # Check if already tracked to avoid duplicate opens
                cursor = conn.execute('''
                    SELECT COUNT(*) FROM tracking_events 
                    WHERE email_id = ? AND event_type = ? AND ip_address = ?
                ''', (email_id, TrackingEventType.OPENED.value, ip_address))
                
                if cursor.fetchone()[0] == 0:  # First open from this IP
                    self.track_event(
                        email_id=email_id,
                        campaign_id=campaign_id,
                        event_type=TrackingEventType.OPENED,
                        recipient_email=recipient_email,
                        user_agent=user_agent,
                        ip_address=ip_address
                    )
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to track pixel open: {e}")
            return False
    
    def track_click(self, click_id: str, user_agent: str = None, ip_address: str = None) -> Optional[str]:
        """Track email click and return original URL"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT ct.email_id, et.campaign_id, et.recipient_email, ct.original_url
                    FROM click_tracking ct
                    JOIN email_tracking et ON ct.email_id = et.email_id
                    WHERE ct.id = ?
                ''', (click_id,))
                
                result = cursor.fetchone()
                if not result:
                    logger.warning(f"No click tracking found for ID {click_id}")
                    return None
                
                email_id, campaign_id, recipient_email, original_url = result
                
                # Track the click event
                self.track_event(
                    email_id=email_id,
                    campaign_id=campaign_id,
                    event_type=TrackingEventType.CLICKED,
                    recipient_email=recipient_email,
                    user_agent=user_agent,
                    ip_address=ip_address,
                    click_url=original_url
                )
                
                return original_url
                
        except Exception as e:
            logger.error(f"Failed to track click: {e}")
            return None
    
    def get_email_metrics(self, email_id: str) -> EmailMetrics:
        """Get metrics for a specific email"""
        metrics = EmailMetrics()
        
        with sqlite3.connect(self.db_path) as conn:
            # Get event counts
            cursor = conn.execute('''
                SELECT event_type, COUNT(*) 
                FROM tracking_events 
                WHERE email_id = ? 
                GROUP BY event_type
            ''', (email_id,))
            
            for event_type, count in cursor.fetchall():
                if event_type == TrackingEventType.SENT.value:
                    metrics.total_sent = count
                elif event_type == TrackingEventType.DELIVERED.value:
                    metrics.total_delivered = count
                elif event_type == TrackingEventType.OPENED.value:
                    metrics.total_opened = count
                elif event_type == TrackingEventType.CLICKED.value:
                    metrics.total_clicked = count
                elif event_type == TrackingEventType.BOUNCED.value:
                    metrics.total_bounced = count
                elif event_type == TrackingEventType.COMPLAINED.value:
                    metrics.total_complained = count
                elif event_type == TrackingEventType.UNSUBSCRIBED.value:
                    metrics.total_unsubscribed = count
                elif event_type == TrackingEventType.FAILED.value:
                    metrics.total_failed = count
            
            # Get unique opens (distinct IP addresses)
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT ip_address) 
                FROM tracking_events 
                WHERE email_id = ? AND event_type = ?
            ''', (email_id, TrackingEventType.OPENED.value))
            metrics.unique_opens = cursor.fetchone()[0] or 0
            
            # Get unique clicks (distinct IP addresses)
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT ip_address) 
                FROM tracking_events 
                WHERE email_id = ? AND event_type = ?
            ''', (email_id, TrackingEventType.CLICKED.value))
            metrics.unique_clicks = cursor.fetchone()[0] or 0
        
        return metrics
    
    def get_campaign_metrics(self, campaign_id: str) -> EmailMetrics:
        """Get aggregated metrics for a campaign"""
        metrics = EmailMetrics()
        
        with sqlite3.connect(self.db_path) as conn:
            # Get event counts
            cursor = conn.execute('''
                SELECT event_type, COUNT(*) 
                FROM tracking_events 
                WHERE campaign_id = ? 
                GROUP BY event_type
            ''', (campaign_id,))
            
            for event_type, count in cursor.fetchall():
                if event_type == TrackingEventType.SENT.value:
                    metrics.total_sent = count
                elif event_type == TrackingEventType.DELIVERED.value:
                    metrics.total_delivered = count
                elif event_type == TrackingEventType.OPENED.value:
                    metrics.total_opened = count
                elif event_type == TrackingEventType.CLICKED.value:
                    metrics.total_clicked = count
                elif event_type == TrackingEventType.BOUNCED.value:
                    metrics.total_bounced = count
                elif event_type == TrackingEventType.COMPLAINED.value:
                    metrics.total_complained = count
                elif event_type == TrackingEventType.UNSUBSCRIBED.value:
                    metrics.total_unsubscribed = count
                elif event_type == TrackingEventType.FAILED.value:
                    metrics.total_failed = count
            
            # Get unique opens
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT CONCAT(email_id, '|', ip_address)) 
                FROM tracking_events 
                WHERE campaign_id = ? AND event_type = ?
            ''', (campaign_id, TrackingEventType.OPENED.value))
            metrics.unique_opens = cursor.fetchone()[0] or 0
            
            # Get unique clicks
            cursor = conn.execute('''
                SELECT COUNT(DISTINCT CONCAT(email_id, '|', ip_address)) 
                FROM tracking_events 
                WHERE campaign_id = ? AND event_type = ?
            ''', (campaign_id, TrackingEventType.CLICKED.value))
            metrics.unique_clicks = cursor.fetchone()[0] or 0
        
        return metrics
    
    def get_events(self, email_id: str = None, campaign_id: str = None, 
                  event_type: TrackingEventType = None, limit: int = 100) -> List[TrackingEvent]:
        """Get tracking events with optional filters"""
        query = 'SELECT * FROM tracking_events WHERE 1=1'
        params = []
        
        if email_id:
            query += ' AND email_id = ?'
            params.append(email_id)
        
        if campaign_id:
            query += ' AND campaign_id = ?'
            params.append(campaign_id)
        
        if event_type:
            query += ' AND event_type = ?'
            params.append(event_type.value)
        
        query += ' ORDER BY timestamp DESC LIMIT ?'
        params.append(limit)
        
        events = []
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            
            for row in cursor.fetchall():
                event = TrackingEvent(
                    id=row[0],
                    email_id=row[1],
                    campaign_id=row[2],
                    event_type=TrackingEventType(row[3]),
                    timestamp=datetime.fromisoformat(row[4]),
                    recipient_email=row[5],
                    user_agent=row[6],
                    ip_address=row[7],
                    click_url=row[8],
                    bounce_reason=row[9],
                    metadata=json.loads(row[10]) if row[10] else None
                )
                events.append(event)
        
        return events
    
    def get_campaign_timeline(self, campaign_id: str, days: int = 30) -> List[Dict]:
        """Get campaign timeline data for analytics"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('''
                SELECT DATE(timestamp) as date, event_type, COUNT(*) as count
                FROM tracking_events 
                WHERE campaign_id = ? AND timestamp >= ?
                GROUP BY DATE(timestamp), event_type
                ORDER BY date, event_type
            ''', (campaign_id, start_date.isoformat()))
            
            timeline_data = []
            for row in cursor.fetchall():
                timeline_data.append({
                    'date': row[0],
                    'event_type': row[1],
                    'count': row[2]
                })
        
        return timeline_data
    
    def cleanup_old_data(self, days: int = 90):
        """Clean up tracking data older than specified days"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            # Delete old tracking events
            cursor = conn.execute('''
                DELETE FROM tracking_events WHERE timestamp < ?
            ''', (cutoff_date.isoformat(),))
            
            deleted_events = cursor.rowcount
            
            # Delete old click tracking records
            cursor = conn.execute('''
                DELETE FROM click_tracking WHERE created_at < ?
            ''', (cutoff_date.isoformat(),))
            
            deleted_clicks = cursor.rowcount
            
            # Delete old email tracking records that have no events
            cursor = conn.execute('''
                DELETE FROM email_tracking 
                WHERE created_at < ? AND email_id NOT IN (
                    SELECT DISTINCT email_id FROM tracking_events
                )
            ''', (cutoff_date.isoformat(),))
            
            deleted_emails = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"Cleaned up {deleted_events} events, {deleted_clicks} clicks, {deleted_emails} email records")
    
    def export_metrics(self, campaign_id: str = None, format: str = 'json') -> str:
        """Export metrics data"""
        if campaign_id:
            metrics = self.get_campaign_metrics(campaign_id)
            timeline = self.get_campaign_timeline(campaign_id)
            
            data = {
                'campaign_id': campaign_id,
                'metrics': asdict(metrics),
                'timeline': timeline,
                'exported_at': datetime.now().isoformat()
            }
        else:
            # Export all campaigns
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT DISTINCT campaign_id FROM tracking_events')
                campaign_ids = [row[0] for row in cursor.fetchall()]
            
            data = {
                'campaigns': {},
                'exported_at': datetime.now().isoformat()
            }
            
            for cid in campaign_ids:
                metrics = self.get_campaign_metrics(cid)
                timeline = self.get_campaign_timeline(cid)
                data['campaigns'][cid] = {
                    'metrics': asdict(metrics),
                    'timeline': timeline
                }
        
        if format.lower() == 'json':
            return json.dumps(data, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")

# Utility functions for email content modification
def inject_tracking_pixel(html_content: str, tracking_pixel_url: str) -> str:
    """Inject tracking pixel into HTML email content"""
    tracking_pixel = f'<img src="{tracking_pixel_url}" width="1" height="1" style="display:none;" alt="" />'
    
    # Try to insert before closing body tag
    if '</body>' in html_content:
        return html_content.replace('</body>', f'{tracking_pixel}</body>')
    else:
        # If no body tag, append at the end
        return html_content + tracking_pixel

def replace_links_with_tracking(html_content: str, email_tracker: EmailTracker, 
                               email_id: str, base_url: str = "http://localhost:8080") -> str:
    """Replace all links in HTML content with tracking URLs"""
    import re
    
    # Find all href attributes
    href_pattern = r'href=["\']([^"\'>]+)["\']'
    
    def replace_link(match):
        original_url = match.group(1)
        
        # Skip mailto, tel, and anchor links
        if original_url.startswith(('mailto:', 'tel:', '#')):
            return match.group(0)
        
        # Generate tracking URL
        tracking_url = email_tracker.generate_click_tracking_url(email_id, original_url, base_url)
        return f'href="{tracking_url}"'
    
    return re.sub(href_pattern, replace_link, html_content)

def prepare_tracked_email(html_content: str, email_tracker: EmailTracker, 
                         email_id: str, campaign_id: str, recipient_email: str,
                         base_url: str = "http://localhost:8080") -> str:
    """Prepare email content with tracking pixel and link tracking"""
    # Create tracking record
    tracking_pixel_id = email_tracker.create_email_tracking(email_id, campaign_id, recipient_email)
    
    # Generate tracking pixel URL
    tracking_pixel_url = email_tracker.generate_tracking_pixel_url(tracking_pixel_id, base_url)
    
    # Inject tracking pixel
    tracked_content = inject_tracking_pixel(html_content, tracking_pixel_url)
    
    # Replace links with tracking URLs
    tracked_content = replace_links_with_tracking(tracked_content, email_tracker, email_id, base_url)
    
    return tracked_content