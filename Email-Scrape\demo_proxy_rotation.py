#!/usr/bin/env python3
"""
Demo script showing proxy rotation functionality
This script demonstrates the proxy rotation system without requiring real proxies
"""

import asyncio
from email_scraper import WebScraper
from config import get_config, ProxyConfig
from loguru import logger

async def demo_proxy_rotation():
    """Demonstrate proxy rotation functionality"""
    print("Email Scraper Pro - Proxy Rotation Demo")
    print("=" * 45)
    
    # Get config
    config = get_config()
    
    # Clear existing proxies and add demo proxies
    config.proxies.clear()
    
    # Add some demo proxies (these will fail but show the rotation logic)
    demo_proxies = [
        {"host": "demo-proxy-1.example.com", "port": 8080, "protocol": "http"},
        {"host": "demo-proxy-2.example.com", "port": 3128, "protocol": "http"},
        {"host": "demo-proxy-3.example.com", "port": 8080, "protocol": "https"},
    ]
    
    for proxy_info in demo_proxies:
        config.add_proxy(**proxy_info)
    
    print(f"Added {len(config.proxies)} demo proxies:")
    for i, proxy in enumerate(config.proxies, 1):
        print(f"  {i}. {proxy}")
    
    print("\nDemonstrating proxy rotation logic...")
    print("-" * 40)
    
    # Create scraper instance
    scraper = WebScraper()
    
    try:
        # Show initial state
        print(f"Initial proxy rotation count: {scraper.proxy_rotation_count}")
        print(f"Failed proxies: {len(scraper.failed_proxies)}")
        
        # Demonstrate proxy selection
        print("\nProxy Selection Demo:")
        for i in range(5):
            proxy = scraper.get_next_proxy()
            print(f"  Rotation {i+1}: {proxy}")
        
        print(f"\nProxy rotation count after selections: {scraper.proxy_rotation_count}")
        
        # Demonstrate marking proxies as failed
        print("\nMarking proxies as failed:")
        for proxy in config.proxies[:2]:  # Mark first 2 as failed
            scraper.mark_proxy_failed(proxy)
            print(f"  Marked as failed: {proxy}")
        
        print(f"Failed proxies: {len(scraper.failed_proxies)}")
        
        # Show remaining available proxies
        print("\nRemaining available proxies:")
        for i in range(3):
            proxy = scraper.get_next_proxy()
            if proxy:
                print(f"  Available: {proxy}")
            else:
                print(f"  No more proxies available")
                break
        
        # Demonstrate browser initialization with proxy
        print("\nDemonstrating browser initialization with proxy...")
        test_proxy = config.proxies[0] if config.proxies else None
        if test_proxy:
            print(f"Initializing browser with proxy: {test_proxy}")
            try:
                await scraper.init_browser(proxy=test_proxy)
                print("✓ Browser initialized successfully")
                print(f"Current proxy: {scraper.current_proxy}")
            except Exception as e:
                print(f"✗ Browser initialization failed (expected): {e}")
        
        # Show configuration options
        print("\nProxy Configuration Options:")
        print(f"  Min delay: {config.scraping.min_delay}s")
        print(f"  Max delay: {config.scraping.max_delay}s")
        print(f"  Max retries: {config.scraping.max_retries}")
        print(f"  User agents available: {len(config.user_agents)}")
        
        # Demonstrate user agent rotation
        print("\nUser Agent Rotation Demo:")
        for i in range(3):
            ua = config.get_random_user_agent()
            print(f"  UA {i+1}: {ua[:80]}...")
        
    except Exception as e:
        print(f"Demo error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await scraper.close_browser()
        print("\nDemo completed!")
        
        # Show summary
        print("\nProxy Rotation Summary:")
        print(f"  Total proxies configured: {len(config.proxies)}")
        print(f"  Failed proxies: {len(scraper.failed_proxies)}")
        print(f"  Rotation count: {scraper.proxy_rotation_count}")
        
        print("\nTo use real proxies:")
        print("  1. Edit sample_proxies.txt with real proxy servers")
        print("  2. Run: python proxy_manager.py --load sample_proxies.txt")
        print("  3. Test proxies: python proxy_manager.py --test")
        print("  4. Run scraper with: python gui_app.py or python cli_app.py")

if __name__ == "__main__":
    try:
        asyncio.run(demo_proxy_rotation())
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")