# Advanced Email Delivery System

## 🚀 Overview

The Advanced Email Delivery System is a comprehensive solution for sending emails with sophisticated obfuscation, anti-spam measures, attachment support, and inbox validation. This system is designed to maximize email deliverability while maintaining security and compliance.

## ✨ Key Features

### 📧 Advanced Email Capabilities
- **Multi-format Attachments**: Support for PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, images, and more
- **Inline Images**: Embed images directly in HTML emails
- **Template System**: Dynamic content with Jinja2 templating
- **Personalization**: Variable substitution for personalized emails

### 🛡️ Obfuscation & Anti-Detection
- **Content Obfuscation**: Multiple techniques to avoid spam filters
  - HTML entity encoding
  - Unicode character variants
  - Invisible character insertion
  - Random whitespace variations
  - Link obfuscation with Base64 encoding
- **Header Randomization**: Randomized email headers to avoid pattern detection
- **Filename Obfuscation**: Automatic attachment filename randomization

### 🔒 Anti-Spam & Deliverability
- **SPF/DKIM/DMARC Validation**: Check sender domain authentication
- **MX Record Validation**: Verify recipient domain mail servers
- **Blacklist Checking**: Real-time blacklist verification
- **Rate Limiting**: Configurable sending limits per hour
- **Reputation Management**: Track and maintain sender reputation
- **Bounce Tracking**: Monitor and handle email bounces

### 📊 Analytics & Tracking
- **Delivery Metrics**: Comprehensive tracking of sent, delivered, bounced emails
- **Open/Click Tracking**: Monitor email engagement (when implemented)
- **Campaign Analytics**: Detailed reporting for bulk campaigns
- **Export Capabilities**: JSON export of delivery logs and metrics

## 🛠️ Installation

### Prerequisites
```bash
Python 3.8+
```

### Install Dependencies
```bash
pip install -r requirements.txt
```

### Required Packages
```
dnspython>=2.4.0
jinja2>=3.1.0
cryptography>=41.0.0
email-validator>=2.0.0
loguru>=0.7.0
requests>=2.31.0
aiohttp>=3.9.0
```

## 🚀 Quick Start

### 1. Basic Setup
```python
from advanced_email_delivery import AdvancedEmailDelivery, ObfuscationConfig, AntiSpamConfig

# Initialize the delivery system
delivery_system = AdvancedEmailDelivery()
```

### 2. Simple Obfuscated Email
```python
import asyncio

async def send_basic_email():
    # Configure obfuscation
    obf_config = ObfuscationConfig(
        randomize_headers=True,
        vary_content=True,
        use_html_entities=True,
        insert_invisible_chars=True
    )
    
    # Send email
    results = await delivery_system.send_advanced_email(
        to_emails=["<EMAIL>"],
        subject="Business Opportunity",
        body="We have an exciting opportunity for you.",
        obfuscation_config=obf_config,
        sender_name="Business Team"
    )
    
    print(f"Results: {results}")

# Run the example
asyncio.run(send_basic_email())
```

### 3. Email with Attachments
```python
from advanced_email_delivery import AttachmentConfig

async def send_with_attachments():
    # Configure attachments
    attachments = [
        AttachmentConfig(
            file_path="document.pdf",
            filename="Business_Proposal.pdf",
            obfuscate_name=True
        ),
        AttachmentConfig(
            file_path="logo.png",
            inline=True,
            content_id="company_logo"
        )
    ]
    
    html_body = """
    <html>
    <body>
        <img src="cid:company_logo" alt="Logo">
        <h2>Business Proposal</h2>
        <p>Please find attached our proposal.</p>
    </body>
    </html>
    """
    
    results = await delivery_system.send_advanced_email(
        to_emails=["<EMAIL>"],
        subject="Business Proposal - Review Required",
        body="Please review the attached proposal.",
        html_body=html_body,
        attachments=attachments
    )
```

## ⚙️ Configuration Options

### ObfuscationConfig
```python
ObfuscationConfig(
    randomize_headers=True,        # Randomize email headers
    vary_content=True,             # Apply content variations
    use_html_entities=True,        # Convert chars to HTML entities
    randomize_whitespace=True,     # Add random whitespace
    split_links=True,              # Obfuscate links
    use_unicode_variants=True,     # Use Unicode character variants
    insert_invisible_chars=True,   # Insert zero-width characters
    use_base64_encoding=False      # Base64 encode content
)
```

### AntiSpamConfig
```python
AntiSpamConfig(
    check_spf=True,                # Validate SPF records
    check_dkim=True,               # Validate DKIM
    check_dmarc=True,              # Validate DMARC
    validate_mx_records=True,      # Check MX records
    check_blacklists=True,         # Check RBL blacklists
    rate_limit_per_hour=100,       # Max emails per hour
    delay_between_emails=2.0,      # Delay between sends (seconds)
    randomize_delays=True,         # Randomize delays
    use_reputation_warming=True,   # Gradual volume increase
    track_bounces=True             # Monitor bounces
)
```

### AttachmentConfig
```python
AttachmentConfig(
    file_path="document.pdf",      # Path to file
    filename="custom_name.pdf",    # Custom filename
    content_type="application/pdf", # MIME type (auto-detected)
    inline=False,                  # Inline attachment
    content_id="unique_id",        # Content ID for inline
    obfuscate_name=True,           # Randomize filename
    max_size_mb=25.0              # Max file size
)
```

## 📈 Advanced Features

### Bulk Email Campaigns
```python
async def bulk_campaign():
    recipients = ["<EMAIL>", "<EMAIL>"]
    
    for recipient in recipients:
        # Personalized content
        personalized_subject = f"Hello {recipient.split('@')[0]}"
        
        await delivery_system.send_advanced_email(
            to_emails=[recipient],
            subject=personalized_subject,
            body="Personalized message content",
            obfuscation_config=obf_config,
            anti_spam_config=anti_spam_config
        )
```

### Delivery Metrics
```python
# Get comprehensive metrics
metrics = delivery_system.get_delivery_metrics()
print(f"Delivery Rate: {metrics['delivery_rate']:.2f}%")
print(f"Open Rate: {metrics['open_rate']:.2f}%")
print(f"Reputation Score: {metrics['reputation_score']}")

# Export delivery log
log_file = delivery_system.export_delivery_log()
print(f"Log exported to: {log_file}")
```

### Inbox Validation
```python
from advanced_email_delivery import InboxValidator

validator = InboxValidator()
validation = await validator.validate_email_deliverability("<EMAIL>")

print(f"Valid: {validation['is_valid']}")
print(f"Deliverability Score: {validation['deliverability_score']}")
print(f"Recommendations: {validation['recommendations']}")
```

## 🔧 SMTP Configuration

The system uses the existing SMTP configuration from `smtp_config.py`. Ensure your MailHop SMTP is properly configured:

```json
{
  "smtp_configs": {
    "mailhop_carlos": {
      "provider": "mailhop",
      "smtp_server": "outbound.mailhop.org",
      "smtp_port": 465,
      "username": "Carlosfenandezlawfirm",
      "password": "Americana123456789@",
      "use_ssl": true
    }
  },
  "active_config": "mailhop_carlos"
}
```

## 📊 Monitoring & Analytics

### Real-time Metrics
- Total emails sent/delivered/bounced
- Open and click rates
- Spam complaint rates
- Sender reputation score
- Delivery success rates

### Logging
All email activities are logged with detailed information:
- Timestamp
- Recipient
- Status (sent/failed/bounced)
- Reason for failures
- Message IDs for tracking

## 🛡️ Security Features

### Data Protection
- Secure credential storage
- Encrypted password handling
- Safe file attachment processing
- Input validation and sanitization

### Compliance
- GDPR-compliant data handling
- CAN-SPAM Act compliance
- Bounce handling and suppression
- Unsubscribe management (when implemented)

## 🚨 Best Practices

### Deliverability
1. **Warm up your IP**: Start with low volumes and gradually increase
2. **Maintain good reputation**: Monitor bounce rates and spam complaints
3. **Authenticate your domain**: Set up SPF, DKIM, and DMARC records
4. **Clean your lists**: Remove invalid and bounced email addresses
5. **Monitor blacklists**: Regularly check if your IP/domain is blacklisted

### Content Guidelines
1. **Avoid spam triggers**: Don't use excessive caps, exclamation marks
2. **Balance text/images**: Maintain good text-to-image ratio
3. **Include unsubscribe**: Always provide unsubscribe options
4. **Personalize content**: Use recipient names and relevant content
5. **Test before sending**: Use spam checkers and preview tools

## 📝 Examples

See `advanced_delivery_example.py` for comprehensive usage examples including:
- Basic obfuscated emails
- Emails with multiple attachments
- Anti-spam delivery configurations
- Bulk email campaigns
- Metrics and reporting

## 🤝 Support

For issues or questions:
1. Check the logs in `email_scraper.log`
2. Review the delivery metrics for insights
3. Validate SMTP configuration
4. Test with small batches first

## 📄 License

This software is part of the Email Scraper project and follows the same licensing terms.
