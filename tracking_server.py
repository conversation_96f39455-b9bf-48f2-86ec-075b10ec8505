#!/usr/bin/env python3
"""
Email Tracking Server
Simple HTTP server to handle tracking pixel opens and click tracking
"""

import asyncio
from aiohttp import web, ClientSession
from urllib.parse import parse_qs, unquote
import base64
from pathlib import Path
from loguru import logger
from email_tracking import EmailT<PERSON>
import json
from datetime import datetime

class TrackingServer:
    """HTTP server for email tracking"""
    
    def __init__(self, host: str = "localhost", port: int = 8080, 
                 tracker_db_path: str = "email_tracking.db"):
        self.host = host
        self.port = port
        self.tracker = EmailTracker(tracker_db_path)
        self.app = web.Application()
        self.setup_routes()
    
    def setup_routes(self):
        """Setup HTTP routes"""
        self.app.router.add_get('/track/pixel', self.handle_pixel_tracking)
        self.app.router.add_get('/track/click', self.handle_click_tracking)
        self.app.router.add_get('/health', self.handle_health_check)
        self.app.router.add_get('/stats', self.handle_stats)
        
        # CORS middleware for cross-origin requests
        self.app.middlewares.append(self.cors_middleware)
    
    @web.middleware
    async def cors_middleware(self, request, handler):
        """Add CORS headers"""
        response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    async def handle_pixel_tracking(self, request):
        """Handle tracking pixel requests"""
        try:
            # Get parameters
            tracking_id = request.query.get('id')
            signature = request.query.get('sig')
            
            if not tracking_id or not signature:
                logger.warning("Missing tracking parameters")
                return self.create_pixel_response()
            
            # Verify signature
            if not self.tracker._verify_signature(tracking_id, signature):
                logger.warning(f"Invalid signature for tracking ID {tracking_id}")
                return self.create_pixel_response()
            
            # Get client information
            user_agent = request.headers.get('User-Agent', '')
            ip_address = self.get_client_ip(request)
            
            # Track the open
            success = self.tracker.track_pixel_open(
                tracking_pixel_id=tracking_id,
                user_agent=user_agent,
                ip_address=ip_address
            )
            
            if success:
                logger.info(f"Tracked email open for pixel {tracking_id} from {ip_address}")
            
            return self.create_pixel_response()
            
        except Exception as e:
            logger.error(f"Error handling pixel tracking: {e}")
            return self.create_pixel_response()
    
    async def handle_click_tracking(self, request):
        """Handle click tracking requests"""
        try:
            # Get parameters
            click_id = request.query.get('id')
            encoded_url = request.query.get('url')
            signature = request.query.get('sig')
            
            if not click_id or not encoded_url or not signature:
                logger.warning("Missing click tracking parameters")
                return web.Response(text="Invalid request", status=400)
            
            # Verify signature
            if not self.tracker._verify_signature(click_id, signature):
                logger.warning(f"Invalid signature for click ID {click_id}")
                return web.Response(text="Invalid signature", status=400)
            
            # Decode original URL
            try:
                original_url = base64.urlsafe_b64decode(encoded_url.encode()).decode()
            except Exception as e:
                logger.error(f"Failed to decode URL: {e}")
                return web.Response(text="Invalid URL", status=400)
            
            # Get client information
            user_agent = request.headers.get('User-Agent', '')
            ip_address = self.get_client_ip(request)
            
            # Track the click
            tracked_url = self.tracker.track_click(
                click_id=click_id,
                user_agent=user_agent,
                ip_address=ip_address
            )
            
            if tracked_url:
                logger.info(f"Tracked click for ID {click_id} to {original_url} from {ip_address}")
                # Redirect to original URL
                return web.Response(
                    status=302,
                    headers={'Location': original_url}
                )
            else:
                logger.warning(f"Failed to track click for ID {click_id}")
                return web.Response(text="Click not found", status=404)
            
        except Exception as e:
            logger.error(f"Error handling click tracking: {e}")
            return web.Response(text="Server error", status=500)
    
    async def handle_health_check(self, request):
        """Health check endpoint"""
        return web.json_response({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'server': 'email-tracking-server'
        })
    
    async def handle_stats(self, request):
        """Get tracking statistics"""
        try:
            # Get query parameters
            campaign_id = request.query.get('campaign_id')
            days = int(request.query.get('days', 30))
            
            if campaign_id:
                metrics = self.tracker.get_campaign_metrics(campaign_id)
                timeline = self.tracker.get_campaign_timeline(campaign_id, days)
                
                return web.json_response({
                    'campaign_id': campaign_id,
                    'metrics': {
                        'total_sent': metrics.total_sent,
                        'total_delivered': metrics.total_delivered,
                        'total_opened': metrics.total_opened,
                        'total_clicked': metrics.total_clicked,
                        'total_bounced': metrics.total_bounced,
                        'unique_opens': metrics.unique_opens,
                        'unique_clicks': metrics.unique_clicks,
                        'delivery_rate': round(metrics.delivery_rate, 4),
                        'open_rate': round(metrics.open_rate, 4),
                        'click_rate': round(metrics.click_rate, 4),
                        'bounce_rate': round(metrics.bounce_rate, 4)
                    },
                    'timeline': timeline
                })
            else:
                # Return general stats
                return web.json_response({
                    'message': 'Provide campaign_id parameter for specific stats',
                    'example': f'/stats?campaign_id=your_campaign_id&days={days}'
                })
                
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return web.json_response(
                {'error': 'Failed to get statistics'}, 
                status=500
            )
    
    def create_pixel_response(self):
        """Create 1x1 transparent pixel response"""
        # 1x1 transparent GIF
        pixel_data = base64.b64decode(
            'R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'
        )
        
        return web.Response(
            body=pixel_data,
            content_type='image/gif',
            headers={
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
    
    def get_client_ip(self, request):
        """Get client IP address from request"""
        # Check for forwarded headers first
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # Fall back to remote address
        return request.remote
    
    async def start_server(self):
        """Start the tracking server"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()
        
        logger.info(f"Email tracking server started on http://{self.host}:{self.port}")
        logger.info(f"Tracking endpoints:")
        logger.info(f"  - Pixel tracking: http://{self.host}:{self.port}/track/pixel")
        logger.info(f"  - Click tracking: http://{self.host}:{self.port}/track/click")
        logger.info(f"  - Health check: http://{self.host}:{self.port}/health")
        logger.info(f"  - Statistics: http://{self.host}:{self.port}/stats")
        
        return runner
    
    def run(self):
        """Run the server (blocking)"""
        async def run_server():
            runner = await self.start_server()
            try:
                # Keep the server running
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("Shutting down tracking server...")
            finally:
                await runner.cleanup()
        
        asyncio.run(run_server())

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Email Tracking Server')
    parser.add_argument('--host', default='localhost', help='Server host')
    parser.add_argument('--port', type=int, default=8080, help='Server port')
    parser.add_argument('--db', default='email_tracking.db', help='Tracking database path')
    parser.add_argument('--log-level', default='INFO', help='Log level')
    
    args = parser.parse_args()
    
    # Configure logging
    logger.remove()
    logger.add(
        "tracking_server.log",
        rotation="10 MB",
        retention="30 days",
        level=args.log_level
    )
    logger.add(
        lambda msg: print(msg, end=''),
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Start server
    server = TrackingServer(args.host, args.port, args.db)
    server.run()

if __name__ == '__main__':
    main()