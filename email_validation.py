#!/usr/bin/env python3
"""
Email Validation and Bounce Handling System
Provides comprehensive email validation, bounce detection, and suppression list management
"""

import re
import dns.resolver
import smtplib
import socket
import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
from loguru import logger
import asyncio
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class ValidationResult(Enum):
    """Email validation results"""
    VALID = "valid"
    INVALID_FORMAT = "invalid_format"
    INVALID_DOMAIN = "invalid_domain"
    NO_MX_RECORD = "no_mx_record"
    SMTP_ERROR = "smtp_error"
    DISPOSABLE = "disposable"
    ROLE_BASED = "role_based"
    SUPPRESSED = "suppressed"
    UNKNOWN = "unknown"

class BounceType(Enum):
    """Types of email bounces"""
    HARD_BOUNCE = "hard_bounce"
    SOFT_BOUNCE = "soft_bounce"
    BLOCK = "block"
    COMPLAINT = "complaint"
    UNSUBSCRIBE = "unsubscribe"
    UNKNOWN = "unknown"

@dataclass
class EmailValidation:
    """Email validation result"""
    email: str
    result: ValidationResult
    confidence: float  # 0.0 to 1.0
    reason: str
    mx_records: List[str] = None
    smtp_valid: bool = None
    is_disposable: bool = False
    is_role_based: bool = False
    validated_at: datetime = None
    
    def __post_init__(self):
        if self.validated_at is None:
            self.validated_at = datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        data = asdict(self)
        data['result'] = self.result.value
        data['validated_at'] = self.validated_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EmailValidation':
        """Create from dictionary"""
        data['result'] = ValidationResult(data['result'])
        data['validated_at'] = datetime.fromisoformat(data['validated_at'])
        return cls(**data)

@dataclass
class BounceRecord:
    """Email bounce record"""
    id: str
    email: str
    bounce_type: BounceType
    reason: str
    campaign_id: Optional[str] = None
    bounce_date: datetime = None
    diagnostic_code: Optional[str] = None
    action: Optional[str] = None
    status: Optional[str] = None
    
    def __post_init__(self):
        if self.bounce_date is None:
            self.bounce_date = datetime.now()
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        data = asdict(self)
        data['bounce_type'] = self.bounce_type.value
        data['bounce_date'] = self.bounce_date.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'BounceRecord':
        """Create from dictionary"""
        data['bounce_type'] = BounceType(data['bounce_type'])
        data['bounce_date'] = datetime.fromisoformat(data['bounce_date'])
        return cls(**data)

class EmailValidator:
    """Email validation system"""
    
    def __init__(self, db_path: str = "email_validation.db"):
        self.db_path = Path(db_path)
        self.disposable_domains = self._load_disposable_domains()
        self.role_based_emails = self._load_role_based_emails()
        self.init_database()
    
    def _load_disposable_domains(self) -> Set[str]:
        """Load list of disposable email domains"""
        # Common disposable email domains
        return {
            '10minutemail.com', 'guerrillamail.com', 'mailinator.com',
            'tempmail.org', 'yopmail.com', 'throwaway.email',
            'temp-mail.org', 'getnada.com', 'maildrop.cc',
            'sharklasers.com', 'grr.la', 'guerrillamailblock.com'
        }
    
    def _load_role_based_emails(self) -> Set[str]:
        """Load list of role-based email prefixes"""
        return {
            'admin', 'administrator', 'postmaster', 'hostmaster',
            'webmaster', 'www', 'ftp', 'mail', 'email', 'marketing',
            'sales', 'support', 'help', 'info', 'contact', 'service',
            'noreply', 'no-reply', 'donotreply', 'do-not-reply',
            'abuse', 'security', 'privacy', 'legal', 'billing',
            'accounts', 'newsletter', 'news', 'alerts', 'notifications'
        }
    
    def init_database(self):
        """Initialize validation database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS email_validations (
                    email TEXT PRIMARY KEY,
                    result TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    reason TEXT NOT NULL,
                    mx_records TEXT,
                    smtp_valid BOOLEAN,
                    is_disposable BOOLEAN,
                    is_role_based BOOLEAN,
                    validated_at TEXT NOT NULL,
                    last_checked TEXT NOT NULL
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS bounce_records (
                    id TEXT PRIMARY KEY,
                    email TEXT NOT NULL,
                    bounce_type TEXT NOT NULL,
                    reason TEXT NOT NULL,
                    campaign_id TEXT,
                    bounce_date TEXT NOT NULL,
                    diagnostic_code TEXT,
                    action TEXT,
                    status TEXT
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS suppression_list (
                    email TEXT PRIMARY KEY,
                    reason TEXT NOT NULL,
                    added_date TEXT NOT NULL,
                    source TEXT,
                    permanent BOOLEAN DEFAULT 1
                )
            ''')
            
            # Create indexes
            conn.execute('CREATE INDEX IF NOT EXISTS idx_validations_result ON email_validations(result)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_bounces_email ON bounce_records(email)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_bounces_type ON bounce_records(bounce_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_bounces_date ON bounce_records(bounce_date)')
            
            conn.commit()
    
    def validate_email_format(self, email: str) -> Tuple[bool, str]:
        """Validate email format using regex"""
        if not email or '@' not in email:
            return False, "Missing @ symbol"
        
        # RFC 5322 compliant regex (simplified)
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(pattern, email):
            return False, "Invalid email format"
        
        local, domain = email.rsplit('@', 1)
        
        # Check local part length
        if len(local) > 64:
            return False, "Local part too long"
        
        # Check domain length
        if len(domain) > 253:
            return False, "Domain too long"
        
        # Check for consecutive dots
        if '..' in email:
            return False, "Consecutive dots not allowed"
        
        return True, "Valid format"
    
    def check_mx_records(self, domain: str) -> Tuple[bool, List[str]]:
        """Check if domain has MX records"""
        try:
            mx_records = dns.resolver.resolve(domain, 'MX')
            mx_list = [str(mx.exchange).rstrip('.') for mx in mx_records]
            return True, mx_list
        except (dns.resolver.NXDOMAIN, dns.resolver.NoAnswer, Exception):
            return False, []
    
    async def verify_smtp(self, email: str, mx_records: List[str]) -> Tuple[bool, str]:
        """Verify email via SMTP (async)"""
        if not mx_records:
            return False, "No MX records"
        
        for mx in mx_records[:3]:  # Try first 3 MX records
            try:
                # Use aiosmtplib for async SMTP
                smtp = aiosmtplib.SMTP(hostname=mx, port=25, timeout=10)
                await smtp.connect()
                
                # HELO/EHLO
                await smtp.ehlo()
                
                # MAIL FROM
                await smtp.mail('<EMAIL>')
                
                # RCPT TO
                code, message = await smtp.rcpt(email)
                await smtp.quit()
                
                if code == 250:
                    return True, "SMTP verification successful"
                elif code in [550, 551, 553]:
                    return False, f"SMTP error: {message}"
                else:
                    return False, f"SMTP code {code}: {message}"
                    
            except Exception as e:
                logger.debug(f"SMTP verification failed for {mx}: {e}")
                continue
        
        return False, "All MX servers failed"
    
    def is_disposable_email(self, email: str) -> bool:
        """Check if email is from a disposable domain"""
        domain = email.split('@')[1].lower()
        return domain in self.disposable_domains
    
    def is_role_based_email(self, email: str) -> bool:
        """Check if email is role-based"""
        local_part = email.split('@')[0].lower()
        return local_part in self.role_based_emails
    
    async def validate_email(self, email: str, check_smtp: bool = True, 
                           use_cache: bool = True) -> EmailValidation:
        """Comprehensive email validation"""
        email = email.lower().strip()
        
        # Check cache first
        if use_cache:
            cached = self.get_cached_validation(email)
            if cached and (datetime.now() - cached.validated_at).days < 7:
                return cached
        
        # Format validation
        format_valid, format_reason = self.validate_email_format(email)
        if not format_valid:
            validation = EmailValidation(
                email=email,
                result=ValidationResult.INVALID_FORMAT,
                confidence=1.0,
                reason=format_reason
            )
            self.cache_validation(validation)
            return validation
        
        domain = email.split('@')[1]
        
        # Check if suppressed
        if self.is_suppressed(email):
            validation = EmailValidation(
                email=email,
                result=ValidationResult.SUPPRESSED,
                confidence=1.0,
                reason="Email is in suppression list"
            )
            return validation
        
        # Check disposable
        is_disposable = self.is_disposable_email(email)
        if is_disposable:
            validation = EmailValidation(
                email=email,
                result=ValidationResult.DISPOSABLE,
                confidence=0.9,
                reason="Disposable email domain",
                is_disposable=True
            )
            self.cache_validation(validation)
            return validation
        
        # Check role-based
        is_role_based = self.is_role_based_email(email)
        
        # MX record check
        mx_valid, mx_records = self.check_mx_records(domain)
        if not mx_valid:
            validation = EmailValidation(
                email=email,
                result=ValidationResult.NO_MX_RECORD,
                confidence=0.95,
                reason="No MX records found",
                is_role_based=is_role_based
            )
            self.cache_validation(validation)
            return validation
        
        # SMTP verification
        smtp_valid = False
        smtp_reason = "SMTP check skipped"
        confidence = 0.7
        
        if check_smtp:
            try:
                smtp_valid, smtp_reason = await self.verify_smtp(email, mx_records)
                confidence = 0.95 if smtp_valid else 0.8
            except Exception as e:
                smtp_reason = f"SMTP check failed: {e}"
                confidence = 0.6
        
        # Determine final result
        if smtp_valid:
            result = ValidationResult.VALID
        elif "550" in smtp_reason or "551" in smtp_reason:
            result = ValidationResult.INVALID_DOMAIN
        elif smtp_reason and "failed" in smtp_reason.lower():
            result = ValidationResult.SMTP_ERROR
        else:
            result = ValidationResult.UNKNOWN
        
        # Adjust confidence for role-based emails
        if is_role_based:
            confidence *= 0.8
            if result == ValidationResult.VALID:
                result = ValidationResult.ROLE_BASED
        
        validation = EmailValidation(
            email=email,
            result=result,
            confidence=confidence,
            reason=smtp_reason,
            mx_records=mx_records,
            smtp_valid=smtp_valid,
            is_disposable=is_disposable,
            is_role_based=is_role_based
        )
        
        self.cache_validation(validation)
        return validation
    
    def cache_validation(self, validation: EmailValidation):
        """Cache validation result"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO email_validations 
                (email, result, confidence, reason, mx_records, smtp_valid, 
                 is_disposable, is_role_based, validated_at, last_checked)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                validation.email, validation.result.value, validation.confidence,
                validation.reason, json.dumps(validation.mx_records) if validation.mx_records else None,
                validation.smtp_valid, validation.is_disposable, validation.is_role_based,
                validation.validated_at.isoformat(), datetime.now().isoformat()
            ))
            conn.commit()
    
    def get_cached_validation(self, email: str) -> Optional[EmailValidation]:
        """Get cached validation result"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                'SELECT * FROM email_validations WHERE email = ?', (email,)
            )
            row = cursor.fetchone()
            
            if row:
                return EmailValidation(
                    email=row[0],
                    result=ValidationResult(row[1]),
                    confidence=row[2],
                    reason=row[3],
                    mx_records=json.loads(row[4]) if row[4] else None,
                    smtp_valid=row[5],
                    is_disposable=row[6],
                    is_role_based=row[7],
                    validated_at=datetime.fromisoformat(row[8])
                )
        
        return None
    
    async def validate_email_list(self, emails: List[str], 
                                 check_smtp: bool = True, 
                                 max_concurrent: int = 10) -> List[EmailValidation]:
        """Validate multiple emails concurrently"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def validate_with_semaphore(email):
            async with semaphore:
                return await self.validate_email(email, check_smtp)
        
        tasks = [validate_with_semaphore(email) for email in emails]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    def add_bounce(self, bounce_record: BounceRecord):
        """Add bounce record"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO bounce_records 
                (id, email, bounce_type, reason, campaign_id, bounce_date, 
                 diagnostic_code, action, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                bounce_record.id, bounce_record.email, bounce_record.bounce_type.value,
                bounce_record.reason, bounce_record.campaign_id, 
                bounce_record.bounce_date.isoformat(),
                bounce_record.diagnostic_code, bounce_record.action, bounce_record.status
            ))
            
            # Auto-suppress hard bounces
            if bounce_record.bounce_type == BounceType.HARD_BOUNCE:
                self.add_to_suppression_list(
                    bounce_record.email, 
                    f"Hard bounce: {bounce_record.reason}",
                    "bounce_handler",
                    permanent=True
                )
            
            conn.commit()
    
    def parse_bounce_message(self, bounce_message: str, email: str = None) -> Optional[BounceRecord]:
        """Parse bounce message and extract information"""
        import uuid
        
        bounce_id = str(uuid.uuid4())
        
        # Common bounce patterns
        hard_bounce_patterns = [
            r'550.*user unknown',
            r'550.*mailbox.*not.*found',
            r'550.*recipient.*rejected',
            r'551.*user.*not.*local',
            r'553.*mailbox.*name.*not.*allowed'
        ]
        
        soft_bounce_patterns = [
            r'421.*service.*not.*available',
            r'450.*mailbox.*temporarily.*unavailable',
            r'451.*temporary.*local.*problem',
            r'452.*insufficient.*system.*storage'
        ]
        
        block_patterns = [
            r'554.*blocked',
            r'550.*spam',
            r'550.*blacklist',
            r'554.*reputation'
        ]
        
        bounce_message_lower = bounce_message.lower()
        
        # Determine bounce type
        bounce_type = BounceType.UNKNOWN
        for pattern in hard_bounce_patterns:
            if re.search(pattern, bounce_message_lower):
                bounce_type = BounceType.HARD_BOUNCE
                break
        
        if bounce_type == BounceType.UNKNOWN:
            for pattern in soft_bounce_patterns:
                if re.search(pattern, bounce_message_lower):
                    bounce_type = BounceType.SOFT_BOUNCE
                    break
        
        if bounce_type == BounceType.UNKNOWN:
            for pattern in block_patterns:
                if re.search(pattern, bounce_message_lower):
                    bounce_type = BounceType.BLOCK
                    break
        
        # Extract email if not provided
        if not email:
            email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
            email_match = re.search(email_pattern, bounce_message)
            if email_match:
                email = email_match.group(0)
            else:
                return None
        
        # Extract diagnostic code
        diagnostic_pattern = r'(\d{3}\s+\d+\.\d+\.\d+)'
        diagnostic_match = re.search(diagnostic_pattern, bounce_message)
        diagnostic_code = diagnostic_match.group(1) if diagnostic_match else None
        
        return BounceRecord(
            id=bounce_id,
            email=email,
            bounce_type=bounce_type,
            reason=bounce_message[:500],  # Truncate long messages
            diagnostic_code=diagnostic_code
        )
    
    def add_to_suppression_list(self, email: str, reason: str, 
                               source: str = "manual", permanent: bool = True):
        """Add email to suppression list"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                INSERT OR REPLACE INTO suppression_list 
                (email, reason, added_date, source, permanent)
                VALUES (?, ?, ?, ?, ?)
            ''', (email.lower(), reason, datetime.now().isoformat(), source, permanent))
            conn.commit()
        
        logger.info(f"Added {email} to suppression list: {reason}")
    
    def remove_from_suppression_list(self, email: str) -> bool:
        """Remove email from suppression list"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                'DELETE FROM suppression_list WHERE email = ?', (email.lower(),)
            )
            conn.commit()
            
            if cursor.rowcount > 0:
                logger.info(f"Removed {email} from suppression list")
                return True
            return False
    
    def is_suppressed(self, email: str) -> bool:
        """Check if email is in suppression list"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(
                'SELECT COUNT(*) FROM suppression_list WHERE email = ?', (email.lower(),)
            )
            return cursor.fetchone()[0] > 0
    
    def get_suppression_list(self, limit: int = 1000) -> List[Dict]:
        """Get suppression list"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('''
                SELECT email, reason, added_date, source, permanent 
                FROM suppression_list 
                ORDER BY added_date DESC 
                LIMIT ?
            ''', (limit,))
            
            return [{
                'email': row[0],
                'reason': row[1],
                'added_date': row[2],
                'source': row[3],
                'permanent': bool(row[4])
            } for row in cursor.fetchall()]
    
    def get_bounce_statistics(self, days: int = 30) -> Dict:
        """Get bounce statistics"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            # Total bounces by type
            cursor = conn.execute('''
                SELECT bounce_type, COUNT(*) 
                FROM bounce_records 
                WHERE bounce_date >= ? 
                GROUP BY bounce_type
            ''', (start_date.isoformat(),))
            
            bounce_counts = dict(cursor.fetchall())
            
            # Daily bounce trends
            cursor = conn.execute('''
                SELECT DATE(bounce_date) as date, bounce_type, COUNT(*) 
                FROM bounce_records 
                WHERE bounce_date >= ? 
                GROUP BY DATE(bounce_date), bounce_type 
                ORDER BY date
            ''', (start_date.isoformat(),))
            
            daily_trends = []
            for row in cursor.fetchall():
                daily_trends.append({
                    'date': row[0],
                    'bounce_type': row[1],
                    'count': row[2]
                })
            
            # Top bounce reasons
            cursor = conn.execute('''
                SELECT reason, COUNT(*) as count 
                FROM bounce_records 
                WHERE bounce_date >= ? 
                GROUP BY reason 
                ORDER BY count DESC 
                LIMIT 10
            ''', (start_date.isoformat(),))
            
            top_reasons = [{
                'reason': row[0],
                'count': row[1]
            } for row in cursor.fetchall()]
            
            return {
                'bounce_counts': bounce_counts,
                'daily_trends': daily_trends,
                'top_reasons': top_reasons,
                'period_days': days
            }
    
    def cleanup_old_data(self, days: int = 90):
        """Clean up old validation and bounce data"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            # Clean old validations
            cursor = conn.execute(
                'DELETE FROM email_validations WHERE last_checked < ?',
                (cutoff_date.isoformat(),)
            )
            deleted_validations = cursor.rowcount
            
            # Clean old bounces (keep permanent suppressions)
            cursor = conn.execute(
                'DELETE FROM bounce_records WHERE bounce_date < ?',
                (cutoff_date.isoformat(),)
            )
            deleted_bounces = cursor.rowcount
            
            # Clean temporary suppressions
            cursor = conn.execute(
                'DELETE FROM suppression_list WHERE permanent = 0 AND added_date < ?',
                (cutoff_date.isoformat(),)
            )
            deleted_suppressions = cursor.rowcount
            
            conn.commit()
            
            logger.info(f"Cleaned up {deleted_validations} validations, "
                       f"{deleted_bounces} bounces, {deleted_suppressions} temp suppressions")
    
    def export_data(self, data_type: str = "all", format: str = "json") -> str:
        """Export validation data"""
        data = {}
        
        if data_type in ["all", "validations"]:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT * FROM email_validations')
                validations = []
                for row in cursor.fetchall():
                    validations.append({
                        'email': row[0],
                        'result': row[1],
                        'confidence': row[2],
                        'reason': row[3],
                        'mx_records': json.loads(row[4]) if row[4] else None,
                        'smtp_valid': row[5],
                        'is_disposable': row[6],
                        'is_role_based': row[7],
                        'validated_at': row[8],
                        'last_checked': row[9]
                    })
                data['validations'] = validations
        
        if data_type in ["all", "bounces"]:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('SELECT * FROM bounce_records')
                bounces = []
                for row in cursor.fetchall():
                    bounces.append({
                        'id': row[0],
                        'email': row[1],
                        'bounce_type': row[2],
                        'reason': row[3],
                        'campaign_id': row[4],
                        'bounce_date': row[5],
                        'diagnostic_code': row[6],
                        'action': row[7],
                        'status': row[8]
                    })
                data['bounces'] = bounces
        
        if data_type in ["all", "suppressions"]:
            data['suppressions'] = self.get_suppression_list(limit=10000)
        
        data['exported_at'] = datetime.now().isoformat()
        
        if format.lower() == 'json':
            return json.dumps(data, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")

# Utility functions
def filter_valid_emails(emails: List[str], validator: EmailValidator, 
                       min_confidence: float = 0.7) -> List[str]:
    """Filter list to only include valid emails"""
    valid_emails = []
    
    async def validate_all():
        validations = await validator.validate_email_list(emails)
        for validation in validations:
            if (isinstance(validation, EmailValidation) and 
                validation.result == ValidationResult.VALID and 
                validation.confidence >= min_confidence):
                valid_emails.append(validation.email)
    
    asyncio.run(validate_all())
    return valid_emails

def create_validation_report(validations: List[EmailValidation]) -> Dict:
    """Create validation report from results"""
    total = len(validations)
    if total == 0:
        return {'total': 0, 'summary': {}}
    
    summary = {}
    for validation in validations:
        result = validation.result.value
        summary[result] = summary.get(result, 0) + 1
    
    # Calculate percentages
    percentages = {k: (v / total) * 100 for k, v in summary.items()}
    
    return {
        'total': total,
        'summary': summary,
        'percentages': percentages,
        'valid_count': summary.get('valid', 0),
        'invalid_count': total - summary.get('valid', 0),
        'average_confidence': sum(v.confidence for v in validations) / total
    }