# Add BMD EMAIL SCRAPPER to Windows Security Exclusions
# This script adds the current folder to Windows Defender exclusions
# Run as Administrator for best results

Write-Host "Adding BMD EMAIL SCRAPPER to Windows Security Exclusions..." -ForegroundColor Green
Write-Host "" 

# Get current script directory
$CurrentPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
Write-Host "Current folder: $CurrentPath" -ForegroundColor Yellow

try {
    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    if (-not $isAdmin) {
        Write-Host "Warning: Not running as Administrator. Some operations may fail." -ForegroundColor Yellow
        Write-Host "For best results, right-click and 'Run as Administrator'" -ForegroundColor Yellow
        Write-Host ""
    }
    
    # Add folder exclusion
    Write-Host "Adding folder exclusion..." -ForegroundColor Cyan
    Add-MpPreference -ExclusionPath $CurrentPath
    Write-Host "✓ Folder exclusion added successfully!" -ForegroundColor Green
    
    # Add process exclusions for Python and batch files
    Write-Host "Adding process exclusions..." -ForegroundColor Cyan
    
    $pythonExe = (Get-Command python -ErrorAction SilentlyContinue).Source
    if ($pythonExe) {
        Add-MpPreference -ExclusionProcess $pythonExe
        Write-Host "✓ Python executable excluded: $pythonExe" -ForegroundColor Green
    }
    
    # Add file type exclusions
    Write-Host "Adding file type exclusions..." -ForegroundColor Cyan
    Add-MpPreference -ExclusionExtension ".py"
    Add-MpPreference -ExclusionExtension ".bat"
    Add-MpPreference -ExclusionExtension ".ps1"
    Write-Host "✓ File extensions excluded: .py, .bat, .ps1" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "🎉 Windows Security exclusions added successfully!" -ForegroundColor Green
    Write-Host "Your BMD EMAIL SCRAPPER should now run without antivirus interference." -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "❌ Error adding exclusions: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual steps to add exclusions:" -ForegroundColor Yellow
    Write-Host "1. Open Windows Security (Windows Defender)" -ForegroundColor White
    Write-Host "2. Go to Virus & threat protection" -ForegroundColor White
    Write-Host "3. Click 'Manage settings' under Virus & threat protection settings" -ForegroundColor White
    Write-Host "4. Scroll down to 'Exclusions' and click 'Add or remove exclusions'" -ForegroundColor White
    Write-Host "5. Click 'Add an exclusion' and select 'Folder'" -ForegroundColor White
    Write-Host "6. Browse to and select: $CurrentPath" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")