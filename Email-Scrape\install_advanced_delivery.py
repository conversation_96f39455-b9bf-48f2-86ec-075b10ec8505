#!/usr/bin/env python3
"""
Installation and Setup Script for Advanced Email Delivery System
This script installs dependencies and sets up the advanced email delivery system
"""

import os
import subprocess
import sys
from pathlib import Path
from loguru import logger

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    logger.info(f"Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies"""
    logger.info("Installing dependencies...")
    
    try:
        # Install from requirements.txt
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        logger.success("Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install dependencies: {e}")
        return False

def verify_smtp_config():
    """Verify SMTP configuration exists"""
    smtp_config_file = Path("smtp_config.json")
    
    if smtp_config_file.exists():
        logger.info("SMTP configuration found")
        return True
    else:
        logger.warning("SMTP configuration not found")
        logger.info("Run setup_mailhop_smtp.py to configure SMTP")
        return False

def create_sample_files():
    """Create sample files for testing"""
    logger.info("Creating sample files for testing...")
    
    # Create sample PDF
    sample_pdf = Path("sample_document.pdf")
    if not sample_pdf.exists():
        with open(sample_pdf, "wb") as f:
            # Minimal PDF content
            pdf_content = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Sample Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF"""
            f.write(pdf_content)
        logger.info("Created sample_document.pdf")
    
    # Create sample image
    sample_image = Path("sample_image.jpg")
    if not sample_image.exists():
        with open(sample_image, "wb") as f:
            # Minimal JPEG header
            jpeg_content = b"\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.' \",#\x1c\x1c(7),01444\x1f'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9"
            f.write(jpeg_content)
        logger.info("Created sample_image.jpg")
    
    # Create sample Excel file
    sample_excel = Path("sample_spreadsheet.xlsx")
    if not sample_excel.exists():
        try:
            import openpyxl
            wb = openpyxl.Workbook()
            ws = wb.active
            ws['A1'] = "Sample Data"
            ws['B1'] = "Value"
            ws['A2'] = "Item 1"
            ws['B2'] = 100
            wb.save(sample_excel)
            logger.info("Created sample_spreadsheet.xlsx")
        except ImportError:
            # Create minimal XLSX structure
            with open(sample_excel, "wb") as f:
                # Minimal ZIP/XLSX header
                xlsx_content = b"PK\x03\x04\x14\x00\x00\x00\x08\x00\x00\x00!\x00"
                f.write(xlsx_content)
            logger.info("Created basic sample_spreadsheet.xlsx")

def test_advanced_delivery():
    """Test the advanced delivery system"""
    logger.info("Testing advanced delivery system...")
    
    try:
        # Import and test basic functionality
        from advanced_email_delivery import AdvancedEmailDelivery, ObfuscationConfig
        
        # Initialize system
        delivery_system = AdvancedEmailDelivery()
        
        # Test obfuscation
        obf_config = ObfuscationConfig()
        obfuscated_text = delivery_system.obfuscator.obfuscate_text("Test message")
        
        logger.info("Advanced delivery system initialized successfully")
        logger.info(f"Obfuscation test: '{obfuscated_text}'")
        
        return True
        
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Some dependencies may be missing")
        return False
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

def setup_logging():
    """Setup logging configuration"""
    log_file = Path("advanced_delivery_setup.log")
    
    logger.add(
        log_file,
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    
    logger.info("Logging configured")

def main():
    """Main installation function"""
    logger.info("Advanced Email Delivery System - Installation")
    logger.info("=" * 50)
    
    # Setup logging
    setup_logging()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        logger.error("Installation failed - dependency installation error")
        sys.exit(1)
    
    # Verify SMTP configuration
    smtp_configured = verify_smtp_config()
    
    # Create sample files
    create_sample_files()
    
    # Test the system
    if test_advanced_delivery():
        logger.success("✅ Advanced Email Delivery System installed successfully!")
        
        if smtp_configured:
            logger.info("✅ SMTP configuration found - system ready to use")
        else:
            logger.warning("⚠️  SMTP not configured - run setup_mailhop_smtp.py")
        
        logger.info("\nNext steps:")
        logger.info("1. Configure SMTP if not done: python setup_mailhop_smtp.py")
        logger.info("2. Run examples: python advanced_delivery_example.py")
        logger.info("3. Check documentation: ADVANCED_EMAIL_DELIVERY_README.md")
        
    else:
        logger.error("❌ Installation completed but system test failed")
        logger.info("Check the logs and try installing missing dependencies manually")
        sys.exit(1)

if __name__ == "__main__":
    main()
