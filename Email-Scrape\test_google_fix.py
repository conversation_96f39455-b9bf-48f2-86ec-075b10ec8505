#!/usr/bin/env python3
"""
Quick test to verify Google search scraping fix
"""

import asyncio
from email_scraper import WebScraper

async def test_google_search():
    """Test the updated Google search functionality"""
    scraper = WebScraper()
    
    try:
        print("Testing Google search scraping...")
        contacts = await scraper.google_search_scrape("contact email software company", max_pages=1)
        
        print(f"Successfully scraped {len(contacts)} contacts")
        
        if contacts:
            print("\nFirst few contacts found:")
            for i, contact in enumerate(contacts[:3]):
                print(f"{i+1}. Email: {contact.email}")
                print(f"   Website: {contact.website}")
                print(f"   Source: {contact.source}")
                print()
        else:
            print("No contacts found, but scraping completed without timeout errors.")
            
    except Exception as e:
        print(f"Test failed: {e}")
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(test_google_search())