#!/usr/bin/env python3
"""
BMD EMAIL SCRAPPER - Force GUI Launcher
This script forces the GUI to appear with maximum compatibility
"""

import sys
import os
import time
import traceback

def force_gui_launch():
    """Force launch the GUI with maximum visibility"""
    print("=" * 50)
    print("BMD EMAIL SCRAPPER - Force GUI Launcher")
    print("=" * 50)
    print()
    
    # Add current directory to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    print(f"Working directory: {current_dir}")
    print(f"Python version: {sys.version}")
    print()
    
    try:
        print("Step 1: Importing tkinter...")
        import tkinter as tk
        from tkinter import messagebox
        print("✓ tkinter imported successfully")
        
        print("Step 2: Creating root window...")
        root = tk.Tk()
        root.withdraw()  # Hide initially
        print("✓ Root window created")
        
        # Force window to appear
        print("Step 3: Configuring window visibility...")
        root.deiconify()
        root.lift()
        root.focus_force()
        root.attributes('-topmost', True)
        
        # Show a test window first
        print("Step 4: Showing test window...")
        test_window = tk.Toplevel(root)
        test_window.title("BMD EMAIL SCRAPPER - Test Window")
        test_window.geometry("400x300")
        test_window.configure(bg="#000000")
        test_window.lift()
        test_window.focus_force()
        test_window.attributes('-topmost', True)
        
        label = tk.Label(test_window, 
                        text="🚀 BMD EMAIL SCRAPPER\n\nTest Window Active!\n\nIf you can see this, GUI is working.\n\nClick OK to continue to main app.",
                        bg="#000000", fg="#00FF00", 
                        font=("Arial", 12, "bold"),
                        justify="center")
        label.pack(expand=True, pady=20)
        
        def continue_to_main():
            try:
                test_window.destroy()
                root.destroy()
            except:
                pass
            launch_main_app()
        
        ok_button = tk.Button(test_window, text="OK - Continue to Main App", 
                              command=continue_to_main,
                              bg='green', fg='white', font=('Arial', 12, 'bold'))
        ok_button.pack(pady=20)
        
        print("✓ Test window displayed")
        print("\n" + "=" * 50)
        print("IMPORTANT: Look for the test window on your screen!")
        print("It should be a black window with green text.")
        print("Click the OK button to continue to the main application.")
        print("=" * 50)
        
        root.mainloop()
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        print("\nFull traceback:")
        traceback.print_exc()
        print("\nPress Enter to exit...")
        input()
        return False
    
    return True

def launch_main_app():
    """Launch the main BMD EMAIL SCRAPPER application"""
    print("\n" + "="*50)
    print("Launching BMD EMAIL SCRAPPER...")
    print("="*50)
    
    try:
        print("Step 5: Importing bulk_gui module...")
        import bulk_gui
        print("✓ bulk_gui imported successfully")
        
        print("Step 6: Starting main application...")
        bulk_gui.main()
        print("✓ Application launched successfully")
        
    except Exception as e:
        print(f"✗ Error launching main application: {e}")
        print("\nTrying alternative launch method...")
        try:
            import subprocess
            subprocess.run([sys.executable, "bulk_gui.py"], check=True)
            print("✓ Alternative launch successful")
        except Exception as e2:
            print(f"✗ Alternative launch failed: {e2}")
            print("\nPlease try running 'python bulk_gui.py' manually.")
            input("Press Enter to exit...")

if __name__ == "__main__":
    force_gui_launch()