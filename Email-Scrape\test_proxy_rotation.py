#!/usr/bin/env python3
"""
Test script for proxy rotation functionality
"""

import asyncio
import sys
from email_scraper import WebScraper
from config import get_config

async def test_proxy_rotation():
    """Test the proxy rotation functionality"""
    print("Testing Email Scraper Pro with Proxy Rotation")
    print("=" * 50)
    
    # Get config and check for proxies
    config = get_config()
    
    if not config.proxies:
        print("No proxies configured. Adding sample proxy configuration...")
        # Add some sample proxy configurations (these are example proxies - replace with real ones)
        config.add_proxy("proxy1.example.com", 8080, protocol="http")
        config.add_proxy("proxy2.example.com", 3128, protocol="http")
        config.save_config()
        print(f"Added {len(config.proxies)} sample proxies to configuration")
        print("Note: Replace these with real proxy servers for actual use")
    else:
        print(f"Found {len(config.proxies)} configured proxies")
        for i, proxy in enumerate(config.proxies, 1):
            print(f"  {i}. {proxy}")
    
    print("\nTesting proxy rotation with web scraping...")
    
    scraper = WebScraper()
    
    try:
        # Test with a simple query
        query = "contact email site:example.com"
        print(f"\nSearching for: {query}")
        
        contacts = await scraper.google_search_scrape(query, max_pages=1)
        
        print(f"\nResults:")
        print(f"Found {len(contacts)} contacts")
        
        for i, contact in enumerate(contacts[:5], 1):  # Show first 5
            print(f"  {i}. {contact.email} - {contact.source}")
        
        if len(contacts) > 5:
            print(f"  ... and {len(contacts) - 5} more")
            
        # Test proxy rotation status
        print(f"\nProxy Status:")
        print(f"Current proxy: {scraper.current_proxy or 'Direct connection'}")
        print(f"Failed proxies: {len(scraper.failed_proxies)}")
        print(f"Proxy rotations: {scraper.proxy_rotation_count}")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await scraper.close_browser()
        print("\nTest completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_proxy_rotation())
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed: {e}")
        sys.exit(1)