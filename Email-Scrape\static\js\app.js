// Main JavaScript file for Email Scraper Pro

// Initialize Socket.IO connection
const socket = io();

// Global variables
let isConnected = false;
let notifications = [];

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupSocketListeners();
    setupGlobalEventListeners();
});

function initializeApp() {
    console.log('Email Scraper Pro - Initializing...');
    
    // Show loading state
    showStatusMessage('Connecting to server...', 'info');
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Set active navigation item
    setActiveNavItem();
}

function setupSocketListeners() {
    // Connection events
    socket.on('connect', function() {
        console.log('Connected to server');
        isConnected = true;
        hideStatusMessage();
        showNotification('Connected to Email Scraper Pro', 'success');
        updateConnectionIndicator(true);
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        isConnected = false;
        showStatusMessage('Connection lost. Attempting to reconnect...', 'warning');
        updateConnectionIndicator(false);
    });
    
    socket.on('connect_error', function(error) {
        console.error('Connection error:', error);
        showStatusMessage('Failed to connect to server', 'danger');
        updateConnectionIndicator(false);
    });
    
    // Application events
    socket.on('status', function(data) {
        console.log('Status update:', data);
        if (data.message) {
            showNotification(data.message, 'info');
        }
    });
    
    socket.on('notification', function(data) {
        showNotification(data.message, data.type || 'info');
    });
    
    socket.on('error', function(data) {
        console.error('Server error:', data);
        showNotification(data.message || 'An error occurred', 'danger');
    });
}

function setupGlobalEventListeners() {
    // Handle form submissions with loading states
    document.addEventListener('submit', function(e) {
        const form = e.target;
        if (form.classList.contains('ajax-form')) {
            e.preventDefault();
            handleAjaxForm(form);
        }
    });
    
    // Handle copy buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('copy-btn') || e.target.closest('.copy-btn')) {
            const btn = e.target.classList.contains('copy-btn') ? e.target : e.target.closest('.copy-btn');
            const text = btn.dataset.copy || btn.textContent;
            copyToClipboard(text);
        }
    });
    
    // Handle refresh buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('refresh-btn') || e.target.closest('.refresh-btn')) {
            const btn = e.target.classList.contains('refresh-btn') ? e.target : e.target.closest('.refresh-btn');
            const target = btn.dataset.refresh;
            if (target && window[target]) {
                window[target]();
            }
        }
    });
}

function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

function showStatusMessage(message, type = 'info') {
    const statusBar = document.getElementById('status-bar');
    const statusMessage = document.getElementById('status-message');
    
    if (statusBar && statusMessage) {
        statusBar.className = `alert alert-${type} d-flex align-items-center`;
        statusMessage.textContent = message;
        statusBar.classList.remove('d-none');
    }
}

function hideStatusMessage() {
    const statusBar = document.getElementById('status-bar');
    if (statusBar) {
        statusBar.classList.add('d-none');
    }
}

function showNotification(message, type = 'info', duration = 5000) {
    const notification = {
        id: Date.now(),
        message: message,
        type: type,
        timestamp: new Date()
    };
    
    notifications.push(notification);
    
    // Create notification element
    const notificationEl = createNotificationElement(notification);
    
    // Add to page
    const container = getNotificationContainer();
    container.appendChild(notificationEl);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification.id);
        }, duration);
    }
    
    // Limit number of notifications
    if (notifications.length > 5) {
        const oldestId = notifications[0].id;
        removeNotification(oldestId);
    }
}

function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `alert alert-${notification.type} alert-dismissible fade show notification-item`;
    div.dataset.notificationId = notification.id;
    div.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${getNotificationIcon(notification.type)} me-2"></i>
            <span>${notification.message}</span>
        </div>
        <button type="button" class="btn-close" onclick="removeNotification(${notification.id})"></button>
    `;
    
    // Add animation
    div.style.animation = 'slideIn 0.3s ease-out';
    
    return div;
}

function getNotificationContainer() {
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
    }
    return container;
}

function removeNotification(id) {
    const element = document.querySelector(`[data-notification-id="${id}"]`);
    if (element) {
        element.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            element.remove();
        }, 300);
    }
    
    notifications = notifications.filter(n => n.id !== id);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle',
        'primary': 'bell'
    };
    return icons[type] || 'bell';
}

function updateConnectionIndicator(connected) {
    const indicators = document.querySelectorAll('.connection-indicator');
    indicators.forEach(indicator => {
        if (connected) {
            indicator.className = 'connection-indicator bg-success';
            indicator.title = 'Connected';
        } else {
            indicator.className = 'connection-indicator bg-danger';
            indicator.title = 'Disconnected';
        }
    });
}

function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Copied to clipboard!', 'success', 2000);
        }).catch(err => {
            console.error('Failed to copy:', err);
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showNotification('Copied to clipboard!', 'success', 2000);
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showNotification('Failed to copy to clipboard', 'danger');
    }
    
    document.body.removeChild(textArea);
}

function handleAjaxForm(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
    
    fetch(form.action, {
        method: form.method || 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Operation completed successfully', 'success');
            if (data.redirect) {
                window.location.href = data.redirect;
            } else if (data.reload) {
                window.location.reload();
            }
        } else {
            throw new Error(data.message || 'Operation failed');
        }
    })
    .catch(error => {
        console.error('Form submission error:', error);
        showNotification(error.message || 'An error occurred', 'danger');
    })
    .finally(() => {
        // Restore button state
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
}

function formatDate(date) {
    if (!date) return 'N/A';
    
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
}

function formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return num.toLocaleString();
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Export functions for global use
window.EmailScraperPro = {
    showNotification,
    hideStatusMessage,
    showStatusMessage,
    copyToClipboard,
    formatDate,
    formatNumber,
    formatBytes,
    debounce,
    throttle,
    socket,
    isConnected: () => isConnected
};

// Add custom CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .notification-item {
        min-width: 300px;
        max-width: 400px;
        margin-bottom: 0.5rem;
    }
    
    .connection-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
`;
document.head.appendChild(style);

console.log('Email Scraper Pro - JavaScript loaded successfully');