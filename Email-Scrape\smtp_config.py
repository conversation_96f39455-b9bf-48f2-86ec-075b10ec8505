#!/usr/bin/env python3
"""
SMTP Configuration Module
Handles SMTP server configuration and email sending functionality
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Optional, Union
import json
import os
from dataclasses import dataclass, asdict
from loguru import logger
import time
from threading import Lock

@dataclass
class SMTPConfig:
    """SMTP server configuration"""
    provider: str
    smtp_server: str
    smtp_port: int
    username: str
    password: str
    use_tls: bool = True
    use_ssl: bool = False
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0
    
    def to_dict(self) -> Dict:
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SMTPConfig':
        return cls(**data)

class SMTPManager:
    """Manages SMTP configurations and email sending"""
    
    # Predefined SMTP configurations for popular providers
    PROVIDERS = {
        'gmail': {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'use_tls': True,
            'use_ssl': False
        },
        'outlook': {
            'smtp_server': 'smtp-mail.outlook.com',
            'smtp_port': 587,
            'use_tls': True,
            'use_ssl': False
        },
        'yahoo': {
            'smtp_server': 'smtp.mail.yahoo.com',
            'smtp_port': 587,
            'use_tls': True,
            'use_ssl': False
        },
        'office365': {
            'smtp_server': 'smtp.office365.com',
            'smtp_port': 587,
            'use_tls': True,
            'use_ssl': False
        },
        'mailhop': {
            'smtp_server': 'outbound.mailhop.org',
            'smtp_port': 465,
            'use_tls': False,
            'use_ssl': True
        },
        'custom': {
            'smtp_server': '',
            'smtp_port': 587,
            'use_tls': True,
            'use_ssl': False
        }
    }
    
    def __init__(self, config_file: str = 'smtp_config.json'):
        self.config_file = config_file
        self.configs: Dict[str, SMTPConfig] = {}
        self.active_config: Optional[str] = None
        self._lock = Lock()
        self.load_configs()
    
    def load_configs(self) -> None:
        """Load SMTP configurations from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for name, config_data in data.get('smtp_configs', {}).items():
                    self.configs[name] = SMTPConfig.from_dict(config_data)
                    
                self.active_config = data.get('active_config')
                logger.info(f"Loaded {len(self.configs)} SMTP configurations")
            else:
                logger.info("No SMTP config file found, starting with empty configuration")
        except Exception as e:
            logger.error(f"Error loading SMTP configs: {e}")
    
    def save_configs(self) -> None:
        """Save SMTP configurations to file"""
        try:
            data = {
                'smtp_configs': {name: config.to_dict() for name, config in self.configs.items()},
                'active_config': self.active_config
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            logger.info(f"Saved {len(self.configs)} SMTP configurations")
        except Exception as e:
            logger.error(f"Error saving SMTP configs: {e}")
    
    def add_config(self, name: str, provider: str, username: str, password: str, 
                   custom_server: str = None, custom_port: int = None) -> bool:
        """Add a new SMTP configuration"""
        try:
            if provider not in self.PROVIDERS:
                raise ValueError(f"Unsupported provider: {provider}")
            
            provider_config = self.PROVIDERS[provider].copy()
            
            # Override with custom settings if provided
            if custom_server:
                provider_config['smtp_server'] = custom_server
            if custom_port:
                provider_config['smtp_port'] = custom_port
            
            config = SMTPConfig(
                provider=provider,
                username=username,
                password=password,
                **provider_config
            )
            
            self.configs[name] = config
            
            # Set as active if it's the first config
            if not self.active_config:
                self.active_config = name
            
            self.save_configs()
            logger.info(f"Added SMTP config '{name}' for provider '{provider}'")
            return True
            
        except Exception as e:
            logger.error(f"Error adding SMTP config: {e}")
            return False
    
    def remove_config(self, name: str) -> bool:
        """Remove an SMTP configuration"""
        try:
            if name in self.configs:
                del self.configs[name]
                
                # Update active config if removed
                if self.active_config == name:
                    self.active_config = next(iter(self.configs.keys()), None)
                
                self.save_configs()
                logger.info(f"Removed SMTP config '{name}'")
                return True
            else:
                logger.warning(f"SMTP config '{name}' not found")
                return False
                
        except Exception as e:
            logger.error(f"Error removing SMTP config: {e}")
            return False
    
    def set_active_config(self, name: str) -> bool:
        """Set the active SMTP configuration"""
        if name in self.configs:
            self.active_config = name
            self.save_configs()
            logger.info(f"Set active SMTP config to '{name}'")
            return True
        else:
            logger.error(f"SMTP config '{name}' not found")
            return False
    
    def get_active_config(self) -> Optional[SMTPConfig]:
        """Get the active SMTP configuration"""
        if self.active_config and self.active_config in self.configs:
            return self.configs[self.active_config]
        return None
    
    def test_connection(self, config_name: str = None) -> bool:
        """Test SMTP connection"""
        try:
            config = self.configs.get(config_name) if config_name else self.get_active_config()
            if not config:
                logger.error("No SMTP configuration available")
                return False
            
            logger.info(f"Testing SMTP connection to {config.smtp_server}:{config.smtp_port}")
            
            # Create SMTP connection
            if config.use_ssl:
                server = smtplib.SMTP_SSL(config.smtp_server, config.smtp_port, timeout=config.timeout)
            else:
                server = smtplib.SMTP(config.smtp_server, config.smtp_port, timeout=config.timeout)
                if config.use_tls:
                    server.starttls(context=ssl.create_default_context())
            
            # Login
            server.login(config.username, config.password)
            server.quit()
            
            logger.info("SMTP connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"SMTP connection test failed: {e}")
            return False
    
    def send_email(self, to_emails: Union[str, List[str]], subject: str, 
                   body: str, html_body: str = None, attachments: List[str] = None,
                   config_name: str = None) -> bool:
        """Send email using configured SMTP"""
        try:
            config = self.configs.get(config_name) if config_name else self.get_active_config()
            if not config:
                logger.error("No SMTP configuration available")
                return False
            
            # Ensure to_emails is a list
            if isinstance(to_emails, str):
                to_emails = [to_emails]
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = config.username
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            # Add text body
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # Add HTML body if provided
            if html_body:
                msg.attach(MIMEText(html_body, 'html', 'utf-8'))
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        msg.attach(part)
            
            # Send email with retry logic
            for attempt in range(config.max_retries):
                try:
                    with self._lock:
                        # Create SMTP connection
                        if config.use_ssl:
                            server = smtplib.SMTP_SSL(config.smtp_server, config.smtp_port, timeout=config.timeout)
                        else:
                            server = smtplib.SMTP(config.smtp_server, config.smtp_port, timeout=config.timeout)
                            if config.use_tls:
                                server.starttls(context=ssl.create_default_context())
                        
                        # Login and send
                        server.login(config.username, config.password)
                        server.send_message(msg)
                        server.quit()
                    
                    logger.info(f"Email sent successfully to {len(to_emails)} recipients")
                    return True
                    
                except Exception as e:
                    logger.warning(f"Email send attempt {attempt + 1} failed: {e}")
                    if attempt < config.max_retries - 1:
                        time.sleep(config.retry_delay)
                    else:
                        raise e
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def get_provider_list(self) -> List[str]:
        """Get list of supported providers"""
        return list(self.PROVIDERS.keys())
    
    def get_config_list(self) -> List[str]:
        """Get list of configured SMTP accounts"""
        return list(self.configs.keys())
    
    def get_config_info(self, name: str) -> Optional[Dict]:
        """Get configuration information (without password)"""
        if name in self.configs:
            config = self.configs[name]
            info = config.to_dict()
            info['password'] = '***masked***'
            return info
        return None

# Example usage and testing
if __name__ == "__main__":
    # Initialize SMTP manager
    smtp_manager = SMTPManager()
    
    # Example: Add Gmail configuration
    # smtp_manager.add_config(
    #     name="my_gmail",
    #     provider="gmail",
    #     username="<EMAIL>",
    #     password="your_app_password"
    # )
    
    # Test connection
    # if smtp_manager.test_connection():
    #     print("SMTP connection successful!")
    # else:
    #     print("SMTP connection failed!")
    
    print("SMTP Configuration Module loaded successfully")
    print(f"Supported providers: {smtp_manager.get_provider_list()}")