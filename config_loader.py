import json
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class RateLimitConfig:
    """Configuration for email rate limiting"""
    emails_per_minute: int = 30
    emails_per_hour: int = 500
    emails_per_day: int = 2000
    delay_between_emails: float = 2.0
    batch_size: int = 10
    batch_delay: float = 30.0

@dataclass
class RetryConfig:
    """Configuration for email retry settings"""
    max_retries: int = 3
    retry_delay: float = 5.0
    exponential_backoff: bool = True
    backoff_multiplier: float = 2.0

@dataclass
class TrackingConfig:
    """Configuration for email tracking"""
    enabled: bool = True
    track_opens: bool = True
    track_clicks: bool = True
    track_bounces: bool = True
    tracking_domain: str = "localhost"

@dataclass
class ValidationConfig:
    """Configuration for email validation"""
    validate_emails: bool = True
    check_mx_records: bool = True
    check_smtp_connection: bool = False
    bounce_handling: bool = True
    max_bounce_rate: float = 0.05

@dataclass
class TemplateConfig:
    """Configuration for email templates"""
    default_template: str = "welcome"
    template_directory: str = "templates"
    auto_personalization: bool = True
    fallback_values: Dict[str, str] = None
    
    def __post_init__(self):
        if self.fallback_values is None:
            self.fallback_values = {
                "name": "Valued Customer",
                "company": "Your Company"
            }

@dataclass
class CampaignConfig:
    """Configuration for email campaigns"""
    max_concurrent_campaigns: int = 3
    auto_pause_on_high_bounce: bool = True
    bounce_threshold: float = 0.1
    campaign_data_retention: int = 90
    default_from_name: str = "Your Company"
    default_reply_to: Optional[str] = None

@dataclass
class MassEmailConfig:
    """Complete mass email configuration"""
    default_smtp_config: str = "gmail"
    rate_limiting: RateLimitConfig = None
    retry_settings: RetryConfig = None
    tracking: TrackingConfig = None
    validation: ValidationConfig = None
    templates: TemplateConfig = None
    campaigns: CampaignConfig = None
    
    def __post_init__(self):
        if self.rate_limiting is None:
            self.rate_limiting = RateLimitConfig()
        if self.retry_settings is None:
            self.retry_settings = RetryConfig()
        if self.tracking is None:
            self.tracking = TrackingConfig()
        if self.validation is None:
            self.validation = ValidationConfig()
        if self.templates is None:
            self.templates = TemplateConfig()
        if self.campaigns is None:
            self.campaigns = CampaignConfig()

class ConfigLoader:
    """Loads and manages application configuration"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config_path = Path(config_path)
        self._config_data = None
        self._mass_email_config = None
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
            else:
                self._config_data = {}
                print(f"Warning: Config file {self.config_path} not found. Using defaults.")
        except Exception as e:
            print(f"Error loading config: {e}. Using defaults.")
            self._config_data = {}
        
        return self._config_data
    
    def get_config(self, section: str = None) -> Dict[str, Any]:
        """Get configuration section or entire config"""
        if section:
            return self._config_data.get(section, {})
        return self._config_data
    
    def get_mass_email_config(self) -> MassEmailConfig:
        """Get mass email configuration as structured object"""
        if self._mass_email_config is None:
            mass_email_data = self._config_data.get('mass_email', {})
            
            # Create structured config objects
            rate_limiting_data = mass_email_data.get('rate_limiting', {})
            rate_limiting = RateLimitConfig(**rate_limiting_data)
            
            retry_data = mass_email_data.get('retry_settings', {})
            retry_settings = RetryConfig(**retry_data)
            
            tracking_data = mass_email_data.get('tracking', {})
            tracking = TrackingConfig(**tracking_data)
            
            validation_data = mass_email_data.get('validation', {})
            validation = ValidationConfig(**validation_data)
            
            template_data = mass_email_data.get('templates', {})
            templates = TemplateConfig(**template_data)
            
            campaign_data = mass_email_data.get('campaigns', {})
            campaigns = CampaignConfig(**campaign_data)
            
            self._mass_email_config = MassEmailConfig(
                default_smtp_config=mass_email_data.get('default_smtp_config', 'gmail'),
                rate_limiting=rate_limiting,
                retry_settings=retry_settings,
                tracking=tracking,
                validation=validation,
                templates=templates,
                campaigns=campaigns
            )
        
        return self._mass_email_config
    
    def save_config(self, config_data: Dict[str, Any] = None) -> bool:
        """Save configuration to JSON file"""
        try:
            data_to_save = config_data if config_data is not None else self._config_data
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data_to_save, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def update_mass_email_config(self, **kwargs) -> bool:
        """Update mass email configuration"""
        if 'mass_email' not in self._config_data:
            self._config_data['mass_email'] = {}
        
        self._config_data['mass_email'].update(kwargs)
        self._mass_email_config = None  # Reset cached config
        return self.save_config()
    
    def get_smtp_config_path(self) -> str:
        """Get path to SMTP configuration file"""
        return str(self.config_path.parent / "smtp_config.json")
    
    def get_templates_path(self) -> str:
        """Get path to email templates file"""
        return str(self.config_path.parent / "email_templates.json")
    
    def get_campaigns_path(self) -> str:
        """Get path to campaigns data directory"""
        return str(self.config_path.parent / "campaigns")

# Global config loader instance
config_loader = ConfigLoader()

# Convenience functions
def get_config(section: str = None) -> Dict[str, Any]:
    """Get configuration section"""
    return config_loader.get_config(section)

def get_mass_email_config() -> MassEmailConfig:
    """Get mass email configuration"""
    return config_loader.get_mass_email_config()

def reload_config() -> Dict[str, Any]:
    """Reload configuration from file"""
    return config_loader.load_config()