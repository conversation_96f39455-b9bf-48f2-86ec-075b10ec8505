#!/usr/bin/env python3
"""
Setup script to configure MailHop SMTP server
This script adds the MailHop SMTP configuration to the system
"""

import sys
import os
from loguru import logger

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from smtp_config import SMTPManager

def setup_mailhop_smtp():
    """Setup MailHop SMTP configuration"""
    try:
        # Initialize SMTP manager
        smtp_manager = SMTPManager()
        
        # MailHop SMTP configuration
        config_name = "mailhop_carlos"
        provider = "mailhop"
        username = "Carlosfenandezlawfirm"
        password = "Americana123456789@"
        
        logger.info("Setting up MailHop SMTP configuration...")
        
        # Add the configuration
        success = smtp_manager.add_config(
            name=config_name,
            provider=provider,
            username=username,
            password=password
        )
        
        if success:
            logger.info(f"Successfully added MailHop SMTP configuration: {config_name}")
            
            # Set as active configuration
            smtp_manager.set_active_config(config_name)
            logger.info(f"Set {config_name} as active SMTP configuration")
            
            # Test the connection
            logger.info("Testing SMTP connection...")
            if smtp_manager.test_connection():
                logger.success("✅ SMTP connection test successful!")
                logger.info("MailHop SMTP is ready to use")
            else:
                logger.error("❌ SMTP connection test failed")
                return False
                
        else:
            logger.error("Failed to add MailHop SMTP configuration")
            return False
            
        return True
        
    except Exception as e:
        logger.error(f"Error setting up MailHop SMTP: {e}")
        return False

def show_current_config():
    """Display current SMTP configuration"""
    try:
        smtp_manager = SMTPManager()
        
        logger.info("Current SMTP Configurations:")
        configs = smtp_manager.list_configs()
        
        if not configs:
            logger.info("No SMTP configurations found")
            return
            
        for config_name in configs:
            config = smtp_manager.get_config(config_name)
            if config:
                active_marker = " (ACTIVE)" if config_name == smtp_manager.active_config else ""
                logger.info(f"  - {config_name}: {config.provider} - {config.smtp_server}:{config.smtp_port}{active_marker}")
                
    except Exception as e:
        logger.error(f"Error displaying configurations: {e}")

if __name__ == "__main__":
    logger.info("MailHop SMTP Setup Script")
    logger.info("=" * 50)
    
    # Show current configurations
    show_current_config()
    
    # Setup MailHop SMTP
    if setup_mailhop_smtp():
        logger.success("MailHop SMTP setup completed successfully!")
        
        # Show updated configurations
        logger.info("\nUpdated SMTP Configurations:")
        show_current_config()
        
    else:
        logger.error("MailHop SMTP setup failed!")
        sys.exit(1)
