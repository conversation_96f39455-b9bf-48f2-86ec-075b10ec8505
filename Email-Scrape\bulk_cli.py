#!/usr/bin/env python3
"""
Bulk Email Scraper CLI
Command-line interface for bulk scraping and email generation
"""

import asyncio
import argparse
import json
import csv
import sys
from pathlib import Path
from typing import List, Dict
from loguru import logger
from bulk_scraper import BulkScraper
from email_generator import EmailGenerator
from config import get_config

class BulkCLI:
    def __init__(self):
        self.scraper = BulkScraper()
        self.generator = EmailGenerator()
        
    def setup_logging(self, debug: bool = False):
        """Setup logging configuration"""
        log_level = "DEBUG" if debug else "INFO"
        logger.remove()
        logger.add(sys.stderr, level=log_level, format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}")
        logger.add("bulk_scraper.log", level="DEBUG", rotation="10 MB")
    
    async def bulk_scrape_command(self, args):
        """Execute bulk scraping command"""
        logger.info(f"Starting bulk scrape for query: '{args.query}'")
        
        results = await self.scraper.bulk_scrape_all_platforms(
            query=args.query,
            location=args.location or "",
            max_results_per_platform=args.max_results
        )
        
        # Save results
        output_file = args.output or f"bulk_scrape_{args.query.replace(' ', '_')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Print summary
        total_results = sum(len(contacts) for contacts in results.values())
        print(f"\n✅ Bulk scraping completed!")
        print(f"📊 Total contacts found: {total_results}")
        print(f"💾 Results saved to: {output_file}")
        
        # Print platform breakdown
        print("\n📈 Platform Breakdown:")
        for platform, contacts in results.items():
            print(f"  {platform}: {len(contacts)} contacts")
        
        # Export to CSV if requested
        if args.csv:
            csv_file = output_file.replace('.json', '.csv')
            self.export_to_csv(results, csv_file)
            print(f"📄 CSV export saved to: {csv_file}")
    
    def generate_emails_command(self, args):
        """Execute email generation command"""
        names = args.names if isinstance(args.names, list) else [args.names]
        
        logger.info(f"Generating emails for names: {names}")
        
        if args.bulk_mode:
            # Generate for multiple regions
            regions = args.regions or list(self.generator.regional_domains.keys())
            results = self.generator.generate_bulk_emails(
                names=names,
                regions=regions,
                emails_per_name=args.count
            )
        else:
            # Generate for single region
            results = {}
            for name in names:
                emails = self.generator.generate_email_variations(
                    name=name,
                    region=args.region,
                    count=args.count,
                    include_business=args.include_business
                )
                results[name] = emails
        
        # Save results
        output_file = args.output or f"generated_emails_{'-'.join(names)}.json"
        self.generator.save_generated_emails(results, output_file)
        
        # Print summary
        total_emails = sum(len(emails) for emails in results.values())
        print(f"\n✅ Email generation completed!")
        print(f"📧 Total emails generated: {total_emails}")
        print(f"💾 Results saved to: {output_file}")
        
        # Print breakdown by name
        print("\n📈 Generation Breakdown:")
        for name, emails in results.items():
            print(f"  {name}: {len(emails)} emails")
            if args.preview:
                print(f"    Sample: {', '.join(emails[:5])}")
        
        # Export to CSV if requested
        if args.csv:
            csv_file = output_file.replace('.json', '.csv')
            self.generator.export_to_csv(results, csv_file)
            print(f"📄 CSV export saved to: {csv_file}")
    
    async def combined_scrape_command(self, args):
        """Execute combined scraping and email generation"""
        names = args.names if isinstance(args.names, list) else [args.names]
        
        logger.info(f"Starting combined scrape and email generation")
        logger.info(f"Query: '{args.query}', Names: {names}, Region: {args.region}")
        
        results = await self.scraper.scrape_with_generated_emails(
            query=args.query,
            names=names,
            region=args.region
        )
        
        # Save results
        output_file = args.output or f"combined_results_{args.query.replace(' ', '_')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # Print comprehensive summary
        print(f"\n✅ Combined scraping and generation completed!")
        print(f"🔍 Query: {results['query']}")
        print(f"🌍 Region: {results['region']}")
        print(f"📊 Scraped contacts: {results['total_scraped']}")
        print(f"📧 Generated emails: {results['total_generated']}")
        print(f"💾 Results saved to: {output_file}")
        
        # Platform breakdown
        print("\n📈 Scraped Platform Breakdown:")
        for platform, contacts in results['scraped_contacts'].items():
            print(f"  {platform}: {len(contacts)} contacts")
        
        # Generated emails breakdown
        print("\n📧 Generated Emails Breakdown:")
        for name, emails in results['generated_emails'].items():
            print(f"  {name}: {len(emails)} emails")
    
    def list_regions_command(self, args):
        """List available regions and their domains"""
        print("\n🌍 Available Regions and Domains:")
        print("=" * 50)
        
        for region, domains in self.generator.regional_domains.items():
            print(f"\n📍 {region.upper().replace('_', ' ')}:")
            print(f"   Domains: {len(domains)}")
            if args.verbose:
                for i, domain in enumerate(domains[:10], 1):
                    print(f"   {i:2d}. {domain}")
                if len(domains) > 10:
                    print(f"   ... and {len(domains) - 10} more")
            else:
                print(f"   Sample: {', '.join(domains[:5])}")
    
    def export_to_csv(self, results: Dict, filename: str):
        """Export scraping results to CSV"""
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Platform', 'Category', 'Name', 'Title', 'Company', 'Email', 'Phone', 'Address', 'Source_URL', 'Scraped_At'])
            
            for category, contacts in results.items():
                for contact in contacts:
                    writer.writerow([
                        contact.get('platform', ''),
                        category,
                        contact.get('name', ''),
                        contact.get('title', ''),
                        contact.get('company', ''),
                        contact.get('email', ''),
                        contact.get('phone', ''),
                        contact.get('address', ''),
                        contact.get('source_url', ''),
                        contact.get('scraped_at', '')
                    ])
    
    def demo_command(self, args):
        """Run demonstration of email generation"""
        print("\n🚀 Email Generator Demo")
        print("=" * 30)
        
        # Demo with sample name
        demo_name = args.name or "anna"
        demo_region = args.region or "global"
        
        print(f"\n📧 Generating emails for '{demo_name}' in region '{demo_region}'...")
        
        emails = self.generator.generate_email_variations(
            name=demo_name,
            region=demo_region,
            count=50,
            include_business=True
        )
        
        print(f"\n✅ Generated {len(emails)} email variations:")
        print("\n📋 Sample emails:")
        for i, email in enumerate(emails[:20], 1):
            print(f"  {i:2d}. {email}")
        
        if len(emails) > 20:
            print(f"  ... and {len(emails) - 20} more variations")
        
        # Show regional breakdown
        domains_used = set(email.split('@')[1] for email in emails)
        print(f"\n🌍 Domains used: {len(domains_used)}")
        for domain in sorted(domains_used)[:10]:
            count = sum(1 for email in emails if email.endswith(f'@{domain}'))
            print(f"  {domain}: {count} emails")

def main():
    parser = argparse.ArgumentParser(
        description="Bulk Email Scraper - Advanced email scraping and generation tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Bulk scrape multiple platforms
  python bulk_cli.py scrape "marketing agency" --location "New York" --max-results 50
  
  # Generate emails for multiple names
  python bulk_cli.py generate anna john sarah --region north_america --count 1000
  
  # Combined scraping and email generation
  python bulk_cli.py combined "tech startup" anna john --region global
  
  # List available regions
  python bulk_cli.py regions --verbose
  
  # Run demo
  python bulk_cli.py demo --name anna --region europe
"""
    )
    
    # Global arguments
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--csv', action='store_true', help='Export results to CSV')
    parser.add_argument('--output', '-o', help='Output file name')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Bulk scrape command
    scrape_parser = subparsers.add_parser('scrape', help='Bulk scrape multiple platforms')
    scrape_parser.add_argument('query', help='Search query/keyword')
    scrape_parser.add_argument('--location', '-l', help='Location filter (for business directories)')
    scrape_parser.add_argument('--max-results', '-m', type=int, default=100, help='Max results per platform')
    
    # Email generation command
    generate_parser = subparsers.add_parser('generate', help='Generate email variations')
    generate_parser.add_argument('names', nargs='+', help='Names to generate emails for')
    generate_parser.add_argument('--region', '-r', default='global', help='Geographic region')
    generate_parser.add_argument('--count', '-c', type=int, default=1000, help='Number of emails to generate')
    generate_parser.add_argument('--include-business', action='store_true', help='Include business domains')
    generate_parser.add_argument('--bulk-mode', action='store_true', help='Generate for multiple regions')
    generate_parser.add_argument('--regions', nargs='+', help='Specific regions for bulk mode')
    generate_parser.add_argument('--preview', action='store_true', help='Show email previews')
    
    # Combined command
    combined_parser = subparsers.add_parser('combined', help='Combined scraping and email generation')
    combined_parser.add_argument('query', help='Search query for scraping')
    combined_parser.add_argument('names', nargs='+', help='Names for email generation')
    combined_parser.add_argument('--region', '-r', default='global', help='Geographic region')
    
    # List regions command
    regions_parser = subparsers.add_parser('regions', help='List available regions')
    regions_parser.add_argument('--verbose', '-v', action='store_true', help='Show detailed domain lists')
    
    # Demo command
    demo_parser = subparsers.add_parser('demo', help='Run email generation demo')
    demo_parser.add_argument('--name', default='anna', help='Name for demo')
    demo_parser.add_argument('--region', default='global', help='Region for demo')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = BulkCLI()
    cli.setup_logging(args.debug)
    
    try:
        if args.command == 'scrape':
            asyncio.run(cli.bulk_scrape_command(args))
        elif args.command == 'generate':
            cli.generate_emails_command(args)
        elif args.command == 'combined':
            asyncio.run(cli.combined_scrape_command(args))
        elif args.command == 'regions':
            cli.list_regions_command(args)
        elif args.command == 'demo':
            cli.demo_command(args)
    except KeyboardInterrupt:
        print("\n⚠️  Operation cancelled by user")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        if args.debug:
            raise
        sys.exit(1)

if __name__ == "__main__":
    main()